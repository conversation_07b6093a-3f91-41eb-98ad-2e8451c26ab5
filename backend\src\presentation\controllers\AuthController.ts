/**
 * Professional Authentication Controller
 * Handles HTTP requests for authentication operations using clean architecture
 */

import { Request, Response, NextFunction } from 'express';
import { RegisterUserCommand } from '../../application/auth/commands/RegisterUserCommand';
import { LoginUserCommand } from '../../application/auth/commands/LoginUserCommand';
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON>, ApplicationError } from '../../shared/errors/ApplicationErrors';

export class AuthController {
  constructor(
    private registerUserCommand: RegisterUserCommand,
    private loginUserCommand: LoginUserCommand
  ) {}

  /**
   * Register a new user
   * POST /api/auth/register
   */
  register = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const result = await this.registerUserCommand.execute({
        email: req.body.email,
        password: req.body.password,
        confirmPassword: req.body.confirmPassword,
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        phone: req.body.phone,
        role: req.body.role,
        acceptTerms: req.body.acceptTerms,
        acceptPrivacy: req.body.acceptPrivacy,
        language: req.body.language || req.headers['accept-language']?.split(',')[0] || 'en',
        referralCode: req.body.referralCode
      });

      res.status(201).json({
        success: true,
        message: result.message,
        data: {
          user: result.user,
          verificationToken: result.verificationToken
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Login user
   * POST /api/auth/login
   */
  login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const result = await this.loginUserCommand.execute({
        email: req.body.email,
        password: req.body.password,
        twoFactorCode: req.body.twoFactorCode,
        deviceId: req.body.deviceId || req.headers['x-device-id'] as string,
        rememberMe: req.body.rememberMe,
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip || req.connection.remoteAddress
      });

      // Set secure HTTP-only cookie for refresh token
      if (result.tokens.refreshToken) {
        res.cookie('refreshToken', result.tokens.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict',
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
          path: '/api/auth'
        });
      }

      const responseData: any = {
        user: result.user,
        accessToken: result.tokens.accessToken,
        expiresIn: result.tokens.expiresIn
      };

      // Add 2FA specific data if required
      if (result.requiresTwoFactor) {
        responseData.requiresTwoFactor = true;
        responseData.tempToken = result.tempToken;
      }

      res.status(200).json({
        success: true,
        message: result.message,
        data: responseData,
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Logout user
   * POST /api/auth/logout
   */
  logout = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Clear refresh token cookie
      res.clearCookie('refreshToken', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        path: '/api/auth'
      });

      // TODO: Blacklist the access token
      // await this.jwtService.blacklistToken(req.headers.authorization?.split(' ')[1]);

      res.status(200).json({
        success: true,
        message: 'Logged out successfully',
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Refresh access token
   * POST /api/auth/refresh
   */
  refreshToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const refreshToken = req.cookies.refreshToken || req.body.refreshToken;
      
      if (!refreshToken) {
        throw new ApplicationError('Refresh token not provided', 401, 'REFRESH_TOKEN_MISSING');
      }

      // TODO: Implement refresh token logic
      // const result = await this.refreshTokenCommand.execute({ refreshToken });

      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          // accessToken: result.accessToken,
          // expiresIn: result.expiresIn
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Verify email
   * POST /api/auth/verify-email
   */
  verifyEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { token } = req.body;

      // TODO: Implement email verification logic
      // await this.verifyEmailCommand.execute({ token });

      res.status(200).json({
        success: true,
        message: 'Email verified successfully',
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Forgot password
   * POST /api/auth/forgot-password
   */
  forgotPassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { email } = req.body;

      // TODO: Implement forgot password logic
      // await this.forgotPasswordCommand.execute({ email });

      res.status(200).json({
        success: true,
        message: 'Password reset instructions sent to your email',
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Reset password
   * POST /api/auth/reset-password
   */
  resetPassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { token, password, confirmPassword } = req.body;

      // TODO: Implement reset password logic
      // await this.resetPasswordCommand.execute({ token, password, confirmPassword });

      res.status(200).json({
        success: true,
        message: 'Password reset successfully',
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Change password
   * POST /api/auth/change-password
   */
  changePassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { currentPassword, newPassword, confirmPassword } = req.body;
      const userId = req.user?.id; // From auth middleware

      // TODO: Implement change password logic
      // await this.changePasswordCommand.execute({ 
      //   userId, 
      //   currentPassword, 
      //   newPassword, 
      //   confirmPassword 
      // });

      res.status(200).json({
        success: true,
        message: 'Password changed successfully',
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Get current user profile
   * GET /api/auth/me
   */
  getCurrentUser = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id; // From auth middleware

      // TODO: Implement get current user logic
      // const user = await this.getUserQuery.execute({ userId });

      res.status(200).json({
        success: true,
        message: 'User profile retrieved successfully',
        data: {
          // user: user
        },
        meta: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] || 'unknown'
        }
      });
    } catch (error) {
      next(error);
    }
  };
}
