import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiTrendingUp, FiTrendingDown } from 'react-icons/fi';

import { 
  UserGroupIcon,
  HomeIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ArrowRightIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Button from '../../components/common/Button';
import { useLanguage } from '../../contexts/LanguageContext';

interface AdminStats {
  totalUsers: number;
  totalProperties: number;
  totalViews: number;
  totalRevenue: number;
  pendingApprovals: number;
  activeUsers: number;
  newUsersToday: number;
  newPropertiesToday: number;
  monthlyGrowth: {
    users: number;
    properties: number;
    revenue: number;
  };
  recentActivity: AdminActivity[];
}

interface AdminActivity {
  id: string;
  type: 'user_registered' | 'property_posted' | 'property_sold' | 'report_submitted';
  title: string;
  description: string;
  timestamp: string;
  status: 'pending' | 'completed' | 'requires_attention';
}

const AdminDashboard: React.FC = () => {
  const { t, isRTL } = useLanguage();
  
  // Mock data - in real app, this would come from API
  const stats: AdminStats = {
    totalUsers: 12450,
    totalProperties: 3280,
    totalViews: 156780,
    totalRevenue: 45600,
    pendingApprovals: 23,
    activeUsers: 1250,
    newUsersToday: 45,
    newPropertiesToday: 12,
    monthlyGrowth: {
      users: 12.5,
      properties: 8.3,
      revenue: 15.7
    },
    recentActivity: [
      {
        id: '1',
        type: 'user_registered',
        title: 'New user registration',
        description: 'Ahmad Khan registered as a new user',
        timestamp: '2024-01-20T10:30:00Z',
        status: 'completed'
      },
      {
        id: '2',
        type: 'property_posted',
        title: 'Property pending approval',
        description: 'Villa in Wazir Akbar Khan needs review',
        timestamp: '2024-01-20T09:15:00Z',
        status: 'pending'
      },
      {
        id: '3',
        type: 'report_submitted',
        title: 'Property report submitted',
        description: 'User reported suspicious listing',
        timestamp: '2024-01-20T08:45:00Z',
        status: 'requires_attention'
      }
    ]
  };
  
  const quickActions = [
    {
      title: t('admin.manageUsers'),
      description: t('admin.manageUsersDesc'),
      icon: UserGroupIcon,
      href: '/admin/users',
      color: 'bg-blue-600 hover:bg-blue-700',
      count: stats.pendingApprovals
    },
    {
      title: t('admin.manageProperties'),
      description: t('admin.managePropertiesDesc'),
      icon: HomeIcon,
      href: '/admin/properties',
      color: 'bg-green-600 hover:bg-green-700',
      count: stats.pendingApprovals
    },
    {
      title: t('admin.analytics'),
      description: t('admin.analyticsDesc'),
      icon: ChartBarIcon,
      href: '/admin/analytics',
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: t('admin.reports'),
      description: t('admin.reportsDesc'),
      icon: ExclamationTriangleIcon,
      href: '/admin/reports',
      color: 'bg-red-600 hover:bg-red-700',
      count: 5
    }
  ];
  
  const statCards = [
    {
      title: t('admin.totalUsers'),
      value: stats.totalUsers.toLocaleString(),
      icon: UserGroupIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: `+${stats.monthlyGrowth.users}%`,
      changeType: 'positive' as const,
      subtitle: `+${stats.newUsersToday} ${t('admin.today')}`
    },
    {
      title: t('admin.totalProperties'),
      value: stats.totalProperties.toLocaleString(),
      icon: HomeIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: `+${stats.monthlyGrowth.properties}%`,
      changeType: 'positive' as const,
      subtitle: `+${stats.newPropertiesToday} ${t('admin.today')}`
    },
    {
      title: t('admin.totalViews'),
      value: stats.totalViews.toLocaleString(),
      icon: EyeIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+23%',
      changeType: 'positive' as const,
      subtitle: t('admin.thisMonth')
    },
    {
      title: t('admin.revenue'),
      value: `$${stats.totalRevenue.toLocaleString()}`,
      icon: CurrencyDollarIcon,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      change: `+${stats.monthlyGrowth.revenue}%`,
      changeType: 'positive' as const,
      subtitle: t('admin.thisMonth')
    }
  ];
  
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registered':
        return <UserGroupIcon className="w-4 h-4" />;
      case 'property_posted':
        return <HomeIcon className="w-4 h-4" />;
      case 'property_sold':
        return <CurrencyDollarIcon className="w-4 h-4" />;
      case 'report_submitted':
        return <ExclamationTriangleIcon className="w-4 h-4" />;
      default:
        return <CalendarIcon className="w-4 h-4" />;
    }
  };
  
  const getActivityColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'requires_attention':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-4 h-4" />;
      case 'pending':
        return <ClockIcon className="w-4 h-4" />;
      case 'requires_attention':
        return <ExclamationTriangleIcon className="w-4 h-4" />;
      default:
        return <ClockIcon className="w-4 h-4" />;
    }
  };
  
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - time.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return t('time.justNow');
    if (diffInHours < 24) return t('time.hoursAgo', { count: diffInHours });
    const diffInDays = Math.floor(diffInHours / 24);
    return t('time.daysAgo', { count: diffInDays });
  };
  
  return (
    <div className={`space-y-8 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-primary rounded-2xl p-8 text-white"
      >
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-3xl font-bold mb-2">
              {t('admin.welcome')}
            </h1>
            <p className="text-primary-100 text-lg">
              {t('admin.welcomeMessage')}
            </p>
          </div>
          <div className="hidden md:block">
            <ChartBarIcon className="w-24 h-24 text-primary-200" />
          </div>
        </div>
      </motion.div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
          >
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm font-medium flex items-center ${
                    stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.changeType === 'positive' ? (
                      <TrendingUpIcon className="w-4 h-4 mr-1" />
                    ) : (
                      <TrendingDownIcon className="w-4 h-4 mr-1" />
                    )}
                    {stat.change}
                  </span>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {stat.subtitle}
                </p>
              </div>
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>
      
      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-2xl p-8 shadow-lg"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          {t('admin.quickActions')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <Link
              key={action.title}
              to={action.href}
              className="group relative"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`${action.color} text-white rounded-xl p-6 transition-all duration-300 transform hover:shadow-lg`}
              >
                <div className="flex items-center justify-between mb-4">
                  <action.icon className="w-8 h-8" />
                  {action.count && (
                    <span className="bg-white bg-opacity-20 text-white text-xs font-bold px-2 py-1 rounded-full">
                      {action.count}
                    </span>
                  )}
                </div>
                <h3 className="font-semibold mb-2">{action.title}</h3>
                <p className="text-sm opacity-90 mb-3">{action.description}</p>
                <ArrowRightIcon className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
              </motion.div>
            </Link>
          ))}
        </div>
      </motion.div>
      
      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-2xl p-8 shadow-lg"
      >
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-bold text-gray-900">
            {t('admin.recentActivity')}
          </h2>
          <Link
            to="/admin/activity"
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            {t('admin.viewAll')}
          </Link>
        </div>
        
        <div className="space-y-4">
          {stats.recentActivity.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`flex items-center space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors ${
                isRTL ? 'space-x-reverse' : ''
              }`}
            >
              <div className={`p-2 rounded-lg ${getActivityColor(activity.status)}`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <div className={`flex items-center space-x-1 ${getActivityColor(activity.status)} px-2 py-1 rounded-full`}>
                    {getStatusIcon(activity.status)}
                    <span className="text-xs font-medium">
                      {t(`admin.${activity.status}`)}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-500">
                  {activity.description}
                </p>
              </div>
              <div className="text-xs text-gray-400">
                {formatTimeAgo(activity.timestamp)}
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default AdminDashboard;
