import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Import translation files
import enTranslations from '../locales/en.json';
import psTranslations from '../locales/ps.json';
import faTranslations from '../locales/fa.json';

export type Language = 'en' | 'ps' | 'fa';

interface LanguageContextType {
  currentLanguage: Language;
  changeLanguage: (lang: Language) => void;
  t: (key: string, params?: Record<string, string | number>) => string;
  isRTL: boolean;
  direction: 'ltr' | 'rtl';
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

interface LanguageProviderProps {
  children: ReactNode;
}

// Translation data
const translations = {
  en: enTranslations,
  ps: psTranslations,
  fa: faTranslations
};

// Helper function to get nested translation value
const getNestedValue = (obj: any, path: string): string => {
  return path.split('.').reduce((current, key) => current?.[key], obj) || path;
};

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState<Language>(() => {
    const saved = localStorage.getItem('language');
    return (saved as Language) || 'en';
  });

  const changeLanguage = (lang: Language) => {
    setCurrentLanguage(lang);
    localStorage.setItem('language', lang);
    
    // Update document direction
    document.documentElement.dir = lang === 'en' ? 'ltr' : 'rtl';
    document.documentElement.lang = lang;
  };

  useEffect(() => {
    // Set initial direction
    document.documentElement.dir = currentLanguage === 'en' ? 'ltr' : 'rtl';
    document.documentElement.lang = currentLanguage;
  }, [currentLanguage]);

  const t = (key: string, params?: Record<string, string | number>): string => {
    const translation = getNestedValue(translations[currentLanguage], key);
    
    if (!params) return translation;
    
    // Replace parameters in the translation
    return Object.entries(params).reduce((str, [paramKey, paramValue]) => {
      return str.replace(new RegExp(`{${paramKey}}`, 'g'), String(paramValue));
    }, translation);
  };

  const isRTL = currentLanguage === 'ps' || currentLanguage === 'fa';
  const direction = isRTL ? 'rtl' : 'ltr';

  const value = {
    currentLanguage,
    changeLanguage,
    t,
    isRTL,
    direction
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;
