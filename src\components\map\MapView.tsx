import React, { useState, useRef, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { 
  MapPinIcon,
  HomeIcon,
  BuildingOfficeIcon,
  HeartIcon,
  EyeIcon,
  XMarkIcon,
  PlusIcon,
  MinusIcon,
  ArrowsPointingOutIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

interface PropertyMarker {
  id: number;
  title: string;
  location: string;
  price: string;
  image: string;
  beds: number;
  baths: number;
  area: string;
  type: string;
  category: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  saved: boolean;
}

interface MapViewProps {
  properties: PropertyMarker[];
  selectedProperty?: PropertyMarker | null;
  onPropertySelect: (property: PropertyMarker | null) => void;
  onToggleSaved?: (propertyId: number) => void;
  className?: string;
}

const MapView: React.FC<MapViewProps> = ({
  properties,
  selectedProperty,
  onPropertySelect,
  onToggleSaved,
  className = ''
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapCenter, setMapCenter] = useState({ lat: 34.5553, lng: 69.2075 }); // Kabul center
  const [zoomLevel, setZoomLevel] = useState(12);
  const [hoveredProperty, setHoveredProperty] = useState<PropertyMarker | null>(null);

  // Mock map implementation (in real app, use Google Maps, Mapbox, or OpenStreetMap)
  const handleMarkerClick = (property: PropertyMarker) => {
    onPropertySelect(property);
    setMapCenter(property.coordinates);
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 1, 18));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 1, 8));
  };

  const handleFullscreen = () => {
    if (mapRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
      } else {
        mapRef.current.requestFullscreen();
      }
    }
  };

  const getMarkerColor = (property: PropertyMarker) => {
    if (selectedProperty?.id === property.id) return 'bg-primary-500';
    if (hoveredProperty?.id === property.id) return 'bg-secondary-500';
    return property.category === 'For Sale' ? 'bg-accent-500' : 'bg-primary-500';
  };

  const getMarkerSize = (property: PropertyMarker) => {
    if (selectedProperty?.id === property.id) return 'w-8 h-8';
    if (hoveredProperty?.id === property.id) return 'w-7 h-7';
    return 'w-6 h-6';
  };

  return (
    <div className={`relative bg-gray-200 rounded-xl overflow-hidden ${className}`} ref={mapRef}>
      {/* Map Background */}
      <div 
        className="w-full h-full bg-gradient-to-br from-blue-100 to-green-100 relative"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23e5e7eb' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }}
      >
        {/* Map Controls */}
        <div className="absolute top-4 right-4 z-20 flex flex-col space-y-2">
          <button
            onClick={handleZoomIn}
            className="w-10 h-10 bg-white rounded-lg shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors duration-300"
          >
            <PlusIcon className="w-5 h-5 text-gray-600" />
          </button>
          <button
            onClick={handleZoomOut}
            className="w-10 h-10 bg-white rounded-lg shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors duration-300"
          >
            <MinusIcon className="w-5 h-5 text-gray-600" />
          </button>
          <button
            onClick={handleFullscreen}
            className="w-10 h-10 bg-white rounded-lg shadow-lg flex items-center justify-center hover:bg-gray-50 transition-colors duration-300"
          >
            <ArrowsPointingOutIcon className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Map Legend */}
        <div className="absolute bottom-4 left-4 z-20 bg-white rounded-lg shadow-lg p-3">
          <div className="text-xs font-medium text-gray-900 mb-2">Legend</div>
          <div className="space-y-1">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-accent-500 rounded-full"></div>
              <span className="text-xs text-gray-600">For Sale</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-primary-500 rounded-full"></div>
              <span className="text-xs text-gray-600">For Rent</span>
            </div>
          </div>
        </div>

        {/* Property Markers */}
        {properties.map((property) => {
          // Calculate position based on coordinates (mock calculation)
          const x = ((property.coordinates.lng - 69.1) * 1000) + 50;
          const y = ((34.6 - property.coordinates.lat) * 1000) + 50;
          
          return (
            <div
              key={property.id}
              className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer z-10"
              style={{ 
                left: `${Math.max(5, Math.min(95, x))}%`, 
                top: `${Math.max(5, Math.min(95, y))}%` 
              }}
              onClick={() => handleMarkerClick(property)}
              onMouseEnter={() => setHoveredProperty(property)}
              onMouseLeave={() => setHoveredProperty(null)}
            >
              {/* Marker */}
              <div className={`
                ${getMarkerColor(property)} ${getMarkerSize(property)}
                rounded-full border-2 border-white shadow-lg
                flex items-center justify-center
                transition-all duration-300 transform hover:scale-110
              `}>
                <HomeIcon className="w-3 h-3 text-white" />
              </div>

              {/* Price Label */}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
                <div className="bg-white px-2 py-1 rounded shadow-lg text-xs font-semibold text-gray-900 whitespace-nowrap">
                  {property.price}
                </div>
              </div>

              {/* Hover Tooltip */}
              {hoveredProperty?.id === property.id && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 animate-fade-in">
                  <div className="bg-white rounded-lg shadow-large p-3 border">
                    <div className="flex items-start space-x-3">
                      <img
                        src={property.image}
                        alt={property.title}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-gray-900 text-sm truncate">
                          {property.title}
                        </h4>
                        <p className="text-xs text-gray-600 mb-1">{property.location}</p>
                        <div className="flex items-center space-x-3 text-xs text-gray-500">
                          <span>{property.beds} beds</span>
                          <span>{property.baths} baths</span>
                          <span>{property.area}</span>
                        </div>
                        <div className="mt-1 text-sm font-bold text-primary-600">
                          {property.price}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* Selected Property Card */}
        {selectedProperty && (
          <div className="absolute bottom-4 right-4 z-20 w-80 animate-slide-up">
            <div className="bg-white rounded-xl shadow-large overflow-hidden">
              <div className="relative">
                <img
                  src={selectedProperty.image}
                  alt={selectedProperty.title}
                  className="w-full h-48 object-cover"
                />
                <button
                  onClick={() => onPropertySelect(null)}
                  className="absolute top-3 right-3 w-8 h-8 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300"
                >
                  <XMarkIcon className="w-5 h-5 text-gray-600" />
                </button>
                
                {/* Save Button */}
                <button
                  onClick={() => onToggleSaved?.(selectedProperty.id)}
                  className="absolute top-3 left-3 w-8 h-8 bg-white/90 rounded-full flex items-center justify-center hover:bg-white transition-colors duration-300"
                >
                  {selectedProperty.saved ? (
                    <HeartSolidIcon className="w-5 h-5 text-red-500" />
                  ) : (
                    <HeartIcon className="w-5 h-5 text-gray-600" />
                  )}
                </button>

                {/* Category Badge */}
                <div className="absolute bottom-3 left-3">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                    {selectedProperty.category}
                  </span>
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                  {selectedProperty.title}
                </h3>
                
                <div className="flex items-center text-gray-600 mb-3">
                  <MapPinIcon className="w-4 h-4 mr-1" />
                  <span className="text-sm">{selectedProperty.location}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                  <div className="flex items-center">
                    <HomeIcon className="w-4 h-4 mr-1" />
                    <span>{selectedProperty.beds} beds</span>
                  </div>
                  <div className="flex items-center">
                    <BuildingOfficeIcon className="w-4 h-4 mr-1" />
                    <span>{selectedProperty.baths} baths</span>
                  </div>
                  <span>{selectedProperty.area}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="text-xl font-bold text-primary-500">
                    {selectedProperty.price}
                  </div>
                  <Link
                    to={`/property/${selectedProperty.id}`}
                    className="btn-primary text-sm"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* No Properties Message */}
        {properties.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <MapPinIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                No properties found
              </h3>
              <p className="text-gray-600">
                Try adjusting your search filters to see more results.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MapView;
