import { Router } from 'express';
import {
  getDashboardStats,
  getAllUsers,
  getAllProperties,
  approveProperty,
  rejectProperty,
  deleteProperty,
  updateUserRole,
  getSystemStats
} from '../controllers/adminController';
import { updateUserStatus, deleteUser } from '../controllers/userController';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize('ADMIN', 'SUPER_ADMIN'));

// Dashboard and stats
router.get('/dashboard', getDashboardStats);
router.get('/stats', getSystemStats);

// User management
router.get('/users', getAllUsers);
router.put('/users/:id/role', updateUserRole);
router.put('/users/:id/status', updateUserStatus);
router.delete('/users/:id', authorize('SUPER_ADMIN'), deleteUser);

// Property management
router.get('/properties', getAllProperties);
router.put('/properties/:id/approve', approveProperty);
router.put('/properties/:id/reject', rejectProperty);
router.delete('/properties/:id', deleteProperty);

export default router;
