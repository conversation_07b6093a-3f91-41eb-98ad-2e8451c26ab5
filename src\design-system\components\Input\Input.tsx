/**
 * Professional Input Component
 * A comprehensive input component with validation, icons, and multiple variants
 */

import React, { forwardRef, InputHTMLAttributes, ReactNode, useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils/cn';
import { Eye, EyeOff, AlertCircle, CheckCircle, Info } from 'lucide-react';

// Input variants
const inputVariants = cva(
  [
    'flex w-full rounded-lg border transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-1',
    'disabled:cursor-not-allowed disabled:opacity-50',
    'placeholder:text-neutral-400'
  ],
  {
    variants: {
      variant: {
        default: [
          'border-neutral-300 bg-white text-neutral-900',
          'hover:border-neutral-400',
          'focus:border-primary-500 focus:ring-primary-500/20'
        ],
        filled: [
          'border-transparent bg-neutral-100 text-neutral-900',
          'hover:bg-neutral-200',
          'focus:bg-white focus:border-primary-500 focus:ring-primary-500/20'
        ],
        flushed: [
          'border-0 border-b-2 border-neutral-300 bg-transparent rounded-none',
          'hover:border-neutral-400',
          'focus:border-primary-500 focus:ring-0'
        ]
      },
      size: {
        sm: 'h-8 px-3 text-sm',
        md: 'h-10 px-3 text-sm',
        lg: 'h-12 px-4 text-base'
      },
      state: {
        default: '',
        error: [
          'border-error-500 focus:border-error-500 focus:ring-error-500/20',
          'text-error-900'
        ],
        success: [
          'border-success-500 focus:border-success-500 focus:ring-success-500/20',
          'text-success-900'
        ],
        warning: [
          'border-warning-500 focus:border-warning-500 focus:ring-warning-500/20',
          'text-warning-900'
        ]
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      state: 'default'
    }
  }
);

export interface InputProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof inputVariants> {
  /** Input label */
  label?: string;
  /** Helper text */
  helperText?: string;
  /** Error message */
  error?: string;
  /** Success message */
  success?: string;
  /** Warning message */
  warning?: string;
  /** Icon to display on the left */
  leftIcon?: ReactNode;
  /** Icon to display on the right */
  rightIcon?: ReactNode;
  /** Additional element to display on the right (e.g., button) */
  rightElement?: ReactNode;
  /** Whether the input is required */
  required?: boolean;
  /** Whether to show password toggle for password inputs */
  showPasswordToggle?: boolean;
  /** Custom container className */
  containerClassName?: string;
  /** Label className */
  labelClassName?: string;
  /** Helper text className */
  helperClassName?: string;
}

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      label,
      helperText,
      error,
      success,
      warning,
      leftIcon,
      rightIcon,
      rightElement,
      required,
      showPasswordToggle,
      containerClassName,
      labelClassName,
      helperClassName,
      variant,
      size,
      state,
      className,
      type = 'text',
      disabled,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);

    // Determine the current state based on props
    const currentState = error ? 'error' : success ? 'success' : warning ? 'warning' : state;

    // Determine the input type
    const inputType = type === 'password' && showPassword ? 'text' : type;

    // Get the appropriate message and icon
    const getMessage = () => {
      if (error) return { text: error, icon: AlertCircle, color: 'text-error-600' };
      if (success) return { text: success, icon: CheckCircle, color: 'text-success-600' };
      if (warning) return { text: warning, icon: Info, color: 'text-warning-600' };
      if (helperText) return { text: helperText, icon: null, color: 'text-neutral-600' };
      return null;
    };

    const message = getMessage();

    return (
      <div className={cn('space-y-1', containerClassName)}>
        {/* Label */}
        {label && (
          <label
            className={cn(
              'block text-sm font-medium text-neutral-700',
              disabled && 'text-neutral-400',
              labelClassName
            )}
          >
            {label}
            {required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}

        {/* Input container */}
        <div className="relative">
          {/* Left icon */}
          {leftIcon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 pointer-events-none">
              <span className="h-4 w-4 flex items-center justify-center">
                {leftIcon}
              </span>
            </div>
          )}

          {/* Input */}
          <input
            ref={ref}
            type={inputType}
            disabled={disabled}
            className={cn(
              inputVariants({ variant, size, state: currentState }),
              leftIcon && 'pl-10',
              (rightIcon || rightElement || (type === 'password' && showPasswordToggle)) && 'pr-10',
              className
            )}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            {...props}
          />

          {/* Right side content */}
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
            {/* Password toggle */}
            {type === 'password' && showPasswordToggle && (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-neutral-400 hover:text-neutral-600 focus:outline-none focus:text-neutral-600 transition-colors"
                tabIndex={-1}
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            )}

            {/* Right icon */}
            {rightIcon && (
              <span className="text-neutral-400 h-4 w-4 flex items-center justify-center">
                {rightIcon}
              </span>
            )}

            {/* Right element */}
            {rightElement}
          </div>
        </div>

        {/* Message */}
        {message && (
          <div className={cn('flex items-center space-x-1 text-xs', message.color, helperClassName)}>
            {message.icon && <message.icon className="h-3 w-3 flex-shrink-0" />}
            <span>{message.text}</span>
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

// Textarea component
export interface TextareaProps
  extends Omit<React.TextareaHTMLAttributes<HTMLTextAreaElement>, 'size'>,
    Pick<InputProps, 'label' | 'helperText' | 'error' | 'success' | 'warning' | 'required' | 'containerClassName' | 'labelClassName' | 'helperClassName'> {
  /** Textarea variant */
  variant?: VariantProps<typeof inputVariants>['variant'];
  /** Textarea state */
  state?: VariantProps<typeof inputVariants>['state'];
  /** Whether to auto-resize the textarea */
  autoResize?: boolean;
  /** Minimum number of rows */
  minRows?: number;
  /** Maximum number of rows */
  maxRows?: number;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      label,
      helperText,
      error,
      success,
      warning,
      required,
      containerClassName,
      labelClassName,
      helperClassName,
      variant = 'default',
      state,
      autoResize = false,
      minRows = 3,
      maxRows = 10,
      className,
      disabled,
      onChange,
      ...props
    },
    ref
  ) => {
    const textareaRef = React.useRef<HTMLTextAreaElement>(null);
    
    // Combine refs
    React.useImperativeHandle(ref, () => textareaRef.current!);

    // Auto-resize functionality
    React.useEffect(() => {
      if (autoResize && textareaRef.current) {
        const textarea = textareaRef.current;
        const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
        const minHeight = lineHeight * minRows;
        const maxHeight = lineHeight * maxRows;

        textarea.style.height = 'auto';
        const scrollHeight = textarea.scrollHeight;
        textarea.style.height = `${Math.min(Math.max(scrollHeight, minHeight), maxHeight)}px`;
      }
    }, [autoResize, minRows, maxRows, props.value]);

    // Determine the current state
    const currentState = error ? 'error' : success ? 'success' : warning ? 'warning' : state;

    // Get the appropriate message
    const getMessage = () => {
      if (error) return { text: error, icon: AlertCircle, color: 'text-error-600' };
      if (success) return { text: success, icon: CheckCircle, color: 'text-success-600' };
      if (warning) return { text: warning, icon: Info, color: 'text-warning-600' };
      if (helperText) return { text: helperText, icon: null, color: 'text-neutral-600' };
      return null;
    };

    const message = getMessage();

    return (
      <div className={cn('space-y-1', containerClassName)}>
        {/* Label */}
        {label && (
          <label
            className={cn(
              'block text-sm font-medium text-neutral-700',
              disabled && 'text-neutral-400',
              labelClassName
            )}
          >
            {label}
            {required && <span className="text-error-500 ml-1">*</span>}
          </label>
        )}

        {/* Textarea */}
        <textarea
          ref={textareaRef}
          disabled={disabled}
          rows={autoResize ? minRows : props.rows || 3}
          className={cn(
            inputVariants({ variant, state: currentState }),
            'resize-none',
            !autoResize && 'resize-y',
            'py-2',
            className
          )}
          onChange={(e) => {
            onChange?.(e);
            if (autoResize && textareaRef.current) {
              const textarea = textareaRef.current;
              const lineHeight = parseInt(getComputedStyle(textarea).lineHeight);
              const minHeight = lineHeight * minRows;
              const maxHeight = lineHeight * maxRows;

              textarea.style.height = 'auto';
              const scrollHeight = textarea.scrollHeight;
              textarea.style.height = `${Math.min(Math.max(scrollHeight, minHeight), maxHeight)}px`;
            }
          }}
          {...props}
        />

        {/* Message */}
        {message && (
          <div className={cn('flex items-center space-x-1 text-xs', message.color, helperClassName)}>
            {message.icon && <message.icon className="h-3 w-3 flex-shrink-0" />}
            <span>{message.text}</span>
          </div>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

// Export variants for external use
export { inputVariants };
export type InputVariant = VariantProps<typeof inputVariants>['variant'];
export type InputSize = VariantProps<typeof inputVariants>['size'];
export type InputState = VariantProps<typeof inputVariants>['state'];
