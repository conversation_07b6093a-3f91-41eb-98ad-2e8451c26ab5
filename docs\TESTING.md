# 🧪 Testing Guide

This guide covers testing strategies and implementation for the Real Estate Platform.

## 📋 Testing Strategy

### Testing Pyramid
```
    /\
   /  \     E2E Tests (Few)
  /____\    
 /      \   Integration Tests (Some)
/________\  Unit Tests (Many)
```

### Test Types
- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test API endpoints and component interactions
- **End-to-End Tests**: Test complete user workflows
- **Performance Tests**: Test application performance under load

## 🔧 Setup

### Frontend Testing Setup

#### Install Dependencies
```bash
npm install --save-dev \
  @testing-library/react \
  @testing-library/jest-dom \
  @testing-library/user-event \
  vitest \
  jsdom \
  @vitest/ui \
  @types/jest
```

#### Vitest Configuration
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    css: true,
  },
})
```

#### Test Setup File
```typescript
// src/test/setup.ts
import '@testing-library/jest-dom'
import { cleanup } from '@testing-library/react'
import { afterEach } from 'vitest'

afterEach(() => {
  cleanup()
})
```

### Backend Testing Setup

#### Install Dependencies
```bash
cd backend
npm install --save-dev \
  jest \
  @types/jest \
  supertest \
  @types/supertest \
  mongodb-memory-server
```

#### Jest Configuration
```javascript
// backend/jest.config.js
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/?(*.)+(spec|test).ts'],
  transform: {
    '^.+\\.ts$': 'ts-jest',
  },
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/index.ts',
  ],
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
}
```

## 🧪 Frontend Tests

### Component Testing Example
```typescript
// src/components/common/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import Button from '../Button'

describe('Button Component', () => {
  it('renders button with text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByRole('button')).toHaveTextContent('Click me')
  })

  it('calls onClick when clicked', () => {
    const handleClick = vi.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies variant styles correctly', () => {
    render(<Button variant="primary">Primary Button</Button>)
    expect(screen.getByRole('button')).toHaveClass('bg-primary-600')
  })

  it('is disabled when disabled prop is true', () => {
    render(<Button disabled>Disabled Button</Button>)
    expect(screen.getByRole('button')).toBeDisabled()
  })
})
```

### Hook Testing Example
```typescript
// src/hooks/__tests__/usePropertySearch.test.ts
import { renderHook, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { usePropertySearch } from '../usePropertySearch'
import * as apiService from '../../services/api'

vi.mock('../../services/api')

describe('usePropertySearch Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('fetches properties on mount', async () => {
    const mockProperties = [
      { id: '1', title: 'Test Property', price: 100000 }
    ]
    
    vi.spyOn(apiService, 'apiService').mockResolvedValue({
      data: mockProperties,
      meta: { total: 1, page: 1, totalPages: 1 }
    })

    const { result } = renderHook(() => usePropertySearch())

    await waitFor(() => {
      expect(result.current.properties).toEqual(mockProperties)
      expect(result.current.loading).toBe(false)
    })
  })

  it('handles search correctly', async () => {
    const { result } = renderHook(() => usePropertySearch())
    
    await waitFor(() => {
      result.current.search('villa')
    })

    expect(apiService.apiService.searchProperties).toHaveBeenCalledWith('villa')
  })
})
```

### Page Testing Example
```typescript
// src/pages/public/__tests__/LoginPage.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { describe, it, expect, vi } from 'vitest'
import LoginPage from '../LoginPage'
import { AuthProvider } from '../../../contexts/AuthContext'

const MockedLoginPage = () => (
  <BrowserRouter>
    <AuthProvider>
      <LoginPage />
    </AuthProvider>
  </BrowserRouter>
)

describe('LoginPage', () => {
  it('renders login form', () => {
    render(<MockedLoginPage />)
    
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
  })

  it('shows validation errors for empty fields', async () => {
    render(<MockedLoginPage />)
    
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }))
    
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
      expect(screen.getByText(/password is required/i)).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    render(<MockedLoginPage />)
    
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /sign in/i }))
    
    // Add assertions for successful login
  })
})
```

## 🔧 Backend Tests

### Model Testing Example
```typescript
// backend/src/models/__tests__/User.test.ts
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import mongoose from 'mongoose'
import { MongoMemoryServer } from 'mongodb-memory-server'
import User from '../User'

describe('User Model', () => {
  let mongoServer: MongoMemoryServer

  beforeEach(async () => {
    mongoServer = await MongoMemoryServer.create()
    const mongoUri = mongoServer.getUri()
    await mongoose.connect(mongoUri)
  })

  afterEach(async () => {
    await mongoose.disconnect()
    await mongoServer.stop()
  })

  it('should create a user successfully', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Test User'
    }

    const user = new User(userData)
    const savedUser = await user.save()

    expect(savedUser._id).toBeDefined()
    expect(savedUser.email).toBe(userData.email)
    expect(savedUser.name).toBe(userData.name)
    expect(savedUser.password).not.toBe(userData.password) // Should be hashed
  })

  it('should require email and password', async () => {
    const user = new User({})
    
    await expect(user.save()).rejects.toThrow()
  })

  it('should not allow duplicate emails', async () => {
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Test User'
    }

    await new User(userData).save()
    
    const duplicateUser = new User(userData)
    await expect(duplicateUser.save()).rejects.toThrow()
  })
})
```

### Controller Testing Example
```typescript
// backend/src/controllers/__tests__/authController.test.ts
import request from 'supertest'
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import app from '../../app'
import User from '../../models/User'
import { connectTestDB, closeTestDB } from '../../test/helpers'

describe('Auth Controller', () => {
  beforeEach(async () => {
    await connectTestDB()
  })

  afterEach(async () => {
    await User.deleteMany({})
    await closeTestDB()
  })

  describe('POST /api/auth/register', () => {
    it('should register a new user', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.user.email).toBe(userData.email)
      expect(response.body.data.tokens.accessToken).toBeDefined()
    })

    it('should not register user with invalid email', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'password123',
        name: 'Test User'
      }

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400)

      expect(response.body.success).toBe(false)
    })
  })

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      const user = new User({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      })
      await user.save()
    })

    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        })
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(response.body.data.tokens.accessToken).toBeDefined()
    })

    it('should not login with invalid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        })
        .expect(401)

      expect(response.body.success).toBe(false)
    })
  })
})
```

### API Integration Testing Example
```typescript
// backend/src/routes/__tests__/properties.test.ts
import request from 'supertest'
import { describe, it, expect, beforeEach, afterEach } from '@jest/globals'
import app from '../../app'
import User from '../../models/User'
import Property from '../../models/Property'
import { generateToken } from '../../utils/jwt'
import { connectTestDB, closeTestDB } from '../../test/helpers'

describe('Properties API', () => {
  let authToken: string
  let userId: string

  beforeEach(async () => {
    await connectTestDB()
    
    const user = new User({
      email: '<EMAIL>',
      password: 'password123',
      name: 'Test User'
    })
    const savedUser = await user.save()
    userId = savedUser._id.toString()
    authToken = generateToken(savedUser._id.toString())
  })

  afterEach(async () => {
    await Property.deleteMany({})
    await User.deleteMany({})
    await closeTestDB()
  })

  describe('GET /api/properties', () => {
    it('should get all properties', async () => {
      const response = await request(app)
        .get('/api/properties')
        .expect(200)

      expect(response.body.success).toBe(true)
      expect(Array.isArray(response.body.data)).toBe(true)
    })

    it('should filter properties by price range', async () => {
      const response = await request(app)
        .get('/api/properties?minPrice=100000&maxPrice=200000')
        .expect(200)

      expect(response.body.success).toBe(true)
    })
  })

  describe('POST /api/properties', () => {
    it('should create a new property', async () => {
      const propertyData = {
        title: 'Test Property',
        description: 'A beautiful test property',
        price: 150000,
        category: 'SALE',
        type: 'HOUSE',
        province: 'Kabul',
        city: 'Kabul',
        address: 'Test Address',
        area: 1500
      }

      const response = await request(app)
        .post('/api/properties')
        .set('Authorization', `Bearer ${authToken}`)
        .send(propertyData)
        .expect(201)

      expect(response.body.success).toBe(true)
      expect(response.body.data.title).toBe(propertyData.title)
    })

    it('should not create property without authentication', async () => {
      const propertyData = {
        title: 'Test Property',
        price: 150000
      }

      await request(app)
        .post('/api/properties')
        .send(propertyData)
        .expect(401)
    })
  })
})
```

## 🎭 End-to-End Tests

### Playwright Setup
```bash
npm install --save-dev @playwright/test
npx playwright install
```

### E2E Test Example
```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Authentication Flow', () => {
  test('user can register and login', async ({ page }) => {
    // Go to registration page
    await page.goto('/register')
    
    // Fill registration form
    await page.fill('[data-testid="firstName"]', 'John')
    await page.fill('[data-testid="lastName"]', 'Doe')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password123')
    await page.fill('[data-testid="confirmPassword"]', 'password123')
    
    // Submit form
    await page.click('[data-testid="register-button"]')
    
    // Should redirect to dashboard
    await expect(page).toHaveURL('/dashboard')
    
    // Logout
    await page.click('[data-testid="logout-button"]')
    
    // Should redirect to home
    await expect(page).toHaveURL('/')
    
    // Login again
    await page.goto('/login')
    await page.fill('[data-testid="email"]', '<EMAIL>')
    await page.fill('[data-testid="password"]', 'password123')
    await page.click('[data-testid="login-button"]')
    
    // Should be back in dashboard
    await expect(page).toHaveURL('/dashboard')
  })
})
```

## 📊 Test Scripts

### Package.json Scripts
```json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "test:backend": "cd backend && npm test",
    "test:e2e": "playwright test",
    "test:all": "npm test && npm run test:backend && npm run test:e2e"
  }
}
```

## 🎯 Testing Best Practices

### General Guidelines
1. **Write tests first** (TDD approach when possible)
2. **Test behavior, not implementation**
3. **Keep tests simple and focused**
4. **Use descriptive test names**
5. **Mock external dependencies**
6. **Test edge cases and error conditions**

### Frontend Testing
- Test user interactions, not implementation details
- Use data-testid attributes for reliable element selection
- Mock API calls and external services
- Test accessibility features

### Backend Testing
- Test all API endpoints
- Test authentication and authorization
- Test data validation
- Test error handling
- Use in-memory database for tests

### Coverage Goals
- **Unit Tests**: 80%+ coverage
- **Integration Tests**: Cover all API endpoints
- **E2E Tests**: Cover critical user journeys

## 🚀 Running Tests

### Development
```bash
# Run frontend tests in watch mode
npm test

# Run backend tests
cd backend && npm test

# Run specific test file
npm test -- Button.test.tsx

# Run tests with coverage
npm run test:coverage
```

### CI/CD
```bash
# Run all tests
npm run test:all

# Generate coverage reports
npm run test:coverage
```

This comprehensive testing setup ensures the reliability and quality of the Real Estate Platform across all components and user interactions.
