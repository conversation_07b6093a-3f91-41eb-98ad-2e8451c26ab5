// Simple API test script
const axios = require("axios");

const BASE_URL = "http://localhost:3001/api";

async function testAPI() {
  console.log("🧪 Testing Real Estate API...\n");

  try {
    // Test 1: Health check
    console.log("1. Testing health check...");
    const health = await axios.get("http://localhost:3001/health");
    console.log("✅ Health check:", health.data.status);

    // Test 2: API info
    console.log("\n2. Testing API info...");
    const apiInfo = await axios.get(BASE_URL);
    console.log("✅ API info:", apiInfo.data.message);

    // Test 3: Register user
    console.log("\n3. Testing user registration...");
    const registerData = {
      email: "<EMAIL>",
      password: "password123",
      name: "Test User",
      // Removing phone for now due to validation
    };

    try {
      const registerResponse = await axios.post(
        `${BASE_URL}/auth/register`,
        registerData
      );
      console.log("✅ User registered successfully");

      const { accessToken } = registerResponse.data.data.tokens;

      // Test 4: Get profile
      console.log("\n4. Testing get profile...");
      const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
        headers: { Authorization: `Bearer ${accessToken}` },
      });
      console.log("✅ Profile retrieved:", profileResponse.data.data.user.name);

      // Test 5: Create property
      console.log("\n5. Testing create property...");
      const propertyData = {
        title: "Beautiful House in Kabul",
        description: "A wonderful 3-bedroom house with garden",
        price: 150000,
        category: "SALE",
        type: "HOUSE",
        province: "Kabul",
        city: "Kabul",
        address: "123 Main Street, Kabul",
        bedrooms: 3,
        bathrooms: 2,
        area: 200,
        parking: true,
        garden: true,
        features: ["Modern Kitchen", "Central Heating"],
      };

      const propertyResponse = await axios.post(
        `${BASE_URL}/properties`,
        propertyData,
        {
          headers: { Authorization: `Bearer ${accessToken}` },
        }
      );
      console.log("✅ Property created:", propertyResponse.data.data.title);

      // Test 6: Get properties
      console.log("\n6. Testing get properties...");
      const propertiesResponse = await axios.get(`${BASE_URL}/properties`);
      console.log(
        "✅ Properties retrieved:",
        propertiesResponse.data.data.length,
        "properties"
      );

      console.log("\n🎉 All API tests passed successfully!");
    } catch (registerError) {
      if (
        registerError.response?.data?.error?.message?.includes("already exists")
      ) {
        console.log("⚠️  User already exists, testing login instead...");

        // Test login
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
          email: registerData.email,
          password: registerData.password,
        });
        console.log("✅ User logged in successfully");

        const { accessToken } = loginResponse.data.data.tokens;

        // Test get profile with login token
        const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
          headers: { Authorization: `Bearer ${accessToken}` },
        });
        console.log(
          "✅ Profile retrieved:",
          profileResponse.data.data.user.name
        );

        console.log("\n🎉 Login and profile tests passed!");
      } else {
        throw registerError;
      }
    }
  } catch (error) {
    console.error("❌ API test failed:", error.response?.data || error.message);
  }
}

// Install axios if not installed
try {
  require("axios");
  testAPI();
} catch (e) {
  console.log("Installing axios...");
  require("child_process").execSync("npm install axios", { stdio: "inherit" });
  console.log("Axios installed, running tests...");
  testAPI();
}
