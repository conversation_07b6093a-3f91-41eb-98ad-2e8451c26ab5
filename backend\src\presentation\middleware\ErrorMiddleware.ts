/**
 * Professional Error Handling Middleware
 * Centralized error handling with proper logging and response formatting
 */

import { Request, Response, NextFunction } from 'express';
import { ApplicationError, Error<PERSON><PERSON><PERSON> } from '../../shared/errors/ApplicationErrors';

export interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    stack?: string;
  };
  meta: {
    timestamp: string;
    requestId: string;
    path: string;
    method: string;
  };
}

/**
 * Global error handling middleware
 */
export const errorMiddleware = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log error for monitoring
  logError(error, req);

  // Handle the error and format response
  const errorResponse = ErrorHandler.handle(error);
  
  const response: ErrorResponse = {
    success: false,
    error: {
      code: errorResponse.errorCode,
      message: errorResponse.message,
      details: errorResponse.details,
      stack: errorResponse.stack
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] as string || 'unknown',
      path: req.path,
      method: req.method
    }
  };

  res.status(errorResponse.statusCode).json(response);
};

/**
 * 404 Not Found middleware
 */
export const notFoundMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const response: ErrorResponse = {
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.path} not found`
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] as string || 'unknown',
      path: req.path,
      method: req.method
    }
  };

  res.status(404).json(response);
};

/**
 * Async error wrapper
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Validation error middleware
 */
export const validationErrorMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // This would be used with express-validator or similar
  // const errors = validationResult(req);
  // if (!errors.isEmpty()) {
  //   const validationError = new ValidationError('Validation failed', errors.array());
  //   return next(validationError);
  // }
  next();
};

/**
 * Rate limit error middleware
 */
export const rateLimitErrorMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const response: ErrorResponse = {
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests, please try again later'
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] as string || 'unknown',
      path: req.path,
      method: req.method
    }
  };

  res.status(429).json(response);
};

/**
 * CORS error middleware
 */
export const corsErrorMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const response: ErrorResponse = {
    success: false,
    error: {
      code: 'CORS_ERROR',
      message: 'Cross-origin request blocked'
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] as string || 'unknown',
      path: req.path,
      method: req.method
    }
  };

  res.status(403).json(response);
};

/**
 * Log error for monitoring and debugging
 */
function logError(error: Error, req: Request): void {
  const errorLog = {
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id'] || 'unknown',
    method: req.method,
    path: req.path,
    query: req.query,
    body: sanitizeBody(req.body),
    headers: sanitizeHeaders(req.headers),
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id || 'anonymous',
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...(error instanceof ApplicationError && {
        statusCode: error.statusCode,
        errorCode: error.errorCode,
        isOperational: error.isOperational,
        details: error.details
      })
    }
  };

  // Log based on error severity
  if (error instanceof ApplicationError && error.isOperational) {
    // Operational errors (expected) - log as warning
    console.warn('Operational Error:', JSON.stringify(errorLog, null, 2));
  } else {
    // Programming errors (unexpected) - log as error
    console.error('Programming Error:', JSON.stringify(errorLog, null, 2));
  }

  // Send to external monitoring service (e.g., Sentry, DataDog)
  if (process.env.NODE_ENV === 'production') {
    // Example: Sentry.captureException(error, { extra: errorLog });
  }
}

/**
 * Sanitize request body for logging (remove sensitive data)
 */
function sanitizeBody(body: any): any {
  if (!body || typeof body !== 'object') return body;

  const sensitiveFields = ['password', 'confirmPassword', 'token', 'secret', 'apiKey'];
  const sanitized = { ...body };

  sensitiveFields.forEach(field => {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  });

  return sanitized;
}

/**
 * Sanitize request headers for logging (remove sensitive data)
 */
function sanitizeHeaders(headers: any): any {
  const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
  const sanitized = { ...headers };

  sensitiveHeaders.forEach(header => {
    if (sanitized[header]) {
      sanitized[header] = '[REDACTED]';
    }
  });

  return sanitized;
}

/**
 * Error monitoring and alerting
 */
export class ErrorMonitor {
  private static errorCounts: Map<string, number> = new Map();
  private static lastReset: Date = new Date();

  static trackError(error: Error): void {
    const errorKey = error.name;
    const currentCount = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, currentCount + 1);

    // Reset counts every hour
    if (Date.now() - this.lastReset.getTime() > 60 * 60 * 1000) {
      this.errorCounts.clear();
      this.lastReset = new Date();
    }

    // Alert if error count exceeds threshold
    if (currentCount > 10) {
      this.sendAlert(errorKey, currentCount);
    }
  }

  private static sendAlert(errorType: string, count: number): void {
    console.warn(`ALERT: High error count for ${errorType}: ${count} errors in the last hour`);
    
    // Send to alerting service (e.g., PagerDuty, Slack)
    // Example: SlackService.sendAlert(`High error count: ${errorType} (${count})`);
  }

  static getErrorStats(): Record<string, number> {
    return Object.fromEntries(this.errorCounts);
  }
}

/**
 * Health check for error monitoring
 */
export const healthCheckMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const stats = ErrorMonitor.getErrorStats();
  const totalErrors = Object.values(stats).reduce((sum, count) => sum + count, 0);
  
  res.status(200).json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      errorStats: stats,
      totalErrors,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.env.npm_package_version || '1.0.0'
    }
  });
};
