import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { 
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import Modal from '../../components/common/Modal';
import Loading from '../../components/common/Loading';
import { useLanguage } from '../../contexts/LanguageContext';

interface Property {
  id: string;
  title: string;
  location: string;
  price: string;
  image: string;
  status: 'active' | 'pending' | 'sold' | 'expired';
  views: number;
  inquiries: number;
  createdAt: string;
  updatedAt: string;
  featured: boolean;
  type: string;
  category: string;
}

const MyListingsPage: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([
    {
      id: '1',
      title: 'Modern Villa in Wazir Akbar Khan',
      location: 'Kabul, Afghanistan',
      price: '$250,000',
      image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      status: 'active',
      views: 1250,
      inquiries: 15,
      createdAt: '2024-01-15',
      updatedAt: '2024-01-20',
      featured: true,
      type: 'residential',
      category: 'villa'
    },
    {
      id: '2',
      title: 'Commercial Building in Chicken Street',
      location: 'Kabul, Afghanistan',
      price: '$450,000',
      image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      status: 'pending',
      views: 890,
      inquiries: 8,
      createdAt: '2024-01-10',
      updatedAt: '2024-01-18',
      featured: false,
      type: 'commercial',
      category: 'building'
    }
  ]);
  
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [propertyToDelete, setPropertyToDelete] = useState<string | null>(null);
  
  const { t, isRTL } = useLanguage();
  
  const statusOptions = [
    { value: 'all', label: t('listings.allStatus'), count: properties.length },
    { value: 'active', label: t('listings.active'), count: properties.filter(p => p.status === 'active').length },
    { value: 'pending', label: t('listings.pending'), count: properties.filter(p => p.status === 'pending').length },
    { value: 'sold', label: t('listings.sold'), count: properties.filter(p => p.status === 'sold').length },
    { value: 'expired', label: t('listings.expired'), count: properties.filter(p => p.status === 'expired').length }
  ];
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'sold':
        return 'bg-blue-100 text-blue-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="w-4 h-4" />;
      case 'pending':
        return <ClockIcon className="w-4 h-4" />;
      case 'sold':
        return <CheckCircleIcon className="w-4 h-4" />;
      case 'expired':
        return <XCircleIcon className="w-4 h-4" />;
      default:
        return <ClockIcon className="w-4 h-4" />;
    }
  };
  
  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         property.location.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || property.status === statusFilter;
    return matchesSearch && matchesStatus;
  });
  
  const handleDeleteProperty = (propertyId: string) => {
    setPropertyToDelete(propertyId);
    setShowDeleteModal(true);
  };
  
  const confirmDelete = () => {
    if (propertyToDelete) {
      setProperties(prev => prev.filter(p => p.id !== propertyToDelete));
      setShowDeleteModal(false);
      setPropertyToDelete(null);
    }
  };
  
  const renderPropertyCard = (property: Property, index: number) => (
    <motion.div
      key={property.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className={`bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden ${
        viewMode === 'list' ? 'flex' : ''
      }`}
    >
      <div className={`relative ${viewMode === 'list' ? 'w-64 flex-shrink-0' : ''}`}>
        <img
          src={property.image}
          alt={property.title}
          className={`object-cover ${
            viewMode === 'list' ? 'w-full h-full' : 'w-full h-48'
          }`}
        />
        <div className="absolute top-3 left-3 flex items-center space-x-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${getStatusColor(property.status)}`}>
            {getStatusIcon(property.status)}
            <span>{t(`listings.${property.status}`)}</span>
          </span>
          {property.featured && (
            <span className="px-2 py-1 bg-primary-600 text-white rounded-full text-xs font-medium">
              {t('listings.featured')}
            </span>
          )}
        </div>
        <div className="absolute top-3 right-3">
          <span className="bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm font-semibold">
            {property.price}
          </span>
        </div>
      </div>
      
      <div className="p-6 flex-1">
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900 line-clamp-2">
            {property.title}
          </h3>
        </div>
        
        <p className="text-gray-600 text-sm mb-4">{property.location}</p>
        
        <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <EyeIcon className="w-4 h-4" />
              <span>{property.views}</span>
            </div>
            <div className="flex items-center space-x-1">
              <MagnifyingGlassIcon className="w-4 h-4" />
              <span>{property.inquiries}</span>
            </div>
          </div>
          <span>{new Date(property.updatedAt).toLocaleDateString()}</span>
        </div>
        
        <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse' : ''}`}>
          <Link to={`/property/${property.id}`}>
            <Button variant="outline" size="sm" icon={<EyeIcon className="w-4 h-4" />}>
              {t('listings.view')}
            </Button>
          </Link>
          <Link to={`/dashboard/listings/${property.id}/edit`}>
            <Button variant="outline" size="sm" icon={<PencilIcon className="w-4 h-4" />}>
              {t('listings.edit')}
            </Button>
          </Link>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleDeleteProperty(property.id)}
            icon={<TrashIcon className="w-4 h-4" />}
            className="text-red-600 hover:text-red-700 hover:border-red-300"
          >
            {t('listings.delete')}
          </Button>
        </div>
      </div>
    </motion.div>
  );
  
  if (loading) {
    return <Loading fullScreen text={t('listings.loading')} />;
  }
  
  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t('listings.myListings')}</h1>
          <p className="text-gray-600 mt-1">
            {t('listings.manageProperties')}
          </p>
        </div>
        <Link to="/post-property">
          <Button icon={<PlusIcon className="w-5 h-5" />}>
            {t('listings.addProperty')}
          </Button>
        </Link>
      </div>
      
      {/* Filters */}
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
          <div className="flex-1 max-w-md">
            <Input
              placeholder={t('listings.searchPlaceholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<MagnifyingGlassIcon className="w-5 h-5" />}
            />
          </div>
          
          <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label} ({option.count})
                </option>
              ))}
            </select>
            
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-all duration-200 ${
                  viewMode === 'grid' 
                    ? 'bg-white text-primary-600 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <Squares2X2Icon className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-all duration-200 ${
                  viewMode === 'list' 
                    ? 'bg-white text-primary-600 shadow-sm' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                <ListBulletIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Properties Grid */}
      {filteredProperties.length > 0 ? (
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1'
        }`}>
          {filteredProperties.map((property, index) => renderPropertyCard(property, index))}
        </div>
      ) : (
        <div className="text-center py-12 bg-white rounded-xl shadow-lg">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <MagnifyingGlassIcon className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchQuery || statusFilter !== 'all' ? t('listings.noResults') : t('listings.noProperties')}
          </h3>
          <p className="text-gray-500 mb-6">
            {searchQuery || statusFilter !== 'all' 
              ? t('listings.noResultsDesc') 
              : t('listings.noPropertiesDesc')
            }
          </p>
          {!searchQuery && statusFilter === 'all' && (
            <Link to="/post-property">
              <Button icon={<PlusIcon className="w-5 h-5" />}>
                {t('listings.addFirstProperty')}
              </Button>
            </Link>
          )}
        </div>
      )}
      
      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('listings.deleteProperty')}
        size="md"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            {t('listings.deleteConfirmation')}
          </p>
        </div>
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="outline"
            onClick={() => setShowDeleteModal(false)}
          >
            {t('common.cancel')}
          </Button>
          <Button
            variant="danger"
            onClick={confirmDelete}
          >
            {t('listings.delete')}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default MyListingsPage;
