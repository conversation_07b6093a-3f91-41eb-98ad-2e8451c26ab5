/**
 * Login User Command
 * Handles user authentication with security features
 */

import { User, UserStatus, VerificationStatus } from '../../../domain/entities/User';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { PasswordService } from '../../../shared/services/PasswordService';
import { JwtService } from '../../../shared/services/JwtService';
import { TwoFactorService } from '../../../shared/services/TwoFactorService';
import { ValidationError, UnauthorizedError, ForbiddenError } from '../../../shared/errors/ApplicationErrors';

export interface LoginUserRequest {
  email: string;
  password: string;
  twoFactorCode?: string;
  deviceId?: string;
  rememberMe?: boolean;
  userAgent?: string;
  ipAddress?: string;
}

export interface LoginUserResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    status: string;
    verificationStatus: string;
    twoFactorEnabled: boolean;
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
  requiresTwoFactor?: boolean;
  tempToken?: string;
  message: string;
}

export class LoginUserCommand {
  constructor(
    private userRepository: IUserRepository,
    private passwordService: PasswordService,
    private jwtService: JwtService,
    private twoFactorService: TwoFactorService
  ) {}

  async execute(request: LoginUserRequest): Promise<LoginUserResponse> {
    // Validate input
    this.validateInput(request);

    // Find user by email
    const user = await this.userRepository.findByEmail(request.email.toLowerCase());
    if (!user) {
      throw new UnauthorizedError('Invalid email or password');
    }

    // Check if account is locked
    if (user.isAccountLocked()) {
      throw new ForbiddenError('Account is temporarily locked due to multiple failed login attempts');
    }

    // Verify password
    const isPasswordValid = await this.passwordService.verify(request.password, user.passwordHash);
    if (!isPasswordValid) {
      // Record failed login attempt
      user.recordFailedLogin();
      await this.userRepository.update(user);
      
      throw new UnauthorizedError('Invalid email or password');
    }

    // Check account status
    this.checkAccountStatus(user);

    // Handle two-factor authentication
    if (user.securitySettings.twoFactorEnabled) {
      return await this.handleTwoFactorAuth(user, request);
    }

    // Complete login
    return await this.completeLogin(user, request);
  }

  private validateInput(request: LoginUserRequest): void {
    const errors: string[] = [];

    if (!request.email) {
      errors.push('Email is required');
    } else if (!this.isValidEmail(request.email)) {
      errors.push('Invalid email format');
    }

    if (!request.password) {
      errors.push('Password is required');
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', errors);
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private checkAccountStatus(user: User): void {
    if (user.status === UserStatus.SUSPENDED) {
      throw new ForbiddenError('Account is suspended. Please contact support.');
    }

    if (user.status === UserStatus.DEACTIVATED) {
      throw new ForbiddenError('Account is deactivated. Please contact support.');
    }

    if (user.verificationStatus === VerificationStatus.PENDING) {
      throw new ForbiddenError('Please verify your email address before logging in.');
    }

    if (user.verificationStatus === VerificationStatus.REJECTED) {
      throw new ForbiddenError('Account verification was rejected. Please contact support.');
    }
  }

  private async handleTwoFactorAuth(user: User, request: LoginUserRequest): Promise<LoginUserResponse> {
    if (!request.twoFactorCode) {
      // Generate temporary token for 2FA step
      const tempToken = await this.jwtService.generateTempToken(user.id, '10m');
      
      return {
        user: this.mapUserResponse(user),
        tokens: {
          accessToken: '',
          refreshToken: '',
          expiresIn: 0
        },
        requiresTwoFactor: true,
        tempToken,
        message: 'Two-factor authentication required'
      };
    }

    // Verify 2FA code
    const isValidCode = await this.twoFactorService.verifyCode(
      user.securitySettings.twoFactorSecret!,
      request.twoFactorCode
    );

    if (!isValidCode) {
      // Record failed login attempt
      user.recordFailedLogin();
      await this.userRepository.update(user);
      
      throw new UnauthorizedError('Invalid two-factor authentication code');
    }

    return await this.completeLogin(user, request);
  }

  private async completeLogin(user: User, request: LoginUserRequest): Promise<LoginUserResponse> {
    // Reset login attempts on successful login
    user.resetLoginAttempts();

    // Add trusted device if provided
    if (request.deviceId && request.rememberMe) {
      user.addTrustedDevice(request.deviceId);
    }

    // Update last login
    await this.userRepository.updateLastLogin(user.id);
    await this.userRepository.update(user);

    // Generate tokens
    const tokenExpiry = request.rememberMe ? '30d' : '24h';
    const accessToken = await this.jwtService.generateAccessToken(user, tokenExpiry);
    const refreshToken = await this.jwtService.generateRefreshToken(user.id);

    // Log login event
    await this.logLoginEvent(user, request);

    return {
      user: this.mapUserResponse(user),
      tokens: {
        accessToken,
        refreshToken,
        expiresIn: request.rememberMe ? 30 * 24 * 60 * 60 : 24 * 60 * 60 // seconds
      },
      message: 'Login successful'
    };
  }

  private mapUserResponse(user: User) {
    return {
      id: user.id,
      email: user.email,
      firstName: user.profile.firstName,
      lastName: user.profile.lastName,
      role: user.role,
      status: user.status,
      verificationStatus: user.verificationStatus,
      twoFactorEnabled: user.securitySettings.twoFactorEnabled
    };
  }

  private async logLoginEvent(user: User, request: LoginUserRequest): Promise<void> {
    const event = {
      type: 'USER_LOGIN',
      userId: user.id,
      email: user.email,
      timestamp: new Date(),
      metadata: {
        userAgent: request.userAgent,
        ipAddress: request.ipAddress,
        deviceId: request.deviceId,
        rememberMe: request.rememberMe,
        twoFactorUsed: user.securitySettings.twoFactorEnabled
      }
    };

    // Send to analytics/audit service
    console.log('Login event:', event);
  }
}
