import toast from 'react-hot-toast';

// Toast configuration
const toastConfig = {
  duration: 4000,
  position: 'top-right' as const,
  style: {
    borderRadius: '12px',
    background: '#333',
    color: '#fff',
    fontSize: '14px',
    fontWeight: '500',
    padding: '12px 16px',
    maxWidth: '400px',
  },
};

// Success toast
export const showSuccessToast = (message: string) => {
  toast.success(message, {
    ...toastConfig,
    style: {
      ...toastConfig.style,
      background: '#10B981',
      color: '#fff',
    },
    iconTheme: {
      primary: '#fff',
      secondary: '#10B981',
    },
  });
};

// Error toast
export const showErrorToast = (message: string) => {
  toast.error(message, {
    ...toastConfig,
    duration: 5000, // Longer duration for errors
    style: {
      ...toastConfig.style,
      background: '#EF4444',
      color: '#fff',
    },
    iconTheme: {
      primary: '#fff',
      secondary: '#EF4444',
    },
  });
};

// Warning toast
export const showWarningToast = (message: string) => {
  toast(message, {
    ...toastConfig,
    icon: '⚠️',
    style: {
      ...toastConfig.style,
      background: '#F59E0B',
      color: '#fff',
    },
  });
};

// Info toast
export const showInfoToast = (message: string) => {
  toast(message, {
    ...toastConfig,
    icon: 'ℹ️',
    style: {
      ...toastConfig.style,
      background: '#3B82F6',
      color: '#fff',
    },
  });
};

// Loading toast
export const showLoadingToast = (message: string) => {
  return toast.loading(message, {
    ...toastConfig,
    style: {
      ...toastConfig.style,
      background: '#6B7280',
      color: '#fff',
    },
  });
};

// Dismiss toast
export const dismissToast = (toastId: string) => {
  toast.dismiss(toastId);
};

// Dismiss all toasts
export const dismissAllToasts = () => {
  toast.dismiss();
};

// Promise toast - for async operations
export const showPromiseToast = <T>(
  promise: Promise<T>,
  messages: {
    loading: string;
    success: string | ((data: T) => string);
    error: string | ((error: any) => string);
  }
) => {
  return toast.promise(promise, messages, {
    ...toastConfig,
    success: {
      style: {
        ...toastConfig.style,
        background: '#10B981',
        color: '#fff',
      },
    },
    error: {
      style: {
        ...toastConfig.style,
        background: '#EF4444',
        color: '#fff',
      },
      duration: 5000,
    },
    loading: {
      style: {
        ...toastConfig.style,
        background: '#6B7280',
        color: '#fff',
      },
    },
  });
};

// Specific error messages for common scenarios
export const ValidationToasts = {
  // Authentication errors
  invalidCredentials: () => showErrorToast('Invalid email or password. Please check your credentials and try again.'),
  emailAlreadyExists: () => showErrorToast('An account with this email already exists. Please use a different email or try logging in.'),
  weakPassword: () => showErrorToast('Password is too weak. Please use at least 8 characters with uppercase, lowercase, number, and special character.'),
  passwordMismatch: () => showErrorToast('Passwords do not match. Please make sure both passwords are identical.'),
  emailRequired: () => showErrorToast('Email address is required. Please enter your email.'),
  passwordRequired: () => showErrorToast('Password is required. Please enter your password.'),
  nameRequired: () => showErrorToast('Full name is required. Please enter your name.'),
  invalidEmail: () => showErrorToast('Please enter a valid email address.'),
  invalidPhone: () => showErrorToast('Please enter a valid Afghan phone number (e.g., +***********).'),
  termsRequired: () => showErrorToast('You must accept the terms and conditions to continue.'),
  
  // Property errors
  titleRequired: () => showErrorToast('Property title is required. Please enter a descriptive title.'),
  titleTooShort: () => showErrorToast('Property title must be at least 5 characters long.'),
  titleTooLong: () => showErrorToast('Property title cannot exceed 200 characters.'),
  descriptionRequired: () => showErrorToast('Property description is required. Please provide details about the property.'),
  descriptionTooShort: () => showErrorToast('Property description must be at least 20 characters long.'),
  descriptionTooLong: () => showErrorToast('Property description cannot exceed 2000 characters.'),
  priceRequired: () => showErrorToast('Property price is required. Please enter the price.'),
  priceInvalid: () => showErrorToast('Please enter a valid price greater than 0.'),
  categoryRequired: () => showErrorToast('Property category is required. Please select Sale, Rent, or Lease.'),
  typeRequired: () => showErrorToast('Property type is required. Please select the property type.'),
  locationRequired: () => showErrorToast('Property location is required. Please enter province, city, and address.'),
  addressRequired: () => showErrorToast('Property address is required. Please enter the full address.'),
  addressTooShort: () => showErrorToast('Property address must be at least 10 characters long.'),
  areaRequired: () => showErrorToast('Property area is required. Please enter the area in square meters.'),
  areaInvalid: () => showErrorToast('Please enter a valid area greater than 0 square meters.'),
  yearBuiltInvalid: () => showErrorToast('Please enter a valid year built (1800 - current year + 5).'),
  
  // File upload errors
  fileRequired: () => showErrorToast('Please select at least one image for the property.'),
  fileTooLarge: () => showErrorToast('File size is too large. Please select images smaller than 10MB.'),
  invalidFileType: () => showErrorToast('Invalid file type. Please select only image files (JPG, PNG, WebP, GIF).'),
  tooManyFiles: () => showErrorToast('Too many files selected. Maximum 10 images allowed.'),
  uploadFailed: () => showErrorToast('Failed to upload images. Please try again.'),
  
  // Message errors
  messageRequired: () => showErrorToast('Message content is required. Please enter your message.'),
  messageTooLong: () => showErrorToast('Message is too long. Please keep it under 2000 characters.'),
  receiverRequired: () => showErrorToast('Message receiver is required. Please select a recipient.'),
  
  // Search errors
  invalidSearchParams: () => showErrorToast('Invalid search parameters. Please check your filters and try again.'),
  priceRangeInvalid: () => showErrorToast('Invalid price range. Minimum price cannot be greater than maximum price.'),
  areaRangeInvalid: () => showErrorToast('Invalid area range. Minimum area cannot be greater than maximum area.'),
  
  // Network errors
  networkError: () => showErrorToast('Network error. Please check your internet connection and try again.'),
  serverError: () => showErrorToast('Server error. Please try again later or contact support if the problem persists.'),
  timeoutError: () => showErrorToast('Request timeout. Please try again.'),
  
  // Permission errors
  unauthorized: () => showErrorToast('You are not authorized to perform this action. Please log in and try again.'),
  forbidden: () => showErrorToast('You do not have permission to access this resource.'),
  sessionExpired: () => showErrorToast('Your session has expired. Please log in again.'),
  
  // General errors
  somethingWentWrong: () => showErrorToast('Something went wrong. Please try again.'),
  invalidData: () => showErrorToast('Invalid data provided. Please check your input and try again.'),
  operationFailed: () => showErrorToast('Operation failed. Please try again.'),
};

// Success messages for common scenarios
export const SuccessToasts = {
  // Authentication success
  loginSuccess: () => showSuccessToast('Welcome back! You have successfully logged in.'),
  registerSuccess: () => showSuccessToast('Account created successfully! Welcome to our platform.'),
  logoutSuccess: () => showSuccessToast('You have been logged out successfully.'),
  passwordChanged: () => showSuccessToast('Password changed successfully. Your account is now more secure.'),
  passwordResetSent: () => showSuccessToast('Password reset instructions have been sent to your email.'),
  passwordResetSuccess: () => showSuccessToast('Password reset successfully. You can now log in with your new password.'),
  
  // Profile success
  profileUpdated: () => showSuccessToast('Profile updated successfully. Your changes have been saved.'),
  avatarUpdated: () => showSuccessToast('Profile picture updated successfully.'),
  
  // Property success
  propertyCreated: () => showSuccessToast('Property listed successfully! It will be reviewed and published soon.'),
  propertyUpdated: () => showSuccessToast('Property updated successfully. Your changes have been saved.'),
  propertyDeleted: () => showSuccessToast('Property deleted successfully.'),
  propertyFavorited: () => showSuccessToast('Property added to your favorites.'),
  propertyUnfavorited: () => showSuccessToast('Property removed from your favorites.'),
  
  // Message success
  messageSent: () => showSuccessToast('Message sent successfully. The recipient will be notified.'),
  messageDeleted: () => showSuccessToast('Message deleted successfully.'),
  
  // File upload success
  filesUploaded: (count: number) => showSuccessToast(`${count} image${count > 1 ? 's' : ''} uploaded successfully.`),
  fileDeleted: () => showSuccessToast('Image deleted successfully.'),
  
  // General success
  operationSuccess: () => showSuccessToast('Operation completed successfully.'),
  changesSaved: () => showSuccessToast('Changes saved successfully.'),
  dataLoaded: () => showSuccessToast('Data loaded successfully.'),
};
