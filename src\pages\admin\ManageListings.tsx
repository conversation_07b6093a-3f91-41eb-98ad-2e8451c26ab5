import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  HomeIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  TrashIcon,
  PencilIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import Button from '../../components/common/Button';
import Input from '../../components/common/Input';
import Modal from '../../components/common/Modal';
import Loading from '../../components/common/Loading';
import { useLanguage } from '../../contexts/LanguageContext';

interface Property {
  id: string;
  title: string;
  location: string;
  price: string;
  image: string;
  status: 'active' | 'pending' | 'rejected' | 'sold';
  type: string;
  category: string;
  owner: {
    id: string;
    name: string;
    email: string;
    avatar: string;
  };
  createdAt: string;
  views: number;
  inquiries: number;
  featured: boolean;
  reportCount: number;
}

const ManageListings: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([
    {
      id: '1',
      title: 'Modern Villa in Wazir Akbar Khan',
      location: 'Kabul, Afghanistan',
      price: '$250,000',
      image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      status: 'pending',
      type: 'residential',
      category: 'villa',
      owner: {
        id: '1',
        name: 'Ahmad Khan',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80'
      },
      createdAt: '2024-01-20',
      views: 1250,
      inquiries: 15,
      featured: false,
      reportCount: 0
    },
    {
      id: '2',
      title: 'Commercial Building in Chicken Street',
      location: 'Kabul, Afghanistan',
      price: '$450,000',
      image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      status: 'active',
      type: 'commercial',
      category: 'building',
      owner: {
        id: '2',
        name: 'Fatima Ali',
        email: '<EMAIL>',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80'
      },
      createdAt: '2024-01-18',
      views: 890,
      inquiries: 8,
      featured: true,
      reportCount: 2
    }
  ]);
  
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [showPropertyModal, setShowPropertyModal] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [propertyToDelete, setPropertyToDelete] = useState<string | null>(null);
  
  const { t, isRTL } = useLanguage();
  
  const statusOptions = [
    { value: 'all', label: t('admin.allStatuses') },
    { value: 'active', label: t('admin.active') },
    { value: 'pending', label: t('admin.pending') },
    { value: 'rejected', label: t('admin.rejected') },
    { value: 'sold', label: t('admin.sold') }
  ];
  
  const typeOptions = [
    { value: 'all', label: t('admin.allTypes') },
    { value: 'residential', label: t('admin.residential') },
    { value: 'commercial', label: t('admin.commercial') },
    { value: 'land', label: t('admin.land') }
  ];
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'sold':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="w-4 h-4" />;
      case 'pending':
        return <ClockIcon className="w-4 h-4" />;
      case 'rejected':
        return <XCircleIcon className="w-4 h-4" />;
      case 'sold':
        return <CheckCircleIcon className="w-4 h-4" />;
      default:
        return <ClockIcon className="w-4 h-4" />;
    }
  };
  
  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         property.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         property.owner.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || property.status === statusFilter;
    const matchesType = typeFilter === 'all' || property.type === typeFilter;
    return matchesSearch && matchesStatus && matchesType;
  });
  
  const handleViewProperty = (property: Property) => {
    setSelectedProperty(property);
    setShowPropertyModal(true);
  };
  
  const handleDeleteProperty = (propertyId: string) => {
    setPropertyToDelete(propertyId);
    setShowDeleteModal(true);
  };
  
  const confirmDelete = () => {
    if (propertyToDelete) {
      setProperties(prev => prev.filter(p => p.id !== propertyToDelete));
      setShowDeleteModal(false);
      setPropertyToDelete(null);
    }
  };
  
  const handleStatusChange = (propertyId: string, newStatus: string) => {
    setProperties(prev => prev.map(property => 
      property.id === propertyId ? { ...property, status: newStatus as Property['status'] } : property
    ));
  };
  
  const handleToggleFeatured = (propertyId: string) => {
    setProperties(prev => prev.map(property => 
      property.id === propertyId ? { ...property, featured: !property.featured } : property
    ));
  };
  
  if (loading) {
    return <Loading fullScreen text={t('admin.loadingProperties')} />;
  }
  
  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
            <HomeIcon className="w-8 h-8 text-primary-600" />
            <span>{t('admin.manageListings')}</span>
          </h1>
          <p className="text-gray-600 mt-1">
            {t('admin.manageListingsDesc')}
          </p>
        </div>
        <div className="text-sm text-gray-500">
          {filteredProperties.length} {t('admin.propertiesFound')}
        </div>
      </div>
      
      {/* Filters */}
      <div className="bg-white rounded-xl p-6 shadow-lg">
        <div className={`flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 ${isRTL ? 'lg:flex-row-reverse' : ''}`}>
          <div className="flex-1 max-w-md">
            <Input
              placeholder={t('admin.searchProperties')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftIcon={<MagnifyingGlassIcon className="w-5 h-5" />}
            />
          </div>
          
          <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              {statusOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              {typeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
      
      {/* Properties Table */}
      <div className="bg-white rounded-xl shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.property')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.owner')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.status')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.performance')}
                </th>
                <th className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {t('admin.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProperties.map((property, index) => (
                <motion.tr
                  key={property.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="hover:bg-gray-50 transition-colors"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <img
                          src={property.image}
                          alt={property.title}
                          className="w-16 h-12 rounded-lg object-cover"
                        />
                        {property.featured && (
                          <div className="absolute -top-1 -right-1">
                            <StarSolidIcon className="w-4 h-4 text-yellow-500" />
                          </div>
                        )}
                        {property.reportCount > 0 && (
                          <div className="absolute -top-1 -left-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                            {property.reportCount}
                          </div>
                        )}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 line-clamp-1">
                          {property.title}
                        </p>
                        <p className="text-sm text-gray-500">{property.location}</p>
                        <p className="text-sm font-semibold text-primary-600">{property.price}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <img
                        src={property.owner.avatar}
                        alt={property.owner.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                      <div>
                        <p className="text-sm font-medium text-gray-900">{property.owner.name}</p>
                        <p className="text-xs text-gray-500">{property.owner.email}</p>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1 w-fit ${getStatusColor(property.status)}`}>
                        {getStatusIcon(property.status)}
                        <span>{t(`admin.${property.status}`)}</span>
                      </span>
                      {property.status === 'pending' && (
                        <div className="flex space-x-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusChange(property.id, 'active')}
                            className="text-green-600 hover:text-green-700 hover:border-green-300 text-xs px-2 py-1"
                          >
                            {t('admin.approve')}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleStatusChange(property.id, 'rejected')}
                            className="text-red-600 hover:text-red-700 hover:border-red-300 text-xs px-2 py-1"
                          >
                            {t('admin.reject')}
                          </Button>
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div>
                      <p>{property.views.toLocaleString()} {t('admin.views')}</p>
                      <p>{property.inquiries} {t('admin.inquiries')}</p>
                      <p className="text-xs">{new Date(property.createdAt).toLocaleDateString()}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewProperty(property)}
                        icon={<EyeIcon className="w-4 h-4" />}
                      >
                        {t('admin.view')}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleToggleFeatured(property.id)}
                        icon={<StarIcon className="w-4 h-4" />}
                        className={property.featured ? 'text-yellow-600 hover:text-yellow-700' : ''}
                      >
                        {property.featured ? t('admin.unfeature') : t('admin.feature')}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteProperty(property.id)}
                        icon={<TrashIcon className="w-4 h-4" />}
                        className="text-red-600 hover:text-red-700 hover:border-red-300"
                      >
                        {t('admin.delete')}
                      </Button>
                    </div>
                  </td>
                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredProperties.length === 0 && (
          <div className="text-center py-12">
            <HomeIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('admin.noPropertiesFound')}
            </h3>
            <p className="text-gray-500">
              {t('admin.noPropertiesFoundDesc')}
            </p>
          </div>
        )}
      </div>
      
      {/* Property Details Modal */}
      <Modal
        isOpen={showPropertyModal}
        onClose={() => setShowPropertyModal(false)}
        title={t('admin.propertyDetails')}
        size="lg"
      >
        {selectedProperty && (
          <div className="space-y-6">
            <div className="flex items-start space-x-4">
              <img
                src={selectedProperty.image}
                alt={selectedProperty.title}
                className="w-24 h-18 rounded-lg object-cover"
              />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">{selectedProperty.title}</h3>
                <p className="text-gray-600">{selectedProperty.location}</p>
                <p className="text-xl font-bold text-primary-600 mt-2">{selectedProperty.price}</p>
                <div className="flex items-center space-x-2 mt-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedProperty.status)}`}>
                    {t(`admin.${selectedProperty.status}`)}
                  </span>
                  {selectedProperty.featured && (
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs font-medium">
                      {t('admin.featured')}
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">{t('admin.owner')}</h4>
                <div className="flex items-center space-x-3">
                  <img
                    src={selectedProperty.owner.avatar}
                    alt={selectedProperty.owner.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{selectedProperty.owner.name}</p>
                    <p className="text-sm text-gray-500">{selectedProperty.owner.email}</p>
                  </div>
                </div>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">{t('admin.performance')}</h4>
                <div className="space-y-1">
                  <p className="text-sm text-gray-600">{selectedProperty.views.toLocaleString()} {t('admin.views')}</p>
                  <p className="text-sm text-gray-600">{selectedProperty.inquiries} {t('admin.inquiries')}</p>
                  <p className="text-sm text-gray-600">{selectedProperty.reportCount} {t('admin.reports')}</p>
                </div>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowPropertyModal(false)}
              >
                {t('common.close')}
              </Button>
            </div>
          </div>
        )}
      </Modal>
      
      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('admin.deleteProperty')}
        size="md"
      >
        <div className="space-y-4">
          <p className="text-gray-600">
            {t('admin.deletePropertyConfirmation')}
          </p>
        </div>
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="outline"
            onClick={() => setShowDeleteModal(false)}
          >
            {t('common.cancel')}
          </Button>
          <Button
            variant="danger"
            onClick={confirmDelete}
          >
            {t('admin.delete')}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default ManageListings;
