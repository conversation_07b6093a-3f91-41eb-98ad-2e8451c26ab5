/**
 * Professional Button Component
 * A comprehensive button component with multiple variants, sizes, and states
 */

import React, { forwardRef, ButtonHTMLAttributes, ReactNode } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils/cn';
import { Loader2 } from 'lucide-react';

// Button variants using class-variance-authority
const buttonVariants = cva(
  // Base styles
  [
    'inline-flex items-center justify-center gap-2',
    'rounded-lg font-semibold transition-all duration-200',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50',
    'select-none whitespace-nowrap'
  ],
  {
    variants: {
      variant: {
        // Primary variant
        primary: [
          'bg-primary-500 text-white shadow-sm',
          'hover:bg-primary-600 hover:shadow-md',
          'active:bg-primary-700 active:shadow-sm',
          'focus:ring-primary-500'
        ],
        // Secondary variant
        secondary: [
          'bg-neutral-100 text-neutral-900 border border-neutral-200',
          'hover:bg-neutral-200 hover:border-neutral-300',
          'active:bg-neutral-300',
          'focus:ring-neutral-500'
        ],
        // Outline variant
        outline: [
          'border border-primary-500 text-primary-500 bg-transparent',
          'hover:bg-primary-50 hover:border-primary-600',
          'active:bg-primary-100',
          'focus:ring-primary-500'
        ],
        // Ghost variant
        ghost: [
          'text-neutral-700 bg-transparent',
          'hover:bg-neutral-100 hover:text-neutral-900',
          'active:bg-neutral-200',
          'focus:ring-neutral-500'
        ],
        // Destructive variant
        destructive: [
          'bg-error-500 text-white shadow-sm',
          'hover:bg-error-600 hover:shadow-md',
          'active:bg-error-700 active:shadow-sm',
          'focus:ring-error-500'
        ],
        // Success variant
        success: [
          'bg-success-500 text-white shadow-sm',
          'hover:bg-success-600 hover:shadow-md',
          'active:bg-success-700 active:shadow-sm',
          'focus:ring-success-500'
        ],
        // Warning variant
        warning: [
          'bg-warning-500 text-white shadow-sm',
          'hover:bg-warning-600 hover:shadow-md',
          'active:bg-warning-700 active:shadow-sm',
          'focus:ring-warning-500'
        ],
        // Link variant
        link: [
          'text-primary-500 underline-offset-4',
          'hover:underline hover:text-primary-600',
          'active:text-primary-700',
          'focus:ring-primary-500'
        ]
      },
      size: {
        xs: 'h-7 px-2 text-xs',
        sm: 'h-8 px-3 text-sm',
        md: 'h-10 px-4 text-sm',
        lg: 'h-11 px-6 text-base',
        xl: 'h-12 px-8 text-lg'
      },
      fullWidth: {
        true: 'w-full'
      },
      rounded: {
        none: 'rounded-none',
        sm: 'rounded-sm',
        md: 'rounded-md',
        lg: 'rounded-lg',
        xl: 'rounded-xl',
        full: 'rounded-full'
      }
    },
    defaultVariants: {
      variant: 'primary',
      size: 'md',
      rounded: 'lg'
    }
  }
);

export interface ButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  /** Button content */
  children: ReactNode;
  /** Loading state */
  loading?: boolean;
  /** Icon to display before text */
  leftIcon?: ReactNode;
  /** Icon to display after text */
  rightIcon?: ReactNode;
  /** Custom loading icon */
  loadingIcon?: ReactNode;
  /** Loading text to display when loading */
  loadingText?: string;
  /** Additional CSS classes */
  className?: string;
  /** Tooltip text */
  tooltip?: string;
}

export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      variant,
      size,
      fullWidth,
      rounded,
      loading = false,
      leftIcon,
      rightIcon,
      loadingIcon,
      loadingText,
      className,
      disabled,
      tooltip,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;

    const buttonContent = (
      <>
        {/* Left icon or loading icon */}
        {loading ? (
          loadingIcon || <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          leftIcon
        )}

        {/* Button text */}
        <span>
          {loading && loadingText ? loadingText : children}
        </span>

        {/* Right icon (hidden when loading) */}
        {!loading && rightIcon}
      </>
    );

    const buttonElement = (
      <button
        ref={ref}
        className={cn(
          buttonVariants({ variant, size, fullWidth, rounded }),
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {buttonContent}
      </button>
    );

    // Wrap with tooltip if provided
    if (tooltip) {
      return (
        <div className="relative group">
          {buttonElement}
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-neutral-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
            {tooltip}
          </div>
        </div>
      );
    }

    return buttonElement;
  }
);

Button.displayName = 'Button';

// Button group component for related actions
export interface ButtonGroupProps {
  children: ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: VariantProps<typeof buttonVariants>['size'];
  variant?: VariantProps<typeof buttonVariants>['variant'];
}

export const ButtonGroup: React.FC<ButtonGroupProps> = ({
  children,
  className,
  orientation = 'horizontal',
  size,
  variant
}) => {
  const groupClasses = cn(
    'inline-flex',
    orientation === 'horizontal' ? 'flex-row' : 'flex-col',
    '[&>button]:rounded-none',
    '[&>button:first-child]:rounded-l-lg',
    '[&>button:last-child]:rounded-r-lg',
    orientation === 'vertical' && [
      '[&>button:first-child]:rounded-t-lg [&>button:first-child]:rounded-l-none',
      '[&>button:last-child]:rounded-b-lg [&>button:last-child]:rounded-r-none'
    ],
    '[&>button:not(:first-child)]:border-l-0',
    orientation === 'vertical' && '[&>button:not(:first-child)]:border-t-0',
    className
  );

  return (
    <div className={groupClasses}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === Button) {
          return React.cloneElement(child, {
            size: size || child.props.size,
            variant: variant || child.props.variant
          });
        }
        return child;
      })}
    </div>
  );
};

// Icon button component for icon-only buttons
export interface IconButtonProps
  extends Omit<ButtonProps, 'children' | 'leftIcon' | 'rightIcon'> {
  icon: ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, size = 'md', ...props }, ref) => {
    const iconSizes = {
      xs: 'h-3 w-3',
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6',
      xl: 'h-7 w-7'
    };

    return (
      <Button
        ref={ref}
        size={size}
        className={cn('p-0 aspect-square', className)}
        {...props}
      >
        <span className={iconSizes[size || 'md']}>{icon}</span>
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

// Floating Action Button component
export interface FABProps extends Omit<ButtonProps, 'variant' | 'size'> {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  size?: 'sm' | 'md' | 'lg';
}

export const FAB: React.FC<FABProps> = ({
  position = 'bottom-right',
  size = 'md',
  className,
  ...props
}) => {
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6'
  };

  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-14 w-14',
    lg: 'h-16 w-16'
  };

  return (
    <Button
      variant="primary"
      rounded="full"
      className={cn(
        'fixed shadow-lg hover:shadow-xl transition-shadow duration-200',
        'z-50',
        positionClasses[position],
        sizeClasses[size],
        className
      )}
      {...props}
    />
  );
};

// Export button variants for external use
export { buttonVariants };
export type ButtonVariant = VariantProps<typeof buttonVariants>['variant'];
export type ButtonSize = VariantProps<typeof buttonVariants>['size'];
