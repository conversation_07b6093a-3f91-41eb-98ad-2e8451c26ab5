import React from 'react';
import { motion } from 'framer-motion';
import Header from './Header';
import Footer from './Footer';
import Sidebar from './Sidebar';
import { useLanguage } from '../../contexts/LanguageContext';

interface LayoutProps {
  children: React.ReactNode;
  showSidebar?: boolean;
  sidebarType?: 'dashboard' | 'admin';
  className?: string;
  containerClassName?: string;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  showSidebar = false,
  sidebarType = 'dashboard',
  className = '',
  containerClassName = ''
}) => {
  const { isRTL } = useLanguage();

  const pageVariants = {
    initial: { opacity: 0, y: 20 },
    in: { opacity: 1, y: 0 },
    out: { opacity: 0, y: -20 }
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.5
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'} ${className}`}>
      <Header />
      
      <div className="flex">
        {showSidebar && (
          <motion.div
            initial={{ x: isRTL ? 100 : -100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className="hidden lg:block"
          >
            <Sidebar type={sidebarType} />
          </motion.div>
        )}
        
        <main className={`flex-1 ${showSidebar ? 'lg:ml-64' : ''} ${containerClassName}`}>
          <motion.div
            initial="initial"
            animate="in"
            exit="out"
            variants={pageVariants}
            transition={pageTransition}
            className="min-h-screen"
          >
            {children}
          </motion.div>
        </main>
      </div>
      
      <Footer />
    </div>
  );
};

export default Layout;
