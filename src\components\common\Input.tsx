import React, { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { ExclamationCircleIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  showPasswordToggle?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>(({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  variant = 'default',
  size = 'md',
  fullWidth = true,
  showPasswordToggle = false,
  className = '',
  type = 'text',
  ...props
}, ref) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [isFocused, setIsFocused] = React.useState(false);
  
  const inputType = showPasswordToggle && type === 'password' 
    ? (showPassword ? 'text' : 'password') 
    : type;
  
  const baseClasses = 'block w-full rounded-lg border transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-1';
  
  const variantClasses = {
    default: `border-gray-300 bg-white focus:border-primary-500 focus:ring-primary-500 ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`,
    filled: `border-transparent bg-gray-100 focus:bg-white focus:border-primary-500 focus:ring-primary-500 ${error ? 'bg-red-50 focus:border-red-500 focus:ring-red-500' : ''}`,
    outline: `border-2 border-gray-300 bg-transparent focus:border-primary-500 focus:ring-primary-500 ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`
  };
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-5 py-4 text-lg'
  };
  
  const widthClass = fullWidth ? 'w-full' : '';
  
  const inputClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${widthClass} ${className}`;
  
  const hasLeftIcon = leftIcon;
  const hasRightIcon = rightIcon || showPasswordToggle || error;
  
  const paddingClasses = `${hasLeftIcon ? 'pl-12' : ''} ${hasRightIcon ? 'pr-12' : ''}`;
  
  return (
    <div className={fullWidth ? 'w-full' : ''}>
      {label && (
        <motion.label
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className={`block text-sm font-medium mb-2 transition-colors duration-300 ${
            error ? 'text-red-700' : isFocused ? 'text-primary-700' : 'text-gray-700'
          }`}
        >
          {label}
        </motion.label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className={`w-5 h-5 transition-colors duration-300 ${
              error ? 'text-red-500' : isFocused ? 'text-primary-500' : 'text-gray-400'
            }`}>
              {leftIcon}
            </div>
          </div>
        )}
        
        <motion.input
          ref={ref}
          type={inputType}
          className={`${inputClasses} ${paddingClasses}`}
          onFocus={(e) => {
            setIsFocused(true);
            props.onFocus?.(e);
          }}
          onBlur={(e) => {
            setIsFocused(false);
            props.onBlur?.(e);
          }}
          whileFocus={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
          {...props}
        />
        
        {(rightIcon || showPasswordToggle || error) && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            {error && (
              <ExclamationCircleIcon className="w-5 h-5 text-red-500" />
            )}
            {showPasswordToggle && !error && (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-300"
              >
                {showPassword ? (
                  <EyeSlashIcon className="w-5 h-5" />
                ) : (
                  <EyeIcon className="w-5 h-5" />
                )}
              </button>
            )}
            {rightIcon && !error && !showPasswordToggle && (
              <div className={`w-5 h-5 transition-colors duration-300 ${
                isFocused ? 'text-primary-500' : 'text-gray-400'
              }`}>
                {rightIcon}
              </div>
            )}
          </div>
        )}
      </div>
      
      {(error || helperText) && (
        <motion.div
          initial={{ opacity: 0, y: -5 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-2"
        >
          {error ? (
            <p className="text-sm text-red-600 flex items-center">
              <ExclamationCircleIcon className="w-4 h-4 mr-1 flex-shrink-0" />
              {error}
            </p>
          ) : helperText ? (
            <p className="text-sm text-gray-500">{helperText}</p>
          ) : null}
        </motion.div>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
