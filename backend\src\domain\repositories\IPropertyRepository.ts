/**
 * Property Repository Interface
 * Defines the contract for property data access operations
 */

import { Property, PropertyType, ListingType, PropertyStatus } from '../entities/Property';
import { PaginatedResult, PaginationOptions } from './IUserRepository';

export interface PropertySearchCriteria {
  ownerId?: string;
  agentId?: string;
  propertyType?: PropertyType;
  listingType?: ListingType;
  status?: PropertyStatus;
  city?: string;
  province?: string;
  country?: string;
  priceMin?: number;
  priceMax?: number;
  areaMin?: number;
  areaMax?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  tags?: string[];
  searchTerm?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
    radius: number; // in kilometers
  };
  createdAfter?: Date;
  createdBefore?: Date;
  publishedAfter?: Date;
  publishedBefore?: Date;
}

export interface PropertySortOptions {
  field: 'createdAt' | 'updatedAt' | 'publishedAt' | 'price' | 'area' | 'views' | 'favorites';
  direction: 'ASC' | 'DESC';
}

export interface PropertyAggregation {
  byType: Record<PropertyType, number>;
  byListingType: Record<ListingType, number>;
  byStatus: Record<PropertyStatus, number>;
  byCity: Record<string, number>;
  byProvince: Record<string, number>;
  priceRanges: {
    range: string;
    count: number;
    averagePrice: number;
  }[];
  areaRanges: {
    range: string;
    count: number;
    averageArea: number;
  }[];
}

export interface MarketAnalytics {
  averagePrice: number;
  medianPrice: number;
  pricePerSqm: number;
  totalListings: number;
  newListings: number;
  soldProperties: number;
  rentedProperties: number;
  averageDaysOnMarket: number;
  priceGrowth: number; // percentage
  demandScore: number;
  supplyScore: number;
}

export interface IPropertyRepository {
  /**
   * Create a new property
   */
  create(property: Property): Promise<Property>;

  /**
   * Find property by ID
   */
  findById(id: string): Promise<Property | null>;

  /**
   * Find multiple properties by IDs
   */
  findByIds(ids: string[]): Promise<Property[]>;

  /**
   * Update property
   */
  update(property: Property): Promise<Property>;

  /**
   * Delete property (hard delete)
   */
  delete(id: string): Promise<void>;

  /**
   * Soft delete property
   */
  softDelete(id: string): Promise<void>;

  /**
   * Search properties with criteria
   */
  search(
    criteria: PropertySearchCriteria,
    sort?: PropertySortOptions,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<Property>>;

  /**
   * Count properties by criteria
   */
  count(criteria?: PropertySearchCriteria): Promise<number>;

  /**
   * Find properties by owner
   */
  findByOwner(ownerId: string): Promise<Property[]>;

  /**
   * Find properties by agent
   */
  findByAgent(agentId: string): Promise<Property[]>;

  /**
   * Find properties by status
   */
  findByStatus(status: PropertyStatus): Promise<Property[]>;

  /**
   * Find properties by type
   */
  findByType(type: PropertyType): Promise<Property[]>;

  /**
   * Find properties by listing type
   */
  findByListingType(type: ListingType): Promise<Property[]>;

  /**
   * Find properties in location
   */
  findByLocation(city: string, province?: string, country?: string): Promise<Property[]>;

  /**
   * Find properties within radius
   */
  findNearby(latitude: number, longitude: number, radiusKm: number): Promise<Property[]>;

  /**
   * Find properties by price range
   */
  findByPriceRange(min: number, max: number): Promise<Property[]>;

  /**
   * Find properties by area range
   */
  findByAreaRange(min: number, max: number): Promise<Property[]>;

  /**
   * Find featured properties
   */
  findFeatured(limit?: number): Promise<Property[]>;

  /**
   * Find recently added properties
   */
  findRecent(limit?: number): Promise<Property[]>;

  /**
   * Find most viewed properties
   */
  findMostViewed(limit?: number): Promise<Property[]>;

  /**
   * Find most favorited properties
   */
  findMostFavorited(limit?: number): Promise<Property[]>;

  /**
   * Find similar properties
   */
  findSimilar(propertyId: string, limit?: number): Promise<Property[]>;

  /**
   * Find properties with pending approval
   */
  findPendingApproval(): Promise<Property[]>;

  /**
   * Find expired properties
   */
  findExpired(): Promise<Property[]>;

  /**
   * Get property aggregations
   */
  getAggregations(criteria?: PropertySearchCriteria): Promise<PropertyAggregation>;

  /**
   * Get market analytics
   */
  getMarketAnalytics(
    city?: string,
    province?: string,
    propertyType?: PropertyType,
    listingType?: ListingType
  ): Promise<MarketAnalytics>;

  /**
   * Update property views
   */
  incrementViews(id: string): Promise<void>;

  /**
   * Update property favorites
   */
  incrementFavorites(id: string): Promise<void>;

  /**
   * Update property inquiries
   */
  incrementInquiries(id: string): Promise<void>;

  /**
   * Bulk update properties
   */
  bulkUpdate(updates: { id: string; data: Partial<Property> }[]): Promise<void>;

  /**
   * Bulk approve properties
   */
  bulkApprove(ids: string[]): Promise<void>;

  /**
   * Bulk reject properties
   */
  bulkReject(ids: string[]): Promise<void>;

  /**
   * Find properties by amenities
   */
  findByAmenities(amenities: string[]): Promise<Property[]>;

  /**
   * Find properties by tags
   */
  findByTags(tags: string[]): Promise<Property[]>;

  /**
   * Get property statistics
   */
  getStatistics(): Promise<{
    total: number;
    byType: Record<PropertyType, number>;
    byListingType: Record<ListingType, number>;
    byStatus: Record<PropertyStatus, number>;
    averagePrice: number;
    totalViews: number;
    newThisMonth: number;
    soldThisMonth: number;
    rentedThisMonth: number;
  }>;

  /**
   * Find properties with images
   */
  findWithImages(): Promise<Property[]>;

  /**
   * Find properties without images
   */
  findWithoutImages(): Promise<Property[]>;

  /**
   * Find properties with virtual tours
   */
  findWithVirtualTours(): Promise<Property[]>;

  /**
   * Search properties by text
   */
  searchByText(query: string, limit?: number): Promise<Property[]>;

  /**
   * Get price history for property
   */
  getPriceHistory(id: string): Promise<{
    date: Date;
    price: number;
    change: number;
    changePercent: number;
  }[]>;

  /**
   * Get property performance metrics
   */
  getPerformanceMetrics(id: string): Promise<{
    views: number;
    favorites: number;
    inquiries: number;
    shares: number;
    conversionRate: number;
    averageViewDuration: number;
    daysOnMarket: number;
    priceChanges: number;
  }>;

  /**
   * Find properties for sitemap
   */
  findForSitemap(): Promise<{ id: string; updatedAt: Date }[]>;

  /**
   * Export properties data
   */
  exportProperties(criteria?: PropertySearchCriteria): Promise<any[]>;

  /**
   * Find properties needing renewal
   */
  findNeedingRenewal(daysBeforeExpiry: number): Promise<Property[]>;

  /**
   * Get trending properties
   */
  findTrending(limit?: number): Promise<Property[]>;

  /**
   * Find properties by owner activity
   */
  findByOwnerActivity(ownerId: string, active: boolean): Promise<Property[]>;
}
