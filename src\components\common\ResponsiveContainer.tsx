import React from 'react';
import { motion } from 'framer-motion';
import { useResponsive } from '../../hooks/useResponsive';

interface ResponsiveContainerProps {
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  center?: boolean;
  animate?: boolean;
}

const ResponsiveContainer: React.FC<ResponsiveContainerProps> = ({
  children,
  className = '',
  maxWidth = 'xl',
  padding = 'md',
  center = true,
  animate = false
}) => {
  const { isMobile, isTablet, isDesktop } = useResponsive();

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    '3xl': 'max-w-3xl',
    full: 'max-w-full'
  };

  const paddingClasses = {
    none: '',
    sm: 'px-2 sm:px-4',
    md: 'px-4 sm:px-6 lg:px-8',
    lg: 'px-6 sm:px-8 lg:px-12',
    xl: 'px-8 sm:px-12 lg:px-16'
  };

  const containerClasses = `
    ${maxWidthClasses[maxWidth]}
    ${paddingClasses[padding]}
    ${center ? 'mx-auto' : ''}
    ${className}
  `;

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut'
      }
    }
  };

  if (animate) {
    return (
      <motion.div
        className={containerClasses}
        initial="hidden"
        animate="visible"
        variants={containerVariants}
      >
        {children}
      </motion.div>
    );
  }

  return <div className={containerClasses}>{children}</div>;
};

// Responsive Grid Component
interface ResponsiveGridProps {
  children: React.ReactNode;
  cols?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md',
  className = ''
}) => {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };

  const gridClasses = `
    grid
    grid-cols-${cols.mobile || 1}
    sm:grid-cols-${cols.tablet || 2}
    lg:grid-cols-${cols.desktop || 3}
    ${gapClasses[gap]}
    ${className}
  `;

  return <div className={gridClasses}>{children}</div>;
};

// Responsive Text Component
interface ResponsiveTextProps {
  children: React.ReactNode;
  size?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  weight?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
}

export const ResponsiveText: React.FC<ResponsiveTextProps> = ({
  children,
  size = { mobile: 'text-base', tablet: 'text-lg', desktop: 'text-xl' },
  weight = { mobile: 'font-normal', tablet: 'font-normal', desktop: 'font-normal' },
  className = '',
  as: Component = 'div'
}) => {
  const textClasses = `
    ${size.mobile}
    ${size.tablet ? `sm:${size.tablet}` : ''}
    ${size.desktop ? `lg:${size.desktop}` : ''}
    ${weight.mobile}
    ${weight.tablet ? `sm:${weight.tablet}` : ''}
    ${weight.desktop ? `lg:${weight.desktop}` : ''}
    ${className}
  `;

  return <Component className={textClasses}>{children}</Component>;
};

// Responsive Image Component
interface ResponsiveImageProps {
  src: string;
  alt: string;
  sizes?: {
    mobile?: string;
    tablet?: string;
    desktop?: string;
  };
  className?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
  loading?: 'lazy' | 'eager';
  placeholder?: string;
}

export const ResponsiveImage: React.FC<ResponsiveImageProps> = ({
  src,
  alt,
  sizes = { mobile: 'w-full', tablet: 'w-full', desktop: 'w-full' },
  className = '',
  objectFit = 'cover',
  loading = 'lazy',
  placeholder
}) => {
  const [imageLoaded, setImageLoaded] = React.useState(false);
  const [imageError, setImageError] = React.useState(false);

  const imageClasses = `
    ${sizes.mobile}
    ${sizes.tablet ? `sm:${sizes.tablet}` : ''}
    ${sizes.desktop ? `lg:${sizes.desktop}` : ''}
    object-${objectFit}
    transition-opacity duration-300
    ${imageLoaded ? 'opacity-100' : 'opacity-0'}
    ${className}
  `;

  return (
    <div className="relative overflow-hidden">
      {placeholder && !imageLoaded && !imageError && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="text-gray-400 text-sm">Loading...</div>
        </div>
      )}
      
      {!imageError ? (
        <img
          src={src}
          alt={alt}
          className={imageClasses}
          loading={loading}
          onLoad={() => setImageLoaded(true)}
          onError={() => setImageError(true)}
        />
      ) : (
        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
          <div className="text-gray-400 text-sm">Failed to load image</div>
        </div>
      )}
    </div>
  );
};

// Responsive Stack Component
interface ResponsiveStackProps {
  children: React.ReactNode;
  direction?: {
    mobile?: 'row' | 'col';
    tablet?: 'row' | 'col';
    desktop?: 'row' | 'col';
  };
  spacing?: 'sm' | 'md' | 'lg' | 'xl';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';
  className?: string;
}

export const ResponsiveStack: React.FC<ResponsiveStackProps> = ({
  children,
  direction = { mobile: 'col', tablet: 'row', desktop: 'row' },
  spacing = 'md',
  align = 'start',
  justify = 'start',
  className = ''
}) => {
  const spacingClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6',
    xl: 'gap-8'
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around',
    evenly: 'justify-evenly'
  };

  const stackClasses = `
    flex
    flex-${direction.mobile}
    ${direction.tablet ? `sm:flex-${direction.tablet}` : ''}
    ${direction.desktop ? `lg:flex-${direction.desktop}` : ''}
    ${spacingClasses[spacing]}
    ${alignClasses[align]}
    ${justifyClasses[justify]}
    ${className}
  `;

  return <div className={stackClasses}>{children}</div>;
};

// Responsive Show/Hide Component
interface ResponsiveVisibilityProps {
  children: React.ReactNode;
  show?: {
    mobile?: boolean;
    tablet?: boolean;
    desktop?: boolean;
  };
  className?: string;
}

export const ResponsiveVisibility: React.FC<ResponsiveVisibilityProps> = ({
  children,
  show = { mobile: true, tablet: true, desktop: true },
  className = ''
}) => {
  const visibilityClasses = `
    ${show.mobile ? 'block' : 'hidden'}
    ${show.tablet !== undefined ? (show.tablet ? 'sm:block' : 'sm:hidden') : ''}
    ${show.desktop !== undefined ? (show.desktop ? 'lg:block' : 'lg:hidden') : ''}
    ${className}
  `;

  return <div className={visibilityClasses}>{children}</div>;
};

export default ResponsiveContainer;
