const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testPropertyAuth() {
  console.log('🧪 Testing Property Authentication...\n');

  try {
    // Test 1: Try to create property without authentication
    console.log('1. Testing property creation without authentication...');
    try {
      await axios.post(`${BASE_URL}/properties`, {
        title: 'Test Property',
        description: 'A test property',
        price: 100000,
        category: 'SALE',
        type: 'HOUSE',
        province: 'Kabul',
        city: 'Kabul',
        address: 'Test Address',
        area: 100
      });
      console.log('❌ ERROR: Property creation should have failed without auth');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Property creation correctly blocked without authentication');
      } else {
        console.log('❌ Unexpected error:', error.response?.data?.message || error.message);
      }
    }

    // Test 2: Test empty properties endpoint
    console.log('\n2. Testing empty properties endpoint...');
    try {
      const response = await axios.get(`${BASE_URL}/properties`);
      console.log('✅ Properties endpoint accessible');
      console.log(`📊 Found ${response.data.data.length} properties`);
      console.log(`📄 Response message: ${response.data.message || 'No message'}`);
    } catch (error) {
      console.log('❌ Properties endpoint error:', error.response?.data?.message || error.message);
    }

    // Test 3: Test favorites without authentication
    console.log('\n3. Testing favorites without authentication...');
    try {
      await axios.get(`${BASE_URL}/users/favorites`);
      console.log('❌ ERROR: Favorites should require authentication');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Favorites correctly blocked without authentication');
      } else {
        console.log('❌ Unexpected error:', error.response?.data?.message || error.message);
      }
    }

    console.log('\n🎉 Authentication tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testPropertyAuth();
