import { Request, Response, NextFunction } from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { AppError, catchAsync } from '../middleware/errorHandler';

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = process.env.UPLOAD_PATH || './uploads';
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, file.fieldname + '-' + uniqueSuffix + extension);
  }
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  const allowedTypes = process.env.ALLOWED_FILE_TYPES?.split(',') || [
    'image/jpeg',
    'image/png',
    'image/webp',
    'image/gif'
  ];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only images are allowed.'));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE || '10485760') // 10MB default
  }
});

export const uploadSingle = upload.single('image');
export const uploadMultiple = upload.array('images', 10);

export const uploadImage = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  if (!req.file) {
    return next(new AppError('No file uploaded', 400));
  }

  const baseUrl = `${req.protocol}://${req.get('host')}`;
  const fileUrl = `${baseUrl}/uploads/${req.file.filename}`;

  res.status(201).json({
    success: true,
    data: {
      filename: req.file.filename,
      originalName: req.file.originalname,
      url: fileUrl,
      size: req.file.size,
      mimeType: req.file.mimetype
    },
    message: 'File uploaded successfully'
  });
});

export const uploadImages = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const files = req.files as Express.Multer.File[];
  
  if (!files || files.length === 0) {
    return next(new AppError('No files uploaded', 400));
  }

  const baseUrl = `${req.protocol}://${req.get('host')}`;
  
  const uploadedFiles = files.map(file => ({
    filename: file.filename,
    originalName: file.originalname,
    url: `${baseUrl}/uploads/${file.filename}`,
    size: file.size,
    mimeType: file.mimetype
  }));

  res.status(201).json({
    success: true,
    data: uploadedFiles,
    message: `${files.length} files uploaded successfully`
  });
});

export const deleteImage = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { filename } = req.params;
  
  if (!filename) {
    return next(new AppError('Filename is required', 400));
  }

  const uploadPath = process.env.UPLOAD_PATH || './uploads';
  const filePath = path.join(uploadPath, filename);

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    return next(new AppError('File not found', 404));
  }

  try {
    // Delete file
    fs.unlinkSync(filePath);
    
    res.json({
      success: true,
      message: 'File deleted successfully'
    });
  } catch (error) {
    return next(new AppError('Failed to delete file', 500));
  }
});

// Middleware to handle multer errors
export const handleUploadError = (error: any, req: Request, res: Response, next: NextFunction) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return next(new AppError('File too large', 400));
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return next(new AppError('Too many files', 400));
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return next(new AppError('Unexpected field name', 400));
    }
  }
  
  if (error.message === 'Invalid file type. Only images are allowed.') {
    return next(new AppError(error.message, 400));
  }
  
  next(error);
};
