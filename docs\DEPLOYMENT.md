# 🚀 Deployment Guide

This guide covers deploying the Real Estate Platform to various environments.

## 📋 Prerequisites

- Node.js 18+ installed
- MongoDB database (local or cloud)
- Domain name (for production)
- SSL certificate (for production)

## 🏗️ Build Process

### Frontend Build
```bash
# Install dependencies
npm install

# Build for production
npm run build

# Preview build (optional)
npm run preview
```

### Backend Build
```bash
cd backend

# Install dependencies
npm install

# Build TypeScript
npm run build

# The compiled files will be in backend/dist/
```

## 🌐 Production Deployment

### Option 1: Traditional VPS/Server

#### 1. Server Setup
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Install PM2 for process management
sudo npm install -g pm2

# Install Nginx
sudo apt install nginx -y
```

#### 2. Application Deployment
```bash
# Clone repository
git clone <your-repo-url>
cd realestate

# Install and build frontend
npm install
npm run build

# Install and build backend
cd backend
npm install
npm run build
cd ..

# Create uploads directory
mkdir -p backend/uploads

# Set up environment variables
cp backend/.env.example backend/.env
# Edit backend/.env with production values

# Start backend with PM2
cd backend
pm2 start dist/index.js --name "realestate-api"
pm2 save
pm2 startup
```

#### 3. Nginx Configuration
```nginx
# /etc/nginx/sites-available/realestate
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;

    # Frontend
    location / {
        root /path/to/realestate/dist;
        try_files $uri $uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
    }

    # Backend API
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # File uploads
    location /uploads {
        alias /path/to/realestate/backend/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 4. SSL Setup with Let's Encrypt
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal (already set up by certbot)
sudo certbot renew --dry-run
```

### Option 2: Docker Deployment

#### 1. Create Dockerfiles

**Frontend Dockerfile:**
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

**Backend Dockerfile:**
```dockerfile
# backend/Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001
CMD ["node", "dist/index.js"]
```

#### 2. Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build: .
    ports:
      - "80:80"
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/realestate_db
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongo
    volumes:
      - ./backend/uploads:/app/uploads

  mongo:
    image: mongo:6.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_PASSWORD}

volumes:
  mongo_data:
```

#### 3. Deploy with Docker
```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Option 3: Cloud Platforms

#### Vercel (Frontend)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Set environment variables in Vercel dashboard
# VITE_API_URL=https://your-backend-url.com/api
```

#### Railway/Render (Backend)
1. Connect your GitHub repository
2. Set environment variables:
   - `NODE_ENV=production`
   - `MONGODB_URI=your-mongodb-connection-string`
   - `JWT_SECRET=your-jwt-secret`
3. Deploy automatically on push

#### MongoDB Atlas (Database)
1. Create cluster at https://cloud.mongodb.com
2. Create database user
3. Whitelist IP addresses
4. Get connection string
5. Update `MONGODB_URI` in environment variables

## 🔧 Environment Variables

### Production Environment Variables

**Frontend (.env.production):**
```env
VITE_API_URL=https://api.yourdomain.com/api
VITE_APP_NAME=Real Estate Platform
VITE_GOOGLE_MAPS_API_KEY=your-production-maps-key
VITE_GOOGLE_ANALYTICS_ID=your-ga-id
```

**Backend (.env):**
```env
NODE_ENV=production
PORT=3001
MONGODB_URI=********************************:port/database
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-token-secret
JWT_REFRESH_EXPIRES_IN=30d
BCRYPT_ROUNDS=12

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Email (choose one)
SENDGRID_API_KEY=your-sendgrid-key
SENDGRID_FROM_EMAIL=<EMAIL>

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## 🔒 Security Checklist

- [ ] Use HTTPS in production
- [ ] Set secure JWT secrets
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Use environment variables for secrets
- [ ] Enable MongoDB authentication
- [ ] Set up firewall rules
- [ ] Regular security updates
- [ ] Monitor logs for suspicious activity
- [ ] Backup database regularly

## 📊 Monitoring & Maintenance

### PM2 Monitoring
```bash
# View running processes
pm2 list

# View logs
pm2 logs realestate-api

# Restart application
pm2 restart realestate-api

# Monitor resources
pm2 monit
```

### Database Backup
```bash
# Create backup
mongodump --uri="********************************:port/database" --out=/backup/$(date +%Y%m%d)

# Restore backup
mongorestore --uri="********************************:port/database" /backup/20231201
```

### Log Rotation
```bash
# Set up logrotate for application logs
sudo nano /etc/logrotate.d/realestate

# Add configuration:
/path/to/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload realestate-api
    endscript
}
```

## 🚨 Troubleshooting

### Common Issues

1. **Frontend not loading:**
   - Check Nginx configuration
   - Verify build files exist
   - Check browser console for errors

2. **API not responding:**
   - Check PM2 status: `pm2 list`
   - View logs: `pm2 logs realestate-api`
   - Check MongoDB connection

3. **File uploads failing:**
   - Check uploads directory permissions
   - Verify file size limits
   - Check disk space

4. **Database connection issues:**
   - Verify MongoDB is running
   - Check connection string
   - Verify network access

### Performance Optimization

1. **Enable Gzip compression in Nginx**
2. **Set up CDN for static assets**
3. **Optimize images before upload**
4. **Use database indexing**
5. **Implement caching strategies**
6. **Monitor and optimize queries**

## 📞 Support

For deployment issues:
1. Check logs first
2. Review this documentation
3. Search existing issues
4. Create new issue with logs and environment details
