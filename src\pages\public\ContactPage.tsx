import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  BuildingOfficeIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    setIsSubmitted(true);
    setIsSubmitting(false);
    
    // Reset form after submission
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
    
    // Reset success message after 5 seconds
    setTimeout(() => {
      setIsSubmitted(false);
    }, 5000);
  };

  const officeLocations = [
    {
      city: 'Kabul',
      address: 'District 10, Shar-e-Naw',
      phone: '+93 70 123 4567',
      email: '<EMAIL>',
      hours: 'Mon-Fri: 9am-5pm, Sat: 10am-2pm',
      image: 'https://images.unsplash.com/photo-1528493366314-e317cd9fc9a0?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'
    },
    {
      city: 'Herat',
      address: 'Badmurghan, Near Herat University',
      phone: '+93 79 876 5432',
      email: '<EMAIL>',
      hours: 'Mon-Fri: 9am-5pm, Sat: 10am-2pm',
      image: 'https://images.unsplash.com/photo-1582650625119-3a31f8fa2699?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'
    },
    {
      city: 'Mazar-i-Sharif',
      address: 'Karte Ariana, Near Blue Mosque',
      phone: '+93 72 345 6789',
      email: '<EMAIL>',
      hours: 'Mon-Fri: 9am-5pm, Sat: 10am-2pm',
      image: 'https://images.unsplash.com/photo-*************-d2a4734c5490?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'
    }
  ];

  const faqItems = [
    {
      question: 'How can I list my property on your platform?',
      answer: 'You can list your property by creating an account and clicking on "Post Property" in the navigation menu. Follow the step-by-step process to add details, photos, and set your price.'
    },
    {
      question: 'What are the fees for listing a property?',
      answer: 'Basic listings are free for individual property owners. Premium listings with enhanced visibility have a small fee. Real estate agencies can contact us for special package rates.'
    },
    {
      question: 'How long does it take to get my property approved?',
      answer: 'Most properties are reviewed and approved within 24-48 hours. We verify all listings to ensure quality and accuracy before they go live on our platform.'
    },
    {
      question: 'Can I edit my listing after it\'s published?',
      answer: 'Yes, you can edit your listing at any time through your dashboard. Updates will be visible immediately, though major changes may require a brief review.'
    }
  ];

  const contactMethods = [
    {
      title: 'Call Us',
      description: 'Speak directly with our customer support team',
      icon: PhoneIcon,
      contact: '+93 70 123 4567',
      action: 'Call now',
      color: 'primary'
    },
    {
      title: 'Email Us',
      description: 'Send us an email and we\'ll respond within 24 hours',
      icon: EnvelopeIcon,
      contact: '<EMAIL>',
      action: 'Send email',
      color: 'secondary'
    },
    {
      title: 'Live Chat',
      description: 'Chat with our support team in real-time',
      icon: ChatBubbleLeftRightIcon,
      contact: 'Available 9am-6pm',
      action: 'Start chat',
      color: 'accent'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-primary py-24 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Get In Touch
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
              We're here to help with all your real estate needs
            </p>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float hidden lg:block" />
        <div className="absolute bottom-20 right-10 w-16 h-16 bg-white/10 rounded-full animate-float hidden lg:block" />
      </section>

      {/* Contact Methods */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {contactMethods.map((method, index) => {
              const Icon = method.icon;
              const colorClasses = {
                primary: 'bg-primary-500 text-white',
                secondary: 'bg-secondary-500 text-white', 
                accent: 'bg-accent-500 text-white'
              };
              
              return (
                <div 
                  key={method.title}
                  className="bg-white rounded-xl shadow-soft p-8 text-center group animate-slide-up hover:shadow-medium transition-all duration-300"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className={`w-16 h-16 ${colorClasses[method.color as keyof typeof colorClasses]} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {method.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {method.description}
                  </p>
                  <div className="text-lg font-bold text-primary-600 mb-6">
                    {method.contact}
                  </div>
                  <button className="btn-primary w-full">
                    {method.action}
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* Form */}
            <div className="bg-white rounded-xl shadow-soft p-8 animate-slide-right">
              <h2 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-6">
                Send Us a Message
              </h2>
              
              {isSubmitted ? (
                <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center animate-scale-in">
                  <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Message Sent!</h3>
                  <p className="text-gray-600 mb-0">Thank you for contacting us. We'll get back to you shortly.</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="name">
                        Your Name *
                      </label>
                      <input
                        id="name"
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                        placeholder="Enter your name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="email">
                        Email Address *
                      </label>
                      <input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        required
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="phone">
                      Phone Number
                    </label>
                    <input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                      placeholder="+93 70 123 4567"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="subject">
                      Subject *
                    </label>
                    <input
                      id="subject"
                      type="text"
                      value={formData.subject}
                      onChange={(e) => handleInputChange('subject', e.target.value)}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                      placeholder="What is your message about?"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2" htmlFor="message">
                      Your Message *
                    </label>
                    <textarea
                      id="message"
                      rows={5}
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                      placeholder="Type your message here..."
                    />
                  </div>
                  
                  <div>
                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="btn-primary w-full flex items-center justify-center"
                    >
                      {isSubmitting ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Sending...
                        </>
                      ) : (
                        'Send Message'
                      )}
                    </button>
                  </div>
                </form>
              )}
            </div>
            
            {/* Contact Tabs */}
            <div className="animate-slide-left">
              <div className="bg-white rounded-xl shadow-soft overflow-hidden">
                <div className="flex border-b border-gray-200">
                  <button
                    onClick={() => setActiveTab('general')}
                    className={`flex-1 py-4 px-6 text-center font-medium ${activeTab === 'general' ? 'text-primary-600 border-b-2 border-primary-500' : 'text-gray-500 hover:text-gray-700'}`}
                  >
                    General Inquiries
                  </button>
                  <button
                    onClick={() => setActiveTab('support')}
                    className={`flex-1 py-4 px-6 text-center font-medium ${activeTab === 'support' ? 'text-primary-600 border-b-2 border-primary-500' : 'text-gray-500 hover:text-gray-700'}`}
                  >
                    Support
                  </button>
                  <button
                    onClick={() => setActiveTab('business')}
                    className={`flex-1 py-4 px-6 text-center font-medium ${activeTab === 'business' ? 'text-primary-600 border-b-2 border-primary-500' : 'text-gray-500 hover:text-gray-700'}`}
                  >
                    Business
                  </button>
                </div>
                
                <div className="p-6">
                  {activeTab === 'general' && (
                    <div className="animate-fade-in">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">General Inquiries</h3>
                      <p className="text-gray-600 mb-6">For general questions about our platform, properties, or services.</p>
                      
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <EnvelopeIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Email</p>
                            <p className="text-sm text-gray-600"><EMAIL></p>
                          </div>
                        </div>
                        
                        <div className="flex items-start">
                          <PhoneIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Phone</p>
                            <p className="text-sm text-gray-600">+93 70 123 4567</p>
                          </div>
                        </div>
                        
                        <div className="flex items-start">
                          <BuildingOfficeIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Hours</p>
                            <p className="text-sm text-gray-600">Monday-Friday: 9am-5pm</p>
                            <p className="text-sm text-gray-600">Saturday: 10am-2pm</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {activeTab === 'support' && (
                    <div className="animate-fade-in">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Technical Support</h3>
                      <p className="text-gray-600 mb-6">For assistance with your account, listings, or technical issues.</p>
                      
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <EnvelopeIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Email</p>
                            <p className="text-sm text-gray-600"><EMAIL></p>
                          </div>
                        </div>
                        
                        <div className="flex items-start">
                          <PhoneIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Support Line</p>
                            <p className="text-sm text-gray-600">+93 70 123 4569</p>
                          </div>
                        </div>
                        
                        <div className="flex items-start">
                          <ChatBubbleLeftRightIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Live Chat</p>
                            <p className="text-sm text-gray-600">Available 9am-6pm, Monday-Friday</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {activeTab === 'business' && (
                    <div className="animate-fade-in">
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Business Development</h3>
                      <p className="text-gray-600 mb-6">For partnerships, advertising, or business opportunities.</p>
                      
                      <div className="space-y-4">
                        <div className="flex items-start">
                          <EnvelopeIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Email</p>
                            <p className="text-sm text-gray-600"><EMAIL></p>
                          </div>
                        </div>
                        
                        <div className="flex items-start">
                          <PhoneIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Business Line</p>
                            <p className="text-sm text-gray-600">+93 70 123 4570</p>
                          </div>
                        </div>
                        
                        <div className="flex items-start">
                          <BuildingOfficeIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                          <div className="ml-3">
                            <p className="text-sm font-medium text-gray-900">Business Hours</p>
                            <p className="text-sm text-gray-600">Monday-Friday: 9am-5pm</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Office Locations */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Offices
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Visit us at one of our locations across Afghanistan
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {officeLocations.map((office, index) => (
              <div 
                key={office.city}
                className="bg-white rounded-xl shadow-soft overflow-hidden group animate-slide-up hover:shadow-medium transition-all duration-300"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="aspect-w-16 aspect-h-9 overflow-hidden">
                  <img 
                    src={office.image} 
                    alt={`${office.city} office`}
                    className="object-cover w-full h-full group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {office.city} Office
                  </h3>
                  
                  <div className="space-y-3 mb-6">
                    <div className="flex items-start">
                      <MapPinIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                      <div className="ml-3">
                        <p className="text-sm text-gray-600">{office.address}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <PhoneIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                      <div className="ml-3">
                        <p className="text-sm text-gray-600">{office.phone}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <EnvelopeIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                      <div className="ml-3">
                        <p className="text-sm text-gray-600">{office.email}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <BuildingOfficeIcon className="w-5 h-5 text-primary-500 mt-1 flex-shrink-0" />
                      <div className="ml-3">
                        <p className="text-sm text-gray-600">{office.hours}</p>
                      </div>
                    </div>
                  </div>
                  
                  <button className="btn-outline w-full">
                    Get Directions
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Find answers to common questions about our services
            </p>
          </div>

          <div className="space-y-6 animate-slide-up">
            {faqItems.map((item, index) => (
              <div 
                key={index}
                className="bg-white rounded-xl shadow-soft p-6 hover:shadow-medium transition-all duration-300"
              >
                <div className="flex items-start">
                  <QuestionMarkCircleIcon className="w-6 h-6 text-primary-500 mt-1 flex-shrink-0" />
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{item.question}</h3>
                    <p className="text-gray-600">{item.answer}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-6">Still have questions?</p>
            <button className="btn-primary">
              Contact Our Support Team
            </button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6">
              Ready to Find Your Dream Property?
            </h2>
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Browse our extensive collection of properties or list your own today
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <Link to="/listings" className="bg-white text-primary-500 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105">
                Browse Properties
              </Link>
              <Link to="/post-property" className="border-2 border-white text-white hover:bg-white hover:text-primary-500 font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105">
                List Your Property
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;