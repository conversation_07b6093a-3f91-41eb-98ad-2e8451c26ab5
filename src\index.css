/* Import all external styles first */
@import "./styles/rtl.css";
@import './styles/mobile.css';

/* Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: "Inter", system-ui, -apple-system, sans-serif;
    line-height: 1.6;
  }

  /* RTL Support for Arabic languages */
  [dir="rtl"] {
    font-family: "Noto Sans Arabic", system-ui, sans-serif;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f5f9;
  }

  ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}

/* Custom component styles */
@layer components {
  /* Button Components */
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-colored focus:outline-none focus:ring-4 focus:ring-primary-200;
  }

  .btn-secondary {
    @apply bg-secondary-500 hover:bg-secondary-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-medium focus:outline-none focus:ring-4 focus:ring-secondary-200;
  }

  .btn-accent {
    @apply bg-accent-500 hover:bg-accent-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-medium focus:outline-none focus:ring-4 focus:ring-accent-200;
  }

  .btn-outline {
    @apply border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary-200;
  }

  .btn-ghost {
    @apply text-gray-700 hover:text-primary-500 hover:bg-primary-50 font-semibold py-3 px-6 rounded-lg transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-primary-200;
  }

  /* Compact button styles for navbar */
  .btn-nav {
    @apply flex items-center space-x-1.5 px-2.5 py-1.5 text-sm font-medium rounded-md transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-200;
  }

  .btn-nav-primary {
    @apply btn-nav text-white bg-primary-500 hover:bg-primary-600 shadow-sm hover:shadow-md;
  }

  .btn-nav-ghost {
    @apply btn-nav text-gray-600 hover:text-primary-600 hover:bg-primary-50;
  }

  /* Beautiful button animations */
  .btn-shimmer {
    @apply relative overflow-hidden;
  }

  .btn-shimmer::before {
    content: "";
    @apply absolute top-0 left-[-100%] w-full h-full bg-gradient-to-r from-transparent via-white/20 to-transparent transition-all duration-700;
  }

  .btn-shimmer:hover::before {
    @apply left-[100%];
  }

  .btn-glow {
    @apply transition-all duration-300;
  }

  .btn-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  /* Icon animations */
  .icon-bounce:hover {
    animation: iconBounce 0.6s ease-in-out;
  }

  @keyframes iconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }

  /* Notification badge pulse */
  .notification-pulse {
    animation: notificationPulse 2s infinite;
  }

  @keyframes notificationPulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.9; }
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 transform hover:-translate-y-1;
  }

  .card-hover {
    @apply hover:shadow-large hover:-translate-y-2;
  }

  /* Navigation */
  .nav-link {
    @apply text-gray-700 hover:text-primary-500 font-medium transition-colors duration-300 relative;
  }

  .nav-link::after {
    content: "";
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-500 to-secondary-500 transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  .nav-link.active {
    @apply text-primary-500;
  }

  .nav-link.active::after {
    @apply w-full;
  }

  /* Enhanced navbar styles */
  .navbar-glass {
    backdrop-filter: blur(20px);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(249, 250, 251, 0.95) 100%);
    border-bottom: 1px solid rgba(229, 231, 235, 0.8);
    position: relative;
  }

  .navbar-glass::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.3), transparent);
  }

  .nav-item {
    @apply relative overflow-hidden;
  }

  .nav-item::before {
    content: "";
    @apply absolute inset-0 bg-gradient-to-r from-primary-50 to-secondary-50 opacity-0 transition-opacity duration-300 rounded-md;
  }

  .nav-item:hover::before {
    @apply opacity-100;
  }

  .nav-item > * {
    @apply relative z-10;
  }
}

/* Custom utility classes */
@layer utilities {
  .text-gradient {
    background: linear-gradient(135deg, #3b82f6, #22c55e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .bg-gradient-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, #22c55e, #16a34a);
  }

  .bg-gradient-accent {
    background: linear-gradient(135deg, #f97316, #ea580c);
  }

  .bg-gradient-hero {
    background: linear-gradient(135deg, #3b82f6 0%, #22c55e 50%, #f97316 100%);
  }
}
