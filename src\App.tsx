import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';

// Components
import Header from './components/layout/Header';
import Footer from './components/layout/Footer';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';

// Public Pages
import HomePage from './pages/public/HomePage';
import AllListingsPage from './pages/public/AllListingsPage';
import PropertyDetailPage from './pages/public/PropertyDetailPage';
import PostPropertyPage from './pages/public/PostPropertyPage';
import LoginPage from './pages/public/LoginPage';
import RegisterPage from './pages/public/RegisterPage';
import LanguageDemoPage from './pages/public/LanguageDemoPage';
import AboutPage from './pages/public/AboutPage';
import ContactPage from './pages/public/ContactPage';

// Dashboard Pages
import DashboardPage from './pages/dashboard/DashboardPage';
import MyListingsPage from './pages/dashboard/MyListingsPage';
import MessagesPage from './pages/dashboard/MessagesPage';
import FavoritesPage from './pages/dashboard/FavoritesPage';

// Admin Pages
import AdminDashboard from './pages/admin/AdminDashboard';
import ManageUsers from './pages/admin/ManageUsers';
import ManageListings from './pages/admin/ManageListings';

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Header />
          <main>
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/listings" element={<AllListingsPage />} />
              <Route path="/property/:id" element={<PropertyDetailPage />} />
              <Route path="/post-property" element={<PostPropertyPage />} />
              <Route path="/login" element={<LoginPage />} />
              <Route path="/register" element={<RegisterPage />} />
              <Route path="/language-demo" element={<LanguageDemoPage />} />
              <Route path="/about" element={<AboutPage />} />
              <Route path="/contact" element={<ContactPage />} />

              {/* Dashboard Routes */}
              <Route path="/dashboard" element={<DashboardPage />} />
              <Route path="/dashboard/listings" element={<MyListingsPage />} />
              <Route path="/messages" element={<MessagesPage />} />
              <Route path="/favorites" element={<FavoritesPage />} />

              {/* Admin Routes */}
              <Route path="/admin" element={<AdminDashboard />} />
              <Route path="/admin/users" element={<ManageUsers />} />
              <Route path="/admin/properties" element={<ManageListings />} />
            </Routes>
          </main>
          <Footer />
        </div>
      </Router>
      </AuthProvider>

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        reverseOrder={false}
        gutter={8}
        containerClassName=""
        containerStyle={{}}
        toastOptions={{
          // Define default options
          className: '',
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
            borderRadius: '12px',
            fontSize: '14px',
            fontWeight: '500',
            padding: '12px 16px',
            maxWidth: '400px',
          },
          // Default options for specific types
          success: {
            duration: 3000,
            style: {
              background: '#10B981',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#10B981',
            },
          },
          error: {
            duration: 5000,
            style: {
              background: '#EF4444',
            },
            iconTheme: {
              primary: '#fff',
              secondary: '#EF4444',
            },
          },
        }}
      />
    </LanguageProvider>
  );
}

export default App
