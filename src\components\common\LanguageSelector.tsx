import React, { useState, useRef, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import type { Language } from '../../contexts/LanguageContext';
import {
  ChevronDownIcon,
  LanguageIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

interface LanguageOption {
  code: Language;
  name: string;
  nativeName: string;
  flag: string;
}

const languages: LanguageOption[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  },
  {
    code: 'ps',
    name: 'Pashto',
    nativeName: 'پښتو',
    flag: '🇦🇫'
  },
  {
    code: 'fa',
    name: 'Dari',
    nativeName: 'دری',
    flag: '🇦🇫'
  }
];

interface LanguageSelectorProps {
  variant?: 'default' | 'compact' | 'icon-only';
  className?: string;
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ 
  variant = 'default',
  className = '' 
}) => {
  const { currentLanguage, changeLanguage, t, isRTL } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const currentLanguageOption = languages.find(lang => lang.code === currentLanguage) || languages[0];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLanguageChange = (langCode: Language) => {
    changeLanguage(langCode);
    setIsOpen(false);
  };

  const getButtonContent = () => {
    switch (variant) {
      case 'icon-only':
        return (
          <div className="flex items-center">
            <LanguageIcon className="w-5 h-5" />
          </div>
        );
      case 'compact':
        return (
          <div className="flex items-center space-x-2">
            <span className="text-lg">{currentLanguageOption.flag}</span>
            <span className="text-sm font-medium">{currentLanguageOption.code.toUpperCase()}</span>
            <ChevronDownIcon className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        );
      default:
        return (
          <div className="flex items-center space-x-2">
            <span className="text-lg">{currentLanguageOption.flag}</span>
            <span className="text-sm font-medium hidden sm:block">
              {currentLanguageOption.nativeName}
            </span>
            <span className="text-sm font-medium sm:hidden">
              {currentLanguageOption.code.toUpperCase()}
            </span>
            <ChevronDownIcon className={`w-4 h-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
          </div>
        );
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          flex items-center px-3 py-2 rounded-lg border border-gray-300 
          bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 
          focus:ring-primary-200 focus:border-primary-500 
          transition-all duration-300
          ${variant === 'icon-only' ? 'p-2' : ''}
        `}
        aria-label="Select language"
      >
        {getButtonContent()}
      </button>

      {isOpen && (
        <div className={`
          absolute top-full mt-2 w-48 bg-white border border-gray-200 
          rounded-lg shadow-large z-50 py-1 animate-slide-down
          ${isRTL ? 'right-0' : 'left-0'}
        `}>
          {languages.map((lang) => (
            <button
              key={lang.code}
              onClick={() => handleLanguageChange(lang.code)}
              className={`
                w-full px-4 py-3 text-left hover:bg-gray-50 
                transition-colors duration-200 flex items-center 
                justify-between group
                ${currentLanguage === lang.code ? 'bg-primary-50 text-primary-700' : 'text-gray-700'}
              `}
            >
              <div className="flex items-center space-x-3">
                <span className="text-lg">{lang.flag}</span>
                <div className="flex flex-col">
                  <span className="text-sm font-medium">{lang.nativeName}</span>
                  <span className="text-xs text-gray-500">{lang.name}</span>
                </div>
              </div>
              
              {language === lang.code && (
                <CheckIcon className="w-4 h-4 text-primary-500" />
              )}
            </button>
          ))}
          
          {/* Language Info */}
          <div className="border-t border-gray-100 mt-1 pt-2 px-4 pb-2">
            <p className="text-xs text-gray-500">
              Direction: {isRTL ? 'RTL' : 'LTR'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
