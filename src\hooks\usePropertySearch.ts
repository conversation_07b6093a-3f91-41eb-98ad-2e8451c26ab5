import { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api';
import type { Property, PropertyFilters } from '../services/api';
import { toast } from 'react-hot-toast';

interface UsePropertySearchResult {
  properties: Property[];
  loading: boolean;
  error: string | null;
  totalCount: number;
  currentPage: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  filters: PropertyFilters;
  setFilters: (filters: Partial<PropertyFilters>) => void;
  updateFilter: (key: keyof PropertyFilters, value: any) => void;
  clearFilters: () => void;
  search: (query?: string) => void;
  loadMore: () => void;
  refresh: () => void;
}

const defaultFilters: PropertyFilters = {
  page: 1,
  limit: 20,
  sortBy: 'createdAt',
  sortOrder: 'desc'
};

export const usePropertySearch = (initialFilters?: Partial<PropertyFilters>): UsePropertySearchResult => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrev, setHasPrev] = useState(false);
  const [filters, setFiltersState] = useState<PropertyFilters>({
    ...defaultFilters,
    ...initialFilters
  });

  const fetchProperties = useCallback(async (searchFilters: PropertyFilters, append = false) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getProperties(searchFilters);
      const { data, meta } = response;

      if (append) {
        setProperties(prev => [...prev, ...data]);
      } else {
        setProperties(data);
      }

      if (meta) {
        setTotalCount(meta.total);
        setCurrentPage(meta.page);
        setTotalPages(meta.totalPages);
        setHasNext(meta.hasNext);
        setHasPrev(meta.hasPrev);
      }
    } catch (err: any) {
      const message = err.response?.data?.message || 'Failed to fetch properties';
      setError(message);
      // Only show error toast for actual errors, not empty results
      if (err.response?.status !== 200) {
        toast.error(message);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const setFilters = useCallback((newFilters: Partial<PropertyFilters>) => {
    const updatedFilters = { ...filters, ...newFilters, page: 1 };
    setFiltersState(updatedFilters);
  }, [filters]);

  const updateFilter = useCallback((key: keyof PropertyFilters, value: any) => {
    setFilters({ [key]: value });
  }, [setFilters]);

  const clearFilters = useCallback(() => {
    setFiltersState(defaultFilters);
  }, []);

  const search = useCallback(async (query?: string) => {
    if (query) {
      try {
        setLoading(true);
        setError(null);
        
        const response = await apiService.searchProperties(query);
        setProperties(response.data);
        setTotalCount(response.data.length);
        setCurrentPage(1);
        setTotalPages(1);
        setHasNext(false);
        setHasPrev(false);
      } catch (err: any) {
        const message = err.response?.data?.message || 'Search failed';
        setError(message);
        // Only show error toast for actual errors, not empty results
        if (err.response?.status !== 200) {
          toast.error(message);
        }
      } finally {
        setLoading(false);
      }
    } else {
      fetchProperties(filters);
    }
  }, [filters, fetchProperties]);

  const loadMore = useCallback(() => {
    if (hasNext && !loading) {
      const nextPageFilters = { ...filters, page: currentPage + 1 };
      fetchProperties(nextPageFilters, true);
    }
  }, [hasNext, loading, filters, currentPage, fetchProperties]);

  const refresh = useCallback(() => {
    fetchProperties(filters);
  }, [filters, fetchProperties]);

  // Fetch properties when filters change
  useEffect(() => {
    fetchProperties(filters);
  }, [filters, fetchProperties]);

  return {
    properties,
    loading,
    error,
    totalCount,
    currentPage,
    totalPages,
    hasNext,
    hasPrev,
    filters,
    setFilters,
    updateFilter,
    clearFilters,
    search,
    loadMore,
    refresh
  };
};

// Hook for managing favorites
export const useFavorites = () => {
  const [favorites, setFavorites] = useState<Property[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchFavorites = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiService.getFavorites();
      setFavorites(response.data);
    } catch (err: any) {
      const message = err.response?.data?.message || 'Failed to fetch favorites';
      // Only show error toast for actual errors, not empty results
      if (err.response?.status !== 200) {
        toast.error(message);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const addToFavorites = useCallback(async (propertyId: string) => {
    try {
      await apiService.addToFavorites(propertyId);
      toast.success('Added to favorites');
      fetchFavorites(); // Refresh favorites list
    } catch (err: any) {
      const message = err.response?.data?.message || 'Failed to add to favorites';
      toast.error(message);
    }
  }, [fetchFavorites]);

  const removeFromFavorites = useCallback(async (propertyId: string) => {
    try {
      await apiService.removeFromFavorites(propertyId);
      toast.success('Removed from favorites');
      fetchFavorites(); // Refresh favorites list
    } catch (err: any) {
      const message = err.response?.data?.message || 'Failed to remove from favorites';
      toast.error(message);
    }
  }, [fetchFavorites]);

  const toggleFavorite = useCallback(async (propertyId: string, isFavorited: boolean) => {
    if (isFavorited) {
      await removeFromFavorites(propertyId);
    } else {
      await addToFavorites(propertyId);
    }
  }, [addToFavorites, removeFromFavorites]);

  useEffect(() => {
    fetchFavorites();
  }, [fetchFavorites]);

  return {
    favorites,
    loading,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    refresh: fetchFavorites
  };
};

// Hook for property management (for property owners)
export const useUserProperties = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    approved: 0,
    pending: 0,
    rejected: 0
  });

  const fetchUserProperties = useCallback(async (filters?: { page?: number; limit?: number; status?: string }) => {
    try {
      setLoading(true);
      const response = await apiService.getUserProperties(filters);
      setProperties(response.data);
      
      // Calculate stats
      const total = response.meta?.total || 0;
      const approved = response.data.filter(p => p.status === 'APPROVED').length;
      const pending = response.data.filter(p => p.status === 'PENDING').length;
      const rejected = response.data.filter(p => p.status === 'REJECTED').length;
      
      setStats({ total, approved, pending, rejected });
    } catch (err: any) {
      const message = err.response?.data?.message || 'Failed to fetch properties';
      // Only show error toast for actual errors, not empty results
      if (err.response?.status !== 200) {
        toast.error(message);
      }
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProperty = useCallback(async (propertyId: string) => {
    try {
      await apiService.deleteProperty(propertyId);
      toast.success('Property deleted successfully');
      fetchUserProperties(); // Refresh list
    } catch (err: any) {
      const message = err.response?.data?.message || 'Failed to delete property';
      toast.error(message);
    }
  }, [fetchUserProperties]);

  useEffect(() => {
    fetchUserProperties();
  }, [fetchUserProperties]);

  return {
    properties,
    loading,
    stats,
    fetchUserProperties,
    deleteProperty,
    refresh: fetchUserProperties
  };
};
