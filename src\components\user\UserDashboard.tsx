import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FiTrendingUp } from 'react-icons/fi';

import { 
  HomeIcon,
  EyeIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  PlusIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ChartBarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import Button from '../common/Button';
import { useLanguage } from '../../contexts/LanguageContext';

interface DashboardStats {
  totalListings: number;
  activeListings: number;
  totalViews: number;
  totalInquiries: number;
  favoriteCount: number;
  monthlyViews: number;
  recentActivity: Activity[];
}

interface Activity {
  id: string;
  type: 'view' | 'inquiry' | 'favorite' | 'listing';
  title: string;
  description: string;
  timestamp: string;
  propertyId?: string;
}

interface UserDashboardProps {
  stats: DashboardStats;
  userName: string;
  className?: string;
}

const UserDashboard: React.FC<UserDashboardProps> = ({
  stats,
  userName,
  className = ''
}) => {
  const { t, isRTL } = useLanguage();
  
  const quickActions = [
    {
      title: t('dashboard.postProperty'),
      description: t('dashboard.postPropertyDesc'),
      icon: PlusIcon,
      href: '/post-property',
      color: 'bg-primary-600 hover:bg-primary-700'
    },
    {
      title: t('dashboard.viewListings'),
      description: t('dashboard.viewListingsDesc'),
      icon: HomeIcon,
      href: '/dashboard/listings',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: t('dashboard.messages'),
      description: t('dashboard.messagesDesc'),
      icon: ChatBubbleLeftRightIcon,
      href: '/messages',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: t('dashboard.favorites'),
      description: t('dashboard.favoritesDesc'),
      icon: HeartIcon,
      href: '/favorites',
      color: 'bg-red-600 hover:bg-red-700'
    }
  ];
  
  const statCards = [
    {
      title: t('dashboard.totalListings'),
      value: stats.totalListings,
      icon: HomeIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+12%',
      changeType: 'positive' as const
    },
    {
      title: t('dashboard.totalViews'),
      value: stats.totalViews.toLocaleString(),
      icon: EyeIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: '+8%',
      changeType: 'positive' as const
    },
    {
      title: t('dashboard.inquiries'),
      value: stats.totalInquiries,
      icon: ChatBubbleLeftRightIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+15%',
      changeType: 'positive' as const
    },
    {
      title: t('dashboard.favorites'),
      value: stats.favoriteCount,
      icon: HeartIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
      change: '+5%',
      changeType: 'positive' as const
    }
  ];
  
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'view':
        return <EyeIcon className="w-4 h-4" />;
      case 'inquiry':
        return <ChatBubbleLeftRightIcon className="w-4 h-4" />;
      case 'favorite':
        return <HeartIcon className="w-4 h-4" />;
      case 'listing':
        return <HomeIcon className="w-4 h-4" />;
      default:
        return <CalendarIcon className="w-4 h-4" />;
    }
  };
  
  const getActivityColor = (type: string) => {
    switch (type) {
      case 'view':
        return 'text-blue-600 bg-blue-100';
      case 'inquiry':
        return 'text-green-600 bg-green-100';
      case 'favorite':
        return 'text-red-600 bg-red-100';
      case 'listing':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };
  
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - time.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return t('time.justNow');
    if (diffInHours < 24) return t('time.hoursAgo', { count: diffInHours });
    const diffInDays = Math.floor(diffInHours / 24);
    return t('time.daysAgo', { count: diffInDays });
  };
  
  return (
    <div className={`space-y-8 ${className}`}>
      {/* Welcome Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-primary rounded-2xl p-8 text-white"
      >
        <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div>
            <h1 className="text-3xl font-bold mb-2">
              {t('dashboard.welcome', { name: userName })}
            </h1>
            <p className="text-primary-100 text-lg">
              {t('dashboard.welcomeMessage')}
            </p>
          </div>
          <div className="hidden md:block">
            <FiTrendingUp className="w-24 h-24 text-primary-200" />
          </div>
        </div>
      </motion.div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow duration-300"
          >
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm font-medium ${
                    stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-1">
                    {t('dashboard.thisMonth')}
                  </span>
                </div>
              </div>
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>
      
      {/* Quick Actions */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-2xl p-8 shadow-lg"
      >
        <h2 className="text-xl font-bold text-gray-900 mb-6">
          {t('dashboard.quickActions')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <Link
              key={action.title}
              to={action.href}
              className="group"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`${action.color} text-white rounded-xl p-6 transition-all duration-300 transform hover:shadow-lg`}
              >
                <action.icon className="w-8 h-8 mb-4" />
                <h3 className="font-semibold mb-2">{action.title}</h3>
                <p className="text-sm opacity-90">{action.description}</p>
                <ArrowRightIcon className="w-4 h-4 mt-3 group-hover:translate-x-1 transition-transform duration-300" />
              </motion.div>
            </Link>
          ))}
        </div>
      </motion.div>
      
      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="bg-white rounded-2xl p-8 shadow-lg"
      >
        <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h2 className="text-xl font-bold text-gray-900">
            {t('dashboard.recentActivity')}
          </h2>
          <Link
            to="/dashboard/activity"
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            {t('dashboard.viewAll')}
          </Link>
        </div>
        
        <div className="space-y-4">
          {stats.recentActivity.slice(0, 5).map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`flex items-center space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors ${
                isRTL ? 'space-x-reverse' : ''
              }`}
            >
              <div className={`p-2 rounded-lg ${getActivityColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {activity.title}
                </p>
                <p className="text-sm text-gray-500 truncate">
                  {activity.description}
                </p>
              </div>
              <div className="text-xs text-gray-400">
                {formatTimeAgo(activity.timestamp)}
              </div>
            </motion.div>
          ))}
        </div>
        
        {stats.recentActivity.length === 0 && (
          <div className="text-center py-8">
            <ChartBarIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">{t('dashboard.noActivity')}</p>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default UserDashboard;
