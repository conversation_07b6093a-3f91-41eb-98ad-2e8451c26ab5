import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { 
  HomeIcon,
  BuildingOfficeIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  PhotoIcon,
  PlusIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import Button from '../common/Button';
import Input from '../common/Input';
import { useLanguage } from '../../contexts/LanguageContext';

interface PropertyFormData {
  title: string;
  propertyType: string;
  category: string;
  province: string;
  city: string;
  district: string;
  address: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  areaUnit: string;
  yearBuilt: number;
  floorNumber: number;
  totalFloors: number;
  parking: number;
  garden: boolean;
  balcony: boolean;
  furnished: string;
  features: string[];
  description: string;
  images: File[];
  price: number;
  priceType: string;
  negotiable: boolean;
  contactName: string;
  phone: string;
  whatsapp: string;
  email: string;
  preferredContact: string;
  availableHours: string;
  featured: boolean;
  urgent: boolean;
}

interface PropertyFormProps {
  initialData?: Partial<PropertyFormData>;
  onSubmit: (data: PropertyFormData) => void;
  loading?: boolean;
  mode?: 'create' | 'edit';
}

const PropertyForm: React.FC<PropertyFormProps> = ({
  initialData,
  onSubmit,
  loading = false,
  mode = 'create'
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>(initialData?.features || []);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const { t, isRTL } = useLanguage();
  
  const { register, handleSubmit, formState: { errors }, watch, setValue } = useForm<PropertyFormData>({
    defaultValues: initialData
  });
  
  const steps = [
    { id: 1, title: t('propertyForm.basicInfo'), icon: HomeIcon },
    { id: 2, title: t('propertyForm.details'), icon: BuildingOfficeIcon },
    { id: 3, title: t('propertyForm.location'), icon: MapPinIcon },
    { id: 4, title: t('propertyForm.pricing'), icon: CurrencyDollarIcon },
    { id: 5, title: t('propertyForm.images'), icon: PhotoIcon }
  ];
  
  const propertyTypes = [
    { value: 'residential', label: t('propertyTypes.residential') },
    { value: 'commercial', label: t('propertyTypes.commercial') },
    { value: 'land', label: t('propertyTypes.land') }
  ];
  
  const categories = {
    residential: [
      { value: 'house', label: t('categories.house') },
      { value: 'apartment', label: t('categories.apartment') },
      { value: 'villa', label: t('categories.villa') },
      { value: 'townhouse', label: t('categories.townhouse') }
    ],
    commercial: [
      { value: 'office', label: t('categories.office') },
      { value: 'shop', label: t('categories.shop') },
      { value: 'warehouse', label: t('categories.warehouse') },
      { value: 'restaurant', label: t('categories.restaurant') }
    ],
    land: [
      { value: 'residential-land', label: t('categories.residentialLand') },
      { value: 'commercial-land', label: t('categories.commercialLand') },
      { value: 'agricultural', label: t('categories.agricultural') }
    ]
  };
  
  const availableFeatures = [
    'Swimming Pool', 'Gym', 'Garden', 'Balcony', 'Parking', 'Security',
    'Elevator', 'Air Conditioning', 'Heating', 'Internet', 'Furnished',
    'Kitchen Appliances', 'Laundry', 'Storage', 'Fireplace', 'Terrace'
  ];
  
  const provinces = [
    'Kabul', 'Herat', 'Kandahar', 'Balkh', 'Nangarhar', 'Kunduz',
    'Takhar', 'Baghlan', 'Ghazni', 'Paktia', 'Khost', 'Laghman'
  ];
  
  const handleFeatureToggle = (feature: string) => {
    setSelectedFeatures(prev => {
      const newFeatures = prev.includes(feature)
        ? prev.filter(f => f !== feature)
        : [...prev, feature];
      setValue('features', newFeatures);
      return newFeatures;
    });
  };
  
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setImageFiles(prev => [...prev, ...files]);
    setValue('images', [...imageFiles, ...files]);
  };
  
  const removeImage = (index: number) => {
    const newFiles = imageFiles.filter((_, i) => i !== index);
    setImageFiles(newFiles);
    setValue('images', newFiles);
  };
  
  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };
  
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };
  
  const onFormSubmit = (data: PropertyFormData) => {
    onSubmit({
      ...data,
      features: selectedFeatures,
      images: imageFiles
    });
  };
  
  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center">
          <div className={`
            flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300
            ${currentStep >= step.id 
              ? 'bg-primary-600 border-primary-600 text-white' 
              : 'border-gray-300 text-gray-400'
            }
          `}>
            <step.icon className="w-5 h-5" />
          </div>
          {index < steps.length - 1 && (
            <div className={`
              w-16 h-0.5 mx-2 transition-all duration-300
              ${currentStep > step.id ? 'bg-primary-600' : 'bg-gray-300'}
            `} />
          )}
        </div>
      ))}
    </div>
  );
  
  const renderStep1 = () => (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      <Input
        label={t('propertyForm.title')}
        {...register('title', { required: t('validation.required') })}
        error={errors.title?.message}
        placeholder={t('propertyForm.titlePlaceholder')}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('propertyForm.propertyType')}
          </label>
          <select
            {...register('propertyType', { required: t('validation.required') })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">{t('propertyForm.selectType')}</option>
            {propertyTypes.map(type => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
          {errors.propertyType && (
            <p className="mt-1 text-sm text-red-600">{errors.propertyType.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('propertyForm.category')}
          </label>
          <select
            {...register('category', { required: t('validation.required') })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">{t('propertyForm.selectCategory')}</option>
            {watch('propertyType') && categories[watch('propertyType') as keyof typeof categories]?.map(category => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
          {errors.category && (
            <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
          )}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          {t('propertyForm.description')}
        </label>
        <textarea
          {...register('description', { required: t('validation.required') })}
          rows={4}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder={t('propertyForm.descriptionPlaceholder')}
        />
        {errors.description && (
          <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
        )}
      </div>
    </motion.div>
  );

  const renderStep2 = () => (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Input
          label={t('propertyForm.bedrooms')}
          type="number"
          {...register('bedrooms', { min: 0 })}
          error={errors.bedrooms?.message}
        />
        <Input
          label={t('propertyForm.bathrooms')}
          type="number"
          {...register('bathrooms', { min: 0 })}
          error={errors.bathrooms?.message}
        />
        <div>
          <Input
            label={t('propertyForm.area')}
            type="number"
            {...register('area', { required: t('validation.required'), min: 1 })}
            error={errors.area?.message}
          />
          <select
            {...register('areaUnit')}
            className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-lg"
          >
            <option value="sqm">Square Meters</option>
            <option value="sqft">Square Feet</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label={t('propertyForm.yearBuilt')}
          type="number"
          {...register('yearBuilt', { min: 1900, max: new Date().getFullYear() + 5 })}
          error={errors.yearBuilt?.message}
        />
        <Input
          label={t('propertyForm.parking')}
          type="number"
          {...register('parking', { min: 0 })}
          error={errors.parking?.message}
        />
      </div>

      <div className="space-y-4">
        <h4 className="text-lg font-medium text-gray-900">{t('propertyForm.amenities')}</h4>
        <div className="flex flex-wrap gap-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register('garden')}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="ml-2">{t('propertyForm.garden')}</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              {...register('balcony')}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="ml-2">{t('propertyForm.balcony')}</span>
          </label>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {t('propertyForm.furnished')}
        </label>
        <select
          {...register('furnished')}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
        >
          <option value="unfurnished">{t('propertyForm.unfurnished')}</option>
          <option value="semi-furnished">{t('propertyForm.semiFurnished')}</option>
          <option value="fully-furnished">{t('propertyForm.fullyFurnished')}</option>
        </select>
      </div>

      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-3">{t('propertyForm.features')}</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {availableFeatures.map(feature => (
            <label key={feature} className="flex items-center">
              <input
                type="checkbox"
                checked={selectedFeatures.includes(feature)}
                onChange={() => handleFeatureToggle(feature)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm">{feature}</span>
            </label>
          ))}
        </div>
      </div>
    </motion.div>
  );

  const renderStep3 = () => (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('propertyForm.province')}
          </label>
          <select
            {...register('province', { required: t('validation.required') })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">{t('propertyForm.selectProvince')}</option>
            {provinces.map(province => (
              <option key={province} value={province}>
                {province}
              </option>
            ))}
          </select>
          {errors.province && (
            <p className="mt-1 text-sm text-red-600">{errors.province.message}</p>
          )}
        </div>

        <Input
          label={t('propertyForm.city')}
          {...register('city', { required: t('validation.required') })}
          error={errors.city?.message}
          placeholder={t('propertyForm.cityPlaceholder')}
        />
      </div>

      <Input
        label={t('propertyForm.district')}
        {...register('district')}
        error={errors.district?.message}
        placeholder={t('propertyForm.districtPlaceholder')}
      />

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          {t('propertyForm.address')}
        </label>
        <textarea
          {...register('address', { required: t('validation.required') })}
          rows={3}
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder={t('propertyForm.addressPlaceholder')}
        />
        {errors.address && (
          <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
        )}
      </div>
    </motion.div>
  );

  const renderStep4 = () => (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label={t('propertyForm.price')}
          type="number"
          {...register('price', { required: t('validation.required'), min: 0 })}
          error={errors.price?.message}
          placeholder="0"
        />

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {t('propertyForm.priceType')}
          </label>
          <select
            {...register('priceType', { required: t('validation.required') })}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="sale">{t('propertyForm.forSale')}</option>
            <option value="rent">{t('propertyForm.forRent')}</option>
            <option value="lease">{t('propertyForm.forLease')}</option>
          </select>
          {errors.priceType && (
            <p className="mt-1 text-sm text-red-600">{errors.priceType.message}</p>
          )}
        </div>
      </div>

      <div className="flex items-center">
        <input
          type="checkbox"
          {...register('negotiable')}
          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
        />
        <span className="ml-2">{t('propertyForm.negotiable')}</span>
      </div>

      <div className="bg-gray-50 p-6 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-4">{t('propertyForm.contactInfo')}</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label={t('propertyForm.contactName')}
            {...register('contactName', { required: t('validation.required') })}
            error={errors.contactName?.message}
          />
          <Input
            label={t('propertyForm.phone')}
            {...register('phone', { required: t('validation.required') })}
            error={errors.phone?.message}
          />
          <Input
            label={t('propertyForm.whatsapp')}
            {...register('whatsapp')}
            error={errors.whatsapp?.message}
          />
          <Input
            label={t('propertyForm.email')}
            type="email"
            {...register('email')}
            error={errors.email?.message}
          />
        </div>
      </div>
    </motion.div>
  );

  const renderStep5 = () => (
    <motion.div
      initial={{ opacity: 0, x: 50 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          {t('propertyForm.images')}
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <div className="mt-4">
            <label htmlFor="images" className="cursor-pointer">
              <span className="mt-2 block text-sm font-medium text-gray-900">
                {t('propertyForm.uploadImages')}
              </span>
              <input
                id="images"
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </label>
            <p className="mt-1 text-xs text-gray-500">
              {t('propertyForm.imageFormats')}
            </p>
          </div>
        </div>
      </div>

      {imageFiles.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {imageFiles.map((file, index) => (
            <div key={index} className="relative">
              <img
                src={URL.createObjectURL(file)}
                alt={`Preview ${index + 1}`}
                className="w-full h-24 object-cover rounded-lg"
              />
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      )}
    </motion.div>
  );

  return (
    <div className={`max-w-4xl mx-auto p-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="bg-white rounded-2xl shadow-xl p-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {mode === 'create' ? t('propertyForm.createTitle') : t('propertyForm.editTitle')}
          </h2>
          <p className="text-gray-600">
            {t('propertyForm.subtitle')}
          </p>
        </div>
        
        {renderStepIndicator()}
        
        <form onSubmit={handleSubmit(onFormSubmit)}>
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
          {currentStep === 4 && renderStep4()}
          {currentStep === 5 && renderStep5()}

          <div className={`flex justify-between mt-8 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 1}
            >
              {t('common.previous')}
            </Button>
            
            {currentStep === steps.length ? (
              <Button
                type="submit"
                loading={loading}
                icon={<PlusIcon className="w-5 h-5" />}
              >
                {mode === 'create' ? t('propertyForm.create') : t('propertyForm.update')}
              </Button>
            ) : (
              <Button
                type="button"
                onClick={nextStep}
              >
                {t('common.next')}
              </Button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

export default PropertyForm;
