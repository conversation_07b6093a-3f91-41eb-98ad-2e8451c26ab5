/**
 * Property Domain Entity
 * Represents the core business logic and rules for Property entities
 */

export enum PropertyType {
  HOUSE = 'HOUSE',
  APARTMENT = 'APARTMENT',
  VILLA = 'VILLA',
  COMMERCIAL = 'COMMERCIAL',
  LAND = 'LAND',
  OFFICE = 'OFFICE',
  WAREHOUSE = 'WAREHOUSE',
  SHOP = 'SHOP'
}

export enum ListingType {
  SALE = 'SALE',
  RENT = 'RENT',
  LEASE = 'LEASE'
}

export enum PropertyStatus {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  SOLD = 'SOLD',
  RENTED = 'RENTED',
  EXPIRED = 'EXPIRED',
  WITHDRAWN = 'WITHDRAWN'
}

export enum PropertyCondition {
  NEW = 'NEW',
  EXCELLENT = 'EXCELLENT',
  GOOD = 'GOOD',
  FAIR = 'FAIR',
  POOR = 'POOR',
  UNDER_CONSTRUCTION = 'UNDER_CONSTRUCTION'
}

export interface PropertyLocation {
  address: string;
  city: string;
  province: string;
  country: string;
  postalCode?: string;
  district?: string;
  neighborhood?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  landmarks?: string[];
  publicTransport?: string[];
}

export interface PropertyFeatures {
  bedrooms?: number;
  bathrooms?: number;
  kitchens?: number;
  livingRooms?: number;
  totalRooms?: number;
  floors?: number;
  area: {
    total: number;
    covered?: number;
    uncovered?: number;
    unit: 'sqm' | 'sqft' | 'marla' | 'kanal';
  };
  parking?: {
    spaces: number;
    type: 'covered' | 'open' | 'garage';
  };
  garden?: {
    area: number;
    type: 'front' | 'back' | 'side' | 'rooftop';
  };
  balcony?: boolean;
  terrace?: boolean;
  basement?: boolean;
  attic?: boolean;
  yearBuilt?: number;
  condition: PropertyCondition;
}

export interface PropertyAmenities {
  security?: boolean;
  elevator?: boolean;
  generator?: boolean;
  airConditioning?: boolean;
  heating?: boolean;
  internet?: boolean;
  cableTV?: boolean;
  gym?: boolean;
  pool?: boolean;
  playground?: boolean;
  mosque?: boolean;
  school?: boolean;
  hospital?: boolean;
  shopping?: boolean;
  publicTransport?: boolean;
  [key: string]: boolean | undefined;
}

export interface PropertyPricing {
  amount: number;
  currency: string;
  negotiable: boolean;
  pricePerUnit?: number;
  maintenanceFee?: number;
  utilities?: {
    included: boolean;
    electricity?: number;
    water?: number;
    gas?: number;
    internet?: number;
  };
  deposit?: number;
  commission?: {
    percentage: number;
    amount: number;
  };
}

export interface PropertyMedia {
  images: string[];
  videos?: string[];
  virtualTour?: string;
  floorPlan?: string[];
  documents?: string[];
}

export interface PropertyContact {
  ownerId: string;
  agentId?: string;
  showOwnerContact: boolean;
  preferredContactMethod: 'phone' | 'email' | 'whatsapp' | 'message';
  availableHours?: {
    start: string;
    end: string;
    days: string[];
  };
}

export interface PropertyAnalytics {
  views: number;
  favorites: number;
  inquiries: number;
  shares: number;
  lastViewed?: Date;
  averageViewDuration?: number;
  conversionRate?: number;
}

export class Property {
  constructor(
    public readonly id: string,
    public title: string,
    public description: string,
    public propertyType: PropertyType,
    public listingType: ListingType,
    public status: PropertyStatus,
    public location: PropertyLocation,
    public features: PropertyFeatures,
    public amenities: PropertyAmenities,
    public pricing: PropertyPricing,
    public media: PropertyMedia,
    public contact: PropertyContact,
    public analytics: PropertyAnalytics,
    public tags: string[],
    public readonly createdAt: Date,
    public updatedAt: Date,
    public publishedAt?: Date,
    public expiresAt?: Date,
    public deletedAt?: Date
  ) {}

  // Business Logic Methods

  /**
   * Check if property is available for viewing/purchase
   */
  public isAvailable(): boolean {
    return this.status === PropertyStatus.APPROVED && 
           !this.isExpired() && 
           !this.deletedAt;
  }

  /**
   * Check if property listing has expired
   */
  public isExpired(): boolean {
    return this.expiresAt ? this.expiresAt < new Date() : false;
  }

  /**
   * Check if property is sold or rented
   */
  public isSoldOrRented(): boolean {
    return this.status === PropertyStatus.SOLD || 
           this.status === PropertyStatus.RENTED;
  }

  /**
   * Approve property listing
   */
  public approve(): void {
    this.status = PropertyStatus.APPROVED;
    this.publishedAt = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Reject property listing
   */
  public reject(): void {
    this.status = PropertyStatus.REJECTED;
    this.updatedAt = new Date();
  }

  /**
   * Mark property as sold
   */
  public markAsSold(): void {
    if (this.listingType !== ListingType.SALE) {
      throw new Error('Only sale properties can be marked as sold');
    }
    this.status = PropertyStatus.SOLD;
    this.updatedAt = new Date();
  }

  /**
   * Mark property as rented
   */
  public markAsRented(): void {
    if (this.listingType !== ListingType.RENT) {
      throw new Error('Only rental properties can be marked as rented');
    }
    this.status = PropertyStatus.RENTED;
    this.updatedAt = new Date();
  }

  /**
   * Withdraw property from listing
   */
  public withdraw(): void {
    this.status = PropertyStatus.WITHDRAWN;
    this.updatedAt = new Date();
  }

  /**
   * Extend property listing expiration
   */
  public extendListing(days: number): void {
    const currentExpiry = this.expiresAt || new Date();
    this.expiresAt = new Date(currentExpiry.getTime() + (days * 24 * 60 * 60 * 1000));
    this.updatedAt = new Date();
  }

  /**
   * Update property details
   */
  public updateDetails(updates: {
    title?: string;
    description?: string;
    features?: Partial<PropertyFeatures>;
    amenities?: Partial<PropertyAmenities>;
    pricing?: Partial<PropertyPricing>;
  }): void {
    if (updates.title) this.title = updates.title;
    if (updates.description) this.description = updates.description;
    if (updates.features) this.features = { ...this.features, ...updates.features };
    if (updates.amenities) this.amenities = { ...this.amenities, ...updates.amenities };
    if (updates.pricing) this.pricing = { ...this.pricing, ...updates.pricing };
    
    this.updatedAt = new Date();
  }

  /**
   * Add images to property
   */
  public addImages(images: string[]): void {
    this.media.images = [...this.media.images, ...images];
    this.updatedAt = new Date();
  }

  /**
   * Remove image from property
   */
  public removeImage(imageUrl: string): void {
    this.media.images = this.media.images.filter(img => img !== imageUrl);
    this.updatedAt = new Date();
  }

  /**
   * Record property view
   */
  public recordView(): void {
    this.analytics.views += 1;
    this.analytics.lastViewed = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Record property favorite
   */
  public recordFavorite(): void {
    this.analytics.favorites += 1;
    this.updatedAt = new Date();
  }

  /**
   * Record property inquiry
   */
  public recordInquiry(): void {
    this.analytics.inquiries += 1;
    this.updatedAt = new Date();
  }

  /**
   * Calculate price per unit area
   */
  public getPricePerUnit(): number {
    return this.pricing.amount / this.features.area.total;
  }

  /**
   * Get property age in years
   */
  public getPropertyAge(): number | null {
    if (!this.features.yearBuilt) return null;
    return new Date().getFullYear() - this.features.yearBuilt;
  }

  /**
   * Check if property has specific amenity
   */
  public hasAmenity(amenity: keyof PropertyAmenities): boolean {
    return Boolean(this.amenities[amenity]);
  }

  /**
   * Get property score based on features and amenities
   */
  public getPropertyScore(): number {
    let score = 0;
    
    // Base score from features
    score += this.features.bedrooms || 0;
    score += this.features.bathrooms || 0;
    score += (this.features.area.total / 100); // Area contribution
    
    // Amenities contribution
    const amenityCount = Object.values(this.amenities).filter(Boolean).length;
    score += amenityCount * 0.5;
    
    // Condition contribution
    const conditionScores = {
      [PropertyCondition.NEW]: 10,
      [PropertyCondition.EXCELLENT]: 8,
      [PropertyCondition.GOOD]: 6,
      [PropertyCondition.FAIR]: 4,
      [PropertyCondition.POOR]: 2,
      [PropertyCondition.UNDER_CONSTRUCTION]: 5
    };
    score += conditionScores[this.features.condition] || 0;
    
    return Math.round(score * 10) / 10; // Round to 1 decimal place
  }

  /**
   * Soft delete property
   */
  public softDelete(): void {
    this.deletedAt = new Date();
    this.status = PropertyStatus.WITHDRAWN;
    this.updatedAt = new Date();
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): any {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      propertyType: this.propertyType,
      listingType: this.listingType,
      status: this.status,
      location: this.location,
      features: this.features,
      amenities: this.amenities,
      pricing: this.pricing,
      media: this.media,
      contact: {
        ...this.contact,
        ownerId: undefined // Don't expose owner ID in public JSON
      },
      analytics: {
        views: this.analytics.views,
        favorites: this.analytics.favorites,
        inquiries: this.analytics.inquiries
      },
      tags: this.tags,
      propertyScore: this.getPropertyScore(),
      pricePerUnit: this.getPricePerUnit(),
      propertyAge: this.getPropertyAge(),
      isAvailable: this.isAvailable(),
      createdAt: this.createdAt,
      updatedAt: this.updatedAt,
      publishedAt: this.publishedAt
    };
  }
}
