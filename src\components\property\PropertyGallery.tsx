import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronLeftIcon,
  ChevronRightIcon,
  XMarkIcon,
  PlayIcon,
  PauseIcon,
  MagnifyingGlassIcon,
  ShareIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import Button from '../common/Button';
import { useLanguage } from '../../contexts/LanguageContext';

interface PropertyImage {
  id: string;
  url: string;
  alt: string;
  type: 'image' | 'video';
  thumbnail?: string;
  caption?: string;
}

interface PropertyGalleryProps {
  images: PropertyImage[];
  title?: string;
  onShare?: () => void;
  onToggleFavorite?: () => void;
  isFavorite?: boolean;
  className?: string;
}

const PropertyGallery: React.FC<PropertyGalleryProps> = ({
  images,
  title,
  onShare,
  onToggleFavorite,
  isFavorite = false,
  className = ''
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const { t, isRTL } = useLanguage();
  
  const currentImage = images[currentIndex];
  
  const nextImage = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };
  
  const prevImage = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };
  
  const goToImage = (index: number) => {
    setCurrentIndex(index);
  };
  
  const openLightbox = () => {
    setIsLightboxOpen(true);
  };
  
  const closeLightbox = () => {
    setIsLightboxOpen(false);
    setIsPlaying(false);
  };
  
  const toggleSlideshow = () => {
    setIsPlaying(!isPlaying);
  };
  
  // Auto-play slideshow
  React.useEffect(() => {
    if (isPlaying && isLightboxOpen) {
      const interval = setInterval(nextImage, 3000);
      return () => clearInterval(interval);
    }
  }, [isPlaying, isLightboxOpen]);
  
  // Keyboard navigation
  React.useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isLightboxOpen) return;
      
      switch (e.key) {
        case 'ArrowLeft':
          prevImage();
          break;
        case 'ArrowRight':
          nextImage();
          break;
        case 'Escape':
          closeLightbox();
          break;
        case ' ':
          e.preventDefault();
          toggleSlideshow();
          break;
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isLightboxOpen]);
  
  if (!images || images.length === 0) {
    return (
      <div className={`bg-gray-200 rounded-xl flex items-center justify-center h-96 ${className}`}>
        <div className="text-center text-gray-500">
          <MagnifyingGlassIcon className="w-12 h-12 mx-auto mb-2" />
          <p>{t('gallery.noImages')}</p>
        </div>
      </div>
    );
  }
  
  return (
    <>
      <div className={`relative ${className}`}>
        {/* Main Image */}
        <div className="relative h-96 lg:h-[500px] rounded-xl overflow-hidden group">
          <motion.img
            key={currentIndex}
            src={currentImage.url}
            alt={currentImage.alt}
            className="w-full h-full object-cover cursor-pointer"
            onClick={openLightbox}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          />
          
          {/* Overlay Controls */}
          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300">
            {/* Navigation Arrows */}
            {images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className={`absolute top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-opacity-70 ${
                    isRTL ? 'right-4' : 'left-4'
                  }`}
                >
                  <ChevronLeftIcon className="w-6 h-6" />
                </button>
                <button
                  onClick={nextImage}
                  className={`absolute top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-opacity-70 ${
                    isRTL ? 'left-4' : 'right-4'
                  }`}
                >
                  <ChevronRightIcon className="w-6 h-6" />
                </button>
              </>
            )}
            
            {/* Top Controls */}
            <div className="absolute top-4 right-4 flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleFavorite}
                icon={isFavorite ? <HeartSolidIcon className="w-5 h-5 text-red-500" /> : <HeartIcon className="w-5 h-5" />}
                className="bg-black bg-opacity-50 text-white hover:bg-opacity-70"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={onShare}
                icon={<ShareIcon className="w-5 h-5" />}
                className="bg-black bg-opacity-50 text-white hover:bg-opacity-70"
              />
              <Button
                variant="ghost"
                size="sm"
                onClick={openLightbox}
                icon={<MagnifyingGlassIcon className="w-5 h-5" />}
                className="bg-black bg-opacity-50 text-white hover:bg-opacity-70"
              />
            </div>
            
            {/* Image Counter */}
            {images.length > 1 && (
              <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm opacity-0 group-hover:opacity-100 transition-all duration-300">
                {currentIndex + 1} / {images.length}
              </div>
            )}
          </div>
        </div>
        
        {/* Thumbnail Strip */}
        {images.length > 1 && (
          <div className="mt-4 flex space-x-2 overflow-x-auto pb-2">
            {images.map((image, index) => (
              <button
                key={image.id}
                onClick={() => goToImage(index)}
                className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                  index === currentIndex 
                    ? 'border-primary-500 ring-2 ring-primary-200' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <img
                  src={image.thumbnail || image.url}
                  alt={image.alt}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        )}
      </div>
      
      {/* Lightbox */}
      <AnimatePresence>
        {isLightboxOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center"
            onClick={closeLightbox}
          >
            <div className="relative w-full h-full flex items-center justify-center p-4">
              {/* Close Button */}
              <button
                onClick={closeLightbox}
                className="absolute top-4 right-4 text-white hover:text-gray-300 transition-colors z-10"
              >
                <XMarkIcon className="w-8 h-8" />
              </button>
              
              {/* Controls */}
              <div className="absolute top-4 left-4 flex items-center space-x-4 z-10">
                <button
                  onClick={toggleSlideshow}
                  className="text-white hover:text-gray-300 transition-colors"
                >
                  {isPlaying ? (
                    <PauseIcon className="w-6 h-6" />
                  ) : (
                    <PlayIcon className="w-6 h-6" />
                  )}
                </button>
                <span className="text-white text-sm">
                  {currentIndex + 1} / {images.length}
                </span>
              </div>
              
              {/* Main Image */}
              <motion.img
                key={currentIndex}
                src={currentImage.url}
                alt={currentImage.alt}
                className="max-w-full max-h-full object-contain"
                onClick={(e) => e.stopPropagation()}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
              />
              
              {/* Navigation */}
              {images.length > 1 && (
                <>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      prevImage();
                    }}
                    className={`absolute top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors ${
                      isRTL ? 'right-4' : 'left-4'
                    }`}
                  >
                    <ChevronLeftIcon className="w-12 h-12" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      nextImage();
                    }}
                    className={`absolute top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 transition-colors ${
                      isRTL ? 'left-4' : 'right-4'
                    }`}
                  >
                    <ChevronRightIcon className="w-12 h-12" />
                  </button>
                </>
              )}
              
              {/* Caption */}
              {currentImage.caption && (
                <div className="absolute bottom-4 left-4 right-4 text-center">
                  <p className="text-white text-sm bg-black bg-opacity-50 px-4 py-2 rounded-lg">
                    {currentImage.caption}
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PropertyGallery;
