/**
 * Professional Card Component
 * A versatile card component for displaying content with various layouts and styles
 */

import React, { forwardRef, HTMLAttributes, ReactNode } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../utils/cn';

// Card variants
const cardVariants = cva(
  [
    'bg-white rounded-lg border transition-all duration-200',
    'overflow-hidden'
  ],
  {
    variants: {
      variant: {
        default: 'border-neutral-200 shadow-sm hover:shadow-md',
        elevated: 'border-neutral-200 shadow-md hover:shadow-lg',
        outlined: 'border-neutral-300 shadow-none hover:border-neutral-400',
        ghost: 'border-transparent shadow-none hover:bg-neutral-50',
        gradient: 'border-transparent shadow-lg bg-gradient-to-br from-primary-500 to-primary-600 text-white'
      },
      size: {
        sm: 'p-4',
        md: 'p-6',
        lg: 'p-8',
        xl: 'p-10'
      },
      interactive: {
        true: 'cursor-pointer hover:scale-[1.02] active:scale-[0.98]',
        false: ''
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      interactive: false
    }
  }
);

export interface CardProps
  extends HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  /** Card content */
  children: ReactNode;
  /** Whether the card is clickable */
  interactive?: boolean;
  /** Custom className */
  className?: string;
}

export const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ children, variant, size, interactive, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(cardVariants({ variant, size, interactive }), className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// Card Header component
export interface CardHeaderProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

export const CardHeader = forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex flex-col space-y-1.5 pb-4', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardHeader.displayName = 'CardHeader';

// Card Title component
export interface CardTitleProps extends HTMLAttributes<HTMLHeadingElement> {
  children: ReactNode;
  className?: string;
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

export const CardTitle = forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ children, className, as: Component = 'h3', ...props }, ref) => {
    return (
      <Component
        ref={ref}
        className={cn(
          'text-lg font-semibold leading-none tracking-tight text-neutral-900',
          className
        )}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

CardTitle.displayName = 'CardTitle';

// Card Description component
export interface CardDescriptionProps extends HTMLAttributes<HTMLParagraphElement> {
  children: ReactNode;
  className?: string;
}

export const CardDescription = forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <p
        ref={ref}
        className={cn('text-sm text-neutral-600', className)}
        {...props}
      >
        {children}
      </p>
    );
  }
);

CardDescription.displayName = 'CardDescription';

// Card Content component
export interface CardContentProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

export const CardContent = forwardRef<HTMLDivElement, CardContentProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('text-neutral-700', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardContent.displayName = 'CardContent';

// Card Footer component
export interface CardFooterProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode;
  className?: string;
}

export const CardFooter = forwardRef<HTMLDivElement, CardFooterProps>(
  ({ children, className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn('flex items-center pt-4', className)}
        {...props}
      >
        {children}
      </div>
    );
  }
);

CardFooter.displayName = 'CardFooter';

// Property Card component (specialized for real estate)
export interface PropertyCardProps extends Omit<CardProps, 'children'> {
  /** Property image */
  image: string;
  /** Property title */
  title: string;
  /** Property price */
  price: string;
  /** Property location */
  location: string;
  /** Property type */
  type: string;
  /** Property features */
  features: {
    bedrooms?: number;
    bathrooms?: number;
    area?: number;
    areaUnit?: string;
  };
  /** Property status */
  status?: 'sale' | 'rent' | 'sold' | 'rented';
  /** Whether the property is featured */
  featured?: boolean;
  /** Click handler */
  onCardClick?: () => void;
  /** Favorite button click handler */
  onFavoriteClick?: () => void;
  /** Whether the property is favorited */
  isFavorited?: boolean;
}

export const PropertyCard: React.FC<PropertyCardProps> = ({
  image,
  title,
  price,
  location,
  type,
  features,
  status = 'sale',
  featured = false,
  onCardClick,
  onFavoriteClick,
  isFavorited = false,
  className,
  ...props
}) => {
  const statusColors = {
    sale: 'bg-success-500',
    rent: 'bg-primary-500',
    sold: 'bg-neutral-500',
    rented: 'bg-neutral-500'
  };

  const statusLabels = {
    sale: 'For Sale',
    rent: 'For Rent',
    sold: 'Sold',
    rented: 'Rented'
  };

  return (
    <Card
      interactive={!!onCardClick}
      className={cn('group overflow-hidden', className)}
      onClick={onCardClick}
      {...props}
    >
      {/* Image container */}
      <div className="relative aspect-[4/3] overflow-hidden">
        <img
          src={image}
          alt={title}
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
        />
        
        {/* Status badge */}
        <div className={cn(
          'absolute top-3 left-3 px-2 py-1 rounded-md text-xs font-medium text-white',
          statusColors[status]
        )}>
          {statusLabels[status]}
        </div>

        {/* Featured badge */}
        {featured && (
          <div className="absolute top-3 right-3 px-2 py-1 rounded-md text-xs font-medium bg-warning-500 text-white">
            Featured
          </div>
        )}

        {/* Favorite button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onFavoriteClick?.();
          }}
          className={cn(
            'absolute bottom-3 right-3 p-2 rounded-full transition-all duration-200',
            'bg-white/80 hover:bg-white shadow-sm hover:shadow-md',
            isFavorited ? 'text-error-500' : 'text-neutral-400 hover:text-error-500'
          )}
        >
          <svg
            className="w-4 h-4"
            fill={isFavorited ? 'currentColor' : 'none'}
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
            />
          </svg>
        </button>
      </div>

      {/* Content */}
      <div className="p-4 space-y-3">
        {/* Price and type */}
        <div className="flex items-center justify-between">
          <span className="text-xl font-bold text-primary-600">{price}</span>
          <span className="text-sm text-neutral-500 bg-neutral-100 px-2 py-1 rounded">
            {type}
          </span>
        </div>

        {/* Title */}
        <h3 className="font-semibold text-neutral-900 line-clamp-2 group-hover:text-primary-600 transition-colors">
          {title}
        </h3>

        {/* Location */}
        <div className="flex items-center text-sm text-neutral-600">
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          {location}
        </div>

        {/* Features */}
        <div className="flex items-center space-x-4 text-sm text-neutral-600">
          {features.bedrooms && (
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 21v-4a2 2 0 012-2h4a2 2 0 012 2v4" />
              </svg>
              {features.bedrooms} bed
            </div>
          )}
          
          {features.bathrooms && (
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11" />
              </svg>
              {features.bathrooms} bath
            </div>
          )}
          
          {features.area && (
            <div className="flex items-center">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
              </svg>
              {features.area} {features.areaUnit || 'sqft'}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

// Export card variants for external use
export { cardVariants };
export type CardVariant = VariantProps<typeof cardVariants>['variant'];
export type CardSize = VariantProps<typeof cardVariants>['size'];
