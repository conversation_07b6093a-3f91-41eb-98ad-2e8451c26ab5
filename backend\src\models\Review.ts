import mongoose, { Document, Schema } from 'mongoose';

export interface IReview extends Document {
  _id: string;
  rating: number; // 1-5 stars
  comment?: string;
  
  // Relations
  reviewer: mongoose.Types.ObjectId;
  property: mongoose.Types.ObjectId;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}

const reviewSchema = new Schema<IReview>({
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot exceed 5']
  },
  comment: {
    type: String,
    trim: true,
    maxlength: [1000, 'Comment cannot exceed 1000 characters']
  },
  
  // Relations
  reviewer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  property: {
    type: Schema.Types.ObjectId,
    ref: 'Property',
    required: true
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc: any, ret: any) {
      const { _id, __v, ...rest } = ret;
      return { id: _id, ...rest };
    }
  }
});

// Ensure a user can only review a property once
reviewSchema.index({ reviewer: 1, property: 1 }, { unique: true });

// Other indexes
reviewSchema.index({ reviewer: 1 });
reviewSchema.index({ property: 1 });
reviewSchema.index({ rating: 1 });
reviewSchema.index({ createdAt: -1 });

export default mongoose.model<IReview>('Review', reviewSchema);
