/* Mobile-First Responsive Utilities */

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Safe area handling for mobile devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Mobile-optimized scrolling */
.scroll-smooth-mobile {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Hide scrollbars on mobile while keeping functionality */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Mobile-friendly focus states */
.focus-mobile {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* Improved tap targets for mobile */
.tap-highlight-none {
  -webkit-tap-highlight-color: transparent;
}

.tap-highlight-primary {
  -webkit-tap-highlight-color: rgba(59, 130, 246, 0.3);
}

/* Mobile-optimized text selection */
.select-none-mobile {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Prevent zoom on input focus (iOS) */
.no-zoom {
  font-size: 16px;
}

/* Mobile-friendly animations with reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-slide-up,
  .animate-slide-down,
  .animate-slide-left,
  .animate-slide-right,
  .animate-scale-in,
  .animate-bounce-gentle,
  .animate-float,
  .animate-wiggle {
    animation: none;
  }
  
  .transition-all,
  .transition-colors,
  .transition-transform,
  .transition-opacity {
    transition: none;
  }
}

/* Mobile-specific utility classes */
@media (max-width: 640px) {
  .mobile-full-width {
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  }
  
  .mobile-padding {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .mobile-text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
  
  .mobile-grid-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .mobile-space-y-4 > * + * {
    margin-top: 1rem;
  }
  
  .mobile-hidden {
    display: none;
  }
  
  .mobile-block {
    display: block;
  }
  
  .mobile-flex {
    display: flex;
  }
}

/* Tablet-specific utilities */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .tablet-hidden {
    display: none;
  }
  
  .tablet-block {
    display: block;
  }
}

/* Desktop-specific utilities */
@media (min-width: 1025px) {
  .desktop-grid-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
  
  .desktop-grid-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
  
  .desktop-hidden {
    display: none;
  }
  
  .desktop-block {
    display: block;
  }
}

/* RTL-specific mobile optimizations */
[dir="rtl"] .mobile-padding {
  padding-left: 1rem;
  padding-right: 1rem;
}

[dir="rtl"] .mobile-text-right {
  text-align: right;
}

[dir="rtl"] .mobile-flex-row-reverse {
  flex-direction: row-reverse;
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-border {
    border-width: 0.5px;
  }
  
  .retina-shadow {
    box-shadow: 0 0.5px 1px rgba(0, 0, 0, 0.1);
  }
}

/* Dark mode mobile optimizations */
@media (prefers-color-scheme: dark) {
  .dark-mobile-bg {
    background-color: #1f2937;
  }
  
  .dark-mobile-text {
    color: #f9fafb;
  }
  
  .dark-mobile-border {
    border-color: #374151;
  }
}

/* Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 640px) {
  .landscape-mobile-compact {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .landscape-mobile-header {
    height: 3rem;
  }
  
  .landscape-mobile-text-sm {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}

/* Touch device specific styles */
@media (hover: none) and (pointer: coarse) {
  .touch-device-padding {
    padding: 0.75rem;
  }
  
  .touch-device-button {
    min-height: 44px;
    padding: 0.75rem 1.5rem;
  }
  
  .touch-device-input {
    min-height: 44px;
    padding: 0.75rem;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Hover-capable device styles */
@media (hover: hover) and (pointer: fine) {
  .hover-device-subtle {
    transition: all 0.2s ease-in-out;
  }
  
  .hover-device-subtle:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

/* Print styles for mobile */
@media print {
  .print-hidden {
    display: none !important;
  }
  
  .print-block {
    display: block !important;
  }
  
  .print-no-shadow {
    box-shadow: none !important;
  }
  
  .print-no-background {
    background: none !important;
  }
}

/* Accessibility improvements for mobile */
@media (prefers-contrast: high) {
  .high-contrast-border {
    border-width: 2px;
    border-color: #000;
  }
  
  .high-contrast-text {
    color: #000;
    font-weight: 600;
  }
}

/* Focus visible for keyboard navigation on mobile */
.focus-visible-mobile:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Mobile-optimized loading states */
.mobile-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Mobile-friendly modal and overlay styles */
.mobile-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 50;
}

.mobile-modal-content {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 1rem 1rem 0 0;
  max-height: 90vh;
  overflow-y: auto;
  transform: translateY(100%);
  transition: transform 0.3s ease-out;
}

.mobile-modal-content.open {
  transform: translateY(0);
}

/* Mobile-optimized form styles */
.mobile-form-group {
  margin-bottom: 1.5rem;
}

.mobile-form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.mobile-form-input {
  width: 100%;
  padding: 0.75rem;
  font-size: 16px; /* Prevents zoom on iOS */
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  transition: border-color 0.2s ease-in-out;
}

.mobile-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
