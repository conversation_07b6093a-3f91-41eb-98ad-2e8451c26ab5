/**
 * User Repository Interface
 * Defines the contract for user data access operations
 */

import { User, UserRole, UserStatus, VerificationStatus } from '../entities/User';

export interface UserSearchCriteria {
  email?: string;
  role?: UserRole;
  status?: UserStatus;
  verificationStatus?: VerificationStatus;
  createdAfter?: Date;
  createdBefore?: Date;
  lastLoginAfter?: Date;
  searchTerm?: string; // For searching in name, email, etc.
}

export interface UserSortOptions {
  field: 'createdAt' | 'updatedAt' | 'email' | 'lastName' | 'lastLogin';
  direction: 'ASC' | 'DESC';
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface IUserRepository {
  /**
   * Create a new user
   */
  create(user: User): Promise<User>;

  /**
   * Find user by ID
   */
  findById(id: string): Promise<User | null>;

  /**
   * Find user by email
   */
  findByEmail(email: string): Promise<User | null>;

  /**
   * Find multiple users by IDs
   */
  findByIds(ids: string[]): Promise<User[]>;

  /**
   * Update user
   */
  update(user: User): Promise<User>;

  /**
   * Delete user (hard delete)
   */
  delete(id: string): Promise<void>;

  /**
   * Soft delete user
   */
  softDelete(id: string): Promise<void>;

  /**
   * Search users with criteria
   */
  search(
    criteria: UserSearchCriteria,
    sort?: UserSortOptions,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<User>>;

  /**
   * Count users by criteria
   */
  count(criteria?: UserSearchCriteria): Promise<number>;

  /**
   * Check if email exists
   */
  emailExists(email: string, excludeId?: string): Promise<boolean>;

  /**
   * Find users by role
   */
  findByRole(role: UserRole): Promise<User[]>;

  /**
   * Find users by status
   */
  findByStatus(status: UserStatus): Promise<User[]>;

  /**
   * Find users with pending verification
   */
  findPendingVerification(): Promise<User[]>;

  /**
   * Find users with locked accounts
   */
  findLockedAccounts(): Promise<User[]>;

  /**
   * Find users created within date range
   */
  findByDateRange(startDate: Date, endDate: Date): Promise<User[]>;

  /**
   * Get user statistics
   */
  getStatistics(): Promise<{
    total: number;
    byRole: Record<UserRole, number>;
    byStatus: Record<UserStatus, number>;
    byVerificationStatus: Record<VerificationStatus, number>;
    newUsersThisMonth: number;
    activeUsersThisMonth: number;
  }>;

  /**
   * Find users with two-factor authentication enabled
   */
  findWithTwoFactorEnabled(): Promise<User[]>;

  /**
   * Find users by trusted device
   */
  findByTrustedDevice(deviceId: string): Promise<User[]>;

  /**
   * Update last login timestamp
   */
  updateLastLogin(id: string): Promise<void>;

  /**
   * Increment login attempts
   */
  incrementLoginAttempts(id: string): Promise<void>;

  /**
   * Reset login attempts
   */
  resetLoginAttempts(id: string): Promise<void>;

  /**
   * Lock user account
   */
  lockAccount(id: string, lockUntil: Date): Promise<void>;

  /**
   * Unlock user account
   */
  unlockAccount(id: string): Promise<void>;

  /**
   * Find users by location (city, province, country)
   */
  findByLocation(city?: string, province?: string, country?: string): Promise<User[]>;

  /**
   * Find users by language preference
   */
  findByLanguage(language: string): Promise<User[]>;

  /**
   * Find agents in specific location
   */
  findAgentsByLocation(city: string, province: string): Promise<User[]>;

  /**
   * Get user activity summary
   */
  getUserActivity(id: string): Promise<{
    propertiesListed: number;
    propertiesSold: number;
    propertiesRented: number;
    messagesReceived: number;
    messagesSent: number;
    profileViews: number;
    lastActivity: Date;
  }>;

  /**
   * Find users who haven't logged in for specified days
   */
  findInactiveUsers(days: number): Promise<User[]>;

  /**
   * Find users with incomplete profiles
   */
  findIncompleteProfiles(): Promise<User[]>;

  /**
   * Bulk update users
   */
  bulkUpdate(updates: { id: string; data: Partial<User> }[]): Promise<void>;

  /**
   * Export users data
   */
  exportUsers(criteria?: UserSearchCriteria): Promise<any[]>;

  /**
   * Find users for newsletter
   */
  findNewsletterSubscribers(): Promise<User[]>;

  /**
   * Find users by registration source
   */
  findByRegistrationSource(source: string): Promise<User[]>;
}
