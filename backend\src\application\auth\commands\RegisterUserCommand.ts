/**
 * Register User Command
 * Handles user registration with comprehensive validation and security
 */

import { User, UserRole, UserStatus, VerificationStatus, UserProfile, UserPreferences, UserSecuritySettings } from '../../../domain/entities/User';
import { IUserRepository } from '../../../domain/repositories/IUserRepository';
import { PasswordService } from '../../../shared/services/PasswordService';
import { EmailService } from '../../../shared/services/EmailService';
import { ValidationError, ConflictError } from '../../../shared/errors/ApplicationErrors';

export interface RegisterUserRequest {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role?: UserRole;
  acceptTerms: boolean;
  acceptPrivacy: boolean;
  language?: string;
  referralCode?: string;
}

export interface RegisterUserResponse {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    verificationStatus: VerificationStatus;
  };
  verificationToken: string;
  message: string;
}

export class RegisterUserCommand {
  constructor(
    private userRepository: IUserRepository,
    private passwordService: PasswordService,
    private emailService: EmailService
  ) {}

  async execute(request: RegisterUserRequest): Promise<RegisterUserResponse> {
    // Validate input
    await this.validateInput(request);

    // Check if email already exists
    const existingUser = await this.userRepository.findByEmail(request.email);
    if (existingUser) {
      throw new ConflictError('Email already registered');
    }

    // Hash password
    const passwordHash = await this.passwordService.hash(request.password);

    // Create user profile
    const profile: UserProfile = {
      firstName: request.firstName,
      lastName: request.lastName,
      phone: request.phone,
      languages: request.language ? [request.language] : ['en']
    };

    // Create user preferences
    const preferences: UserPreferences = {
      language: request.language || 'en',
      currency: 'USD',
      timezone: 'UTC',
      notifications: {
        email: true,
        sms: false,
        push: true,
        marketing: false
      },
      privacy: {
        showProfile: true,
        showContact: false,
        showActivity: false
      },
      searchPreferences: {}
    };

    // Create security settings
    const securitySettings: UserSecuritySettings = {
      twoFactorEnabled: false,
      lastPasswordChange: new Date(),
      loginAttempts: 0,
      trustedDevices: [],
      securityQuestions: []
    };

    // Create user entity
    const user = new User(
      this.generateUserId(),
      request.email.toLowerCase(),
      passwordHash,
      request.role || UserRole.USER,
      UserStatus.PENDING,
      VerificationStatus.PENDING,
      profile,
      preferences,
      securitySettings,
      new Date(),
      new Date()
    );

    // Save user to database
    const savedUser = await this.userRepository.create(user);

    // Generate verification token
    const verificationToken = await this.generateVerificationToken(savedUser.id);

    // Send verification email
    await this.sendVerificationEmail(savedUser, verificationToken);

    // Log registration event
    await this.logRegistrationEvent(savedUser, request.referralCode);

    return {
      user: {
        id: savedUser.id,
        email: savedUser.email,
        firstName: savedUser.profile.firstName,
        lastName: savedUser.profile.lastName,
        role: savedUser.role,
        status: savedUser.status,
        verificationStatus: savedUser.verificationStatus
      },
      verificationToken,
      message: 'Registration successful. Please check your email to verify your account.'
    };
  }

  private async validateInput(request: RegisterUserRequest): Promise<void> {
    const errors: string[] = [];

    // Email validation
    if (!request.email) {
      errors.push('Email is required');
    } else if (!this.isValidEmail(request.email)) {
      errors.push('Invalid email format');
    }

    // Password validation
    if (!request.password) {
      errors.push('Password is required');
    } else if (!this.isValidPassword(request.password)) {
      errors.push('Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character');
    }

    // Confirm password validation
    if (request.password !== request.confirmPassword) {
      errors.push('Passwords do not match');
    }

    // Name validation
    if (!request.firstName || request.firstName.trim().length < 2) {
      errors.push('First name must be at least 2 characters long');
    }

    if (!request.lastName || request.lastName.trim().length < 2) {
      errors.push('Last name must be at least 2 characters long');
    }

    // Phone validation (if provided)
    if (request.phone && !this.isValidPhone(request.phone)) {
      errors.push('Invalid phone number format');
    }

    // Terms and privacy validation
    if (!request.acceptTerms) {
      errors.push('You must accept the terms of service');
    }

    if (!request.acceptPrivacy) {
      errors.push('You must accept the privacy policy');
    }

    // Role validation
    if (request.role && !Object.values(UserRole).includes(request.role)) {
      errors.push('Invalid user role');
    }

    if (errors.length > 0) {
      throw new ValidationError('Validation failed', errors);
    }
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidPassword(password: string): boolean {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  }

  private isValidPhone(phone: string): boolean {
    // Basic international phone number validation
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
  }

  private generateUserId(): string {
    // Generate UUID v4
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  private async generateVerificationToken(userId: string): Promise<string> {
    // Generate secure verification token
    const token = Math.random().toString(36).substring(2, 15) + 
                  Math.random().toString(36).substring(2, 15);
    
    // Store token in cache/database with expiration
    // Implementation depends on your caching strategy
    
    return token;
  }

  private async sendVerificationEmail(user: User, token: string): Promise<void> {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
    
    await this.emailService.sendEmail({
      to: user.email,
      subject: 'Verify Your Email Address',
      template: 'email-verification',
      data: {
        firstName: user.profile.firstName,
        verificationUrl,
        supportEmail: process.env.SUPPORT_EMAIL
      }
    });
  }

  private async logRegistrationEvent(user: User, referralCode?: string): Promise<void> {
    // Log registration event for analytics
    const event = {
      type: 'USER_REGISTERED',
      userId: user.id,
      email: user.email,
      role: user.role,
      referralCode,
      timestamp: new Date(),
      metadata: {
        userAgent: '', // Get from request context
        ipAddress: '', // Get from request context
        source: 'web' // or 'mobile', 'api', etc.
      }
    };

    // Send to analytics service
    // Implementation depends on your analytics setup
    console.log('Registration event:', event);
  }
}
