# 🚀 Quick Start Guide

## Prerequisites

- Node.js 18+ installed
- MongoDB running locally or connection string ready

## 1. Environment Setup

### Frontend Environment

Create `.env` file in root directory:

```env
VITE_API_URL=http://localhost:3001/api
VITE_APP_NAME=Real Estate Platform
```

### Backend Environment

Create `backend/.env` file:

```env
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/realestate_db
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-change-this-too
JWT_REFRESH_EXPIRES_IN=30d
BCRYPT_ROUNDS=12
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000
```

## 2. Installation & Startup

### Terminal 1 - Backend

```bash
cd backend
npm install
npm run dev
```

Backend will start on http://localhost:3001

### Terminal 2 - Frontend

```bash
npm install
npm run dev
```

Frontend will start on http://localhost:5173

## 3. First Time Setup

### Create Admin User

1. Go to http://localhost:5173/register
2. Register with your details
3. The first user becomes admin automatically

### Test the System

1. **Login**: Use your registered credentials
2. **Add Property**: Go to Dashboard → Add Property
3. **Browse Properties**: Visit All Listings page
4. **Test Search**: Use the search functionality
5. **Test Messaging**: Contact property owners

## 4. Key Features to Test

### User Features

- ✅ Registration and Login
- ✅ Profile Management
- ✅ Property Browsing
- ✅ Advanced Search & Filters
- ✅ Favorites System
- ✅ Messaging System

### Property Management

- ✅ Add New Property (5-step form)
- ✅ Upload Multiple Images
- ✅ Edit Property Details
- ✅ Property Status Management

### Admin Features

- ✅ User Management
- ✅ Property Moderation
- ✅ System Analytics
- ✅ Content Management

### Multi-language

- ✅ Switch between English, Pashto, Dari
- ✅ RTL support for Pashto and Dari
- ✅ Localized content

## 5. API Testing

### Health Check

```bash
curl http://localhost:3001/health
```

### Register User

```bash
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "name": "Test User"
  }'
```

### Get Properties

```bash
curl http://localhost:3001/api/properties
```

## 6. Troubleshooting

### Common Issues

**Backend won't start:**

- Check MongoDB is running
- Verify environment variables
- Check port 3001 is available

**Frontend won't start:**

- Check Node.js version (18+)
- Clear node_modules and reinstall
- Check port 5173 is available

**Database connection issues:**

- Ensure MongoDB is running
- Check MONGODB_URI in backend/.env
- Verify database permissions

**API calls failing:**

- Check VITE_API_URL in frontend .env
- Verify backend is running on port 3001
- Check browser console for CORS errors

**Axios import errors:**

- Clear Vite cache: `rm -rf node_modules/.vite`
- Clear node_modules: `rm -rf node_modules package-lock.json`
- Reinstall dependencies: `npm install`
- Restart development server: `npm run dev`

### Reset Database

```bash
# Connect to MongoDB
mongosh
use realestate_db
db.dropDatabase()
```

## 7. Production Deployment

For production deployment, see:

- `docs/DEPLOYMENT.md` - Complete deployment guide
- `docs/SYSTEM_SUMMARY.md` - System overview
- `README.md` - Full documentation

## 8. Development Tips

### Hot Reload

Both frontend and backend support hot reload:

- Frontend: Vite automatically reloads on changes
- Backend: Nodemon restarts server on changes

### Debugging

- Frontend: Use browser dev tools
- Backend: Check terminal logs
- Database: Use MongoDB Compass or mongosh

### Code Quality

```bash
# Frontend linting
npm run lint

# Backend linting
cd backend && npm run lint

# Type checking
npm run type-check
```

## 🎉 You're Ready!

The Real Estate Platform is now running and ready for use. Explore all the features and customize as needed for your specific requirements.

For detailed documentation, see the `docs/` folder and `README.md`.

Happy coding! 🏠✨
