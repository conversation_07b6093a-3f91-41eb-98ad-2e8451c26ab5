import React from 'react';
import { Link } from 'react-router-dom';
import {
  HomeIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  CheckCircleIcon,
  StarIcon,
  PhoneIcon,
  GlobeAltIcon,
  LightBulbIcon,
  HeartIcon,
  TrophyIcon
} from '@heroicons/react/24/outline';

const AboutPage: React.FC = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      position: "Founder & CEO",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "With over 15 years of experience in real estate, <PERSON> founded the company with a vision to transform property buying in Afghanistan."
    },
    {
      name: "<PERSON><PERSON>",
      position: "Chief Operations Officer",
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "<PERSON><PERSON> oversees all operations and ensures our platform delivers exceptional service to both buyers and sellers."
    },
    {
      name: "<PERSON>",
      position: "Head of Property Listings",
      image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Khalid works directly with property owners to ensure all listings meet our quality standards and provide accurate information."
    },
    {
      name: "Fatima Rahmani",
      position: "Customer Relations Manager",
      image: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80",
      bio: "Fatima leads our customer support team, ensuring every client receives personalized assistance throughout their journey."
    }
  ];

  const values = [
    {
      title: "Trust",
      description: "We verify every property listing to ensure accuracy and build trust with our users.",
      icon: CheckCircleIcon,
      color: "primary"
    },
    {
      title: "Innovation",
      description: "We continuously improve our platform with the latest technology to enhance user experience.",
      icon: LightBulbIcon,
      color: "secondary"
    },
    {
      title: "Excellence",
      description: "We strive for excellence in every aspect of our service, from property listings to customer support.",
      icon: TrophyIcon,
      color: "accent"
    },
    {
      title: "Community",
      description: "We're committed to supporting local communities and helping people find homes they love.",
      icon: HeartIcon,
      color: "primary"
    }
  ];

  const milestones = [
    { year: "2015", title: "Company Founded", description: "Started with a small team of 5 people in Kabul" },
    { year: "2017", title: "Expanded to Herat", description: "Opened our second office and expanded property listings" },
    { year: "2019", title: "Launched Online Platform", description: "Introduced our digital platform for property listings" },
    { year: "2021", title: "Reached 1000+ Properties", description: "Milestone of over 1000 active property listings" },
    { year: "2023", title: "Mobile App Launch", description: "Released our mobile application for iOS and Android" }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-primary py-24 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              About Our Company
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
              We're on a mission to transform real estate in Afghanistan through technology and trust
            </p>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float hidden lg:block" />
        <div className="absolute bottom-20 right-10 w-16 h-16 bg-white/10 rounded-full animate-float hidden lg:block" />
      </section>

      {/* Our Story Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="animate-slide-right">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                Our Story
              </h2>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                Founded in 2015, our real estate platform was born from a vision to transform the property market in Afghanistan. We recognized the challenges faced by both buyers and sellers in a fragmented market and set out to create a solution that brings transparency, trust, and efficiency to real estate transactions.
              </p>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                What started as a small team of passionate individuals has grown into a comprehensive platform serving thousands of users across multiple cities. Our journey has been defined by continuous innovation and an unwavering commitment to our users.
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                Today, we're proud to be Afghanistan's leading real estate platform, connecting property seekers with their dream homes and helping property owners find the right buyers and tenants.
              </p>
            </div>
            
            <div className="relative animate-slide-left">
              <div className="aspect-w-4 aspect-h-3 rounded-2xl overflow-hidden shadow-large">
                <img 
                  src="https://images.unsplash.com/photo-1577415124269-fc1140a69e91?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80" 
                  alt="Our office building"
                  className="object-cover w-full h-full"
                />
              </div>
              <div className="absolute -bottom-6 -left-6 w-48 h-48 bg-primary-50 rounded-lg -z-10" />
              <div className="absolute -top-6 -right-6 w-48 h-48 bg-secondary-50 rounded-lg -z-10" />
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Values
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              const colorClasses = {
                primary: 'bg-primary-500 text-white',
                secondary: 'bg-secondary-500 text-white', 
                accent: 'bg-accent-500 text-white'
              };
              
              return (
                <div 
                  key={value.title}
                  className="bg-white rounded-xl shadow-soft p-8 text-center group animate-slide-up hover:shadow-medium transition-all duration-300"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className={`w-16 h-16 ${colorClasses[value.color as keyof typeof colorClasses]} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="w-8 h-8" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {value.title}
                  </h3>
                  <p className="text-gray-600">
                    {value.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Company Timeline */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Journey
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Key milestones in our company's history
            </p>
          </div>

          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-primary-200 z-0" />
            
            <div className="relative z-10">
              {milestones.map((milestone, index) => (
                <div 
                  key={milestone.year}
                  className={`flex items-center mb-12 animate-fade-in ${index % 2 === 0 ? 'justify-start md:justify-end' : 'justify-start'}`}
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  <div className={`w-full md:w-5/12 ${index % 2 === 0 ? 'md:text-right md:pr-8' : 'md:pl-8'}`}>
                    <div className="bg-white rounded-xl shadow-soft p-6 hover:shadow-medium transition-all duration-300 transform hover:-translate-y-1">
                      <div className="text-primary-500 font-bold text-xl mb-2">{milestone.year}</div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">{milestone.title}</h3>
                      <p className="text-gray-600">{milestone.description}</p>
                    </div>
                  </div>
                  
                  <div className="absolute left-1/2 transform -translate-x-1/2 flex items-center justify-center">
                    <div className="w-8 h-8 bg-primary-500 rounded-full border-4 border-white shadow-md" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Meet Our Team
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The dedicated professionals behind our success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <div 
                key={member.name}
                className="bg-white rounded-xl shadow-soft overflow-hidden group animate-slide-up hover:shadow-medium transition-all duration-300"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="aspect-w-1 aspect-h-1 overflow-hidden">
                  <img 
                    src={member.image} 
                    alt={member.name}
                    className="object-cover w-full h-full group-hover:scale-110 transition-transform duration-500"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-1">
                    {member.name}
                  </h3>
                  <div className="text-primary-500 font-medium mb-4">{member.position}</div>
                  <p className="text-gray-600">
                    {member.bio}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6">
              Join Us in Transforming Real Estate
            </h2>
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Whether you're looking to buy, sell, or rent property, we're here to help you every step of the way
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <Link to="/listings" className="bg-white text-primary-500 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105">
                Browse Properties
              </Link>
              <Link to="/contact" className="border-2 border-white text-white hover:bg-white hover:text-primary-500 font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105">
                Contact Us
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;