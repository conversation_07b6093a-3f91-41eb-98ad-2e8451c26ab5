import mongoose, { Document, Schema } from 'mongoose';

export interface IFavorite extends Document {
  _id: string;
  user: mongoose.Types.ObjectId;
  property: mongoose.Types.ObjectId;
  createdAt: Date;
}

const favoriteSchema = new Schema<IFavorite>({
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  property: {
    type: Schema.Types.ObjectId,
    ref: 'Property',
    required: true
  }
}, {
  timestamps: { createdAt: true, updatedAt: false },
  toJSON: {
    transform: function(doc: any, ret: any) {
      const { _id, __v, ...rest } = ret;
      return { id: _id, ...rest };
    }
  }
});

// Ensure a user can only favorite a property once
favoriteSchema.index({ user: 1, property: 1 }, { unique: true });

// Other indexes
favoriteSchema.index({ user: 1 });
favoriteSchema.index({ property: 1 });
favoriteSchema.index({ createdAt: -1 });

export default mongoose.model<IFavorite>('Favorite', favoriteSchema);
