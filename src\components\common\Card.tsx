import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  clickable?: boolean;
  onClick?: () => void;
  className?: string;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  image?: {
    src: string;
    alt: string;
    position?: 'top' | 'left' | 'right';
  };
  loading?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  size = 'md',
  hover = false,
  clickable = false,
  onClick,
  className = '',
  header,
  footer,
  image,
  loading = false
}) => {
  const baseClasses = 'bg-white rounded-xl transition-all duration-300';
  
  const variantClasses = {
    default: 'shadow-soft border border-gray-100',
    elevated: 'shadow-large',
    outlined: 'border-2 border-gray-200',
    filled: 'bg-gray-50 border border-gray-200'
  };
  
  const sizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };
  
  const hoverClasses = hover || clickable 
    ? 'hover:shadow-large hover:-translate-y-1 transform cursor-pointer' 
    : '';
  
  const cardClasses = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${hoverClasses} ${className}`;
  
  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    hover: hover || clickable ? { y: -4, scale: 1.02 } : {},
    tap: clickable ? { scale: 0.98 } : {}
  };
  
  const CardComponent = clickable ? motion.button : motion.div;
  
  if (loading) {
    return (
      <div className={cardClasses}>
        <div className="animate-pulse space-y-4">
          {image && (
            <div className="h-48 bg-gray-300 rounded-lg"></div>
          )}
          {header && (
            <div className="h-6 bg-gray-300 rounded w-3/4"></div>
          )}
          <div className="space-y-3">
            <div className="h-4 bg-gray-300 rounded"></div>
            <div className="h-4 bg-gray-300 rounded w-5/6"></div>
            <div className="h-4 bg-gray-300 rounded w-4/6"></div>
          </div>
          {footer && (
            <div className="h-10 bg-gray-300 rounded"></div>
          )}
        </div>
      </div>
    );
  }
  
  return (
    <CardComponent
      className={cardClasses}
      onClick={clickable ? onClick : undefined}
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      whileTap="tap"
      transition={{ duration: 0.2 }}
    >
      {image && image.position === 'top' && (
        <div className="-m-6 mb-6 overflow-hidden rounded-t-xl">
          <img
            src={image.src}
            alt={image.alt}
            className="w-full h-48 object-cover transition-transform duration-300 hover:scale-105"
          />
        </div>
      )}
      
      <div className={`${image && image.position !== 'top' ? 'flex' : ''}`}>
        {image && image.position === 'left' && (
          <div className="flex-shrink-0 mr-6">
            <img
              src={image.src}
              alt={image.alt}
              className="w-24 h-24 object-cover rounded-lg"
            />
          </div>
        )}
        
        <div className="flex-1">
          {header && (
            <div className="mb-4 pb-4 border-b border-gray-100">
              {header}
            </div>
          )}
          
          <div className="space-y-4">
            {children}
          </div>
          
          {footer && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              {footer}
            </div>
          )}
        </div>
        
        {image && image.position === 'right' && (
          <div className="flex-shrink-0 ml-6">
            <img
              src={image.src}
              alt={image.alt}
              className="w-24 h-24 object-cover rounded-lg"
            />
          </div>
        )}
      </div>
    </CardComponent>
  );
};

// Specialized card components
export const StatsCard: React.FC<{
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease';
  };
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'accent' | 'success' | 'warning' | 'danger';
}> = ({ title, value, change, icon, color = 'primary' }) => {
  const colorClasses = {
    primary: 'bg-primary-500 text-white',
    secondary: 'bg-secondary-500 text-white',
    accent: 'bg-accent-500 text-white',
    success: 'bg-green-500 text-white',
    warning: 'bg-yellow-500 text-white',
    danger: 'bg-red-500 text-white'
  };
  
  return (
    <Card hover className="relative overflow-hidden">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-3xl font-bold text-gray-900">{value}</p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              change.type === 'increase' ? 'text-green-600' : 'text-red-600'
            }`}>
              <span className={`inline-block w-0 h-0 mr-1 ${
                change.type === 'increase' 
                  ? 'border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-green-600'
                  : 'border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-red-600'
              }`} />
              {Math.abs(change.value)}%
            </div>
          )}
        </div>
        {icon && (
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${colorClasses[color]}`}>
            {icon}
          </div>
        )}
      </div>
    </Card>
  );
};

export const FeatureCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
}> = ({ icon, title, description, action }) => (
  <Card hover className="text-center">
    <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
      <div className="text-white">
        {icon}
      </div>
    </div>
    <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
    <p className="text-gray-600 mb-4">{description}</p>
    {action && (
      <button
        onClick={action.onClick}
        className="text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200"
      >
        {action.label} →
      </button>
    )}
  </Card>
);

export default Card;
