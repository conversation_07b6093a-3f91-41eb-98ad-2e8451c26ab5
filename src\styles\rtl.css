/* RTL (Right-to-Left) Support for Arabic/Persian Languages */

/* Base RTL styles */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] body {
  font-family: 'Segoe UI', 'Tahom<PERSON>', 'Arial', sans-serif;
}

/* Flexbox direction adjustments */
[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row;
}

/* Space adjustments */
[dir="rtl"] .space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Margin and padding adjustments */
[dir="rtl"] .ml-1 { margin-left: 0; margin-right: 0.25rem; }
[dir="rtl"] .ml-2 { margin-left: 0; margin-right: 0.5rem; }
[dir="rtl"] .ml-3 { margin-left: 0; margin-right: 0.75rem; }
[dir="rtl"] .ml-4 { margin-left: 0; margin-right: 1rem; }
[dir="rtl"] .ml-6 { margin-left: 0; margin-right: 1.5rem; }
[dir="rtl"] .ml-8 { margin-left: 0; margin-right: 2rem; }

[dir="rtl"] .mr-1 { margin-right: 0; margin-left: 0.25rem; }
[dir="rtl"] .mr-2 { margin-right: 0; margin-left: 0.5rem; }
[dir="rtl"] .mr-3 { margin-right: 0; margin-left: 0.75rem; }
[dir="rtl"] .mr-4 { margin-right: 0; margin-left: 1rem; }
[dir="rtl"] .mr-6 { margin-right: 0; margin-left: 1.5rem; }
[dir="rtl"] .mr-8 { margin-right: 0; margin-left: 2rem; }

[dir="rtl"] .pl-1 { padding-left: 0; padding-right: 0.25rem; }
[dir="rtl"] .pl-2 { padding-left: 0; padding-right: 0.5rem; }
[dir="rtl"] .pl-3 { padding-left: 0; padding-right: 0.75rem; }
[dir="rtl"] .pl-4 { padding-left: 0; padding-right: 1rem; }
[dir="rtl"] .pl-6 { padding-left: 0; padding-right: 1.5rem; }
[dir="rtl"] .pl-8 { padding-left: 0; padding-right: 2rem; }
[dir="rtl"] .pl-10 { padding-left: 0; padding-right: 2.5rem; }

[dir="rtl"] .pr-1 { padding-right: 0; padding-left: 0.25rem; }
[dir="rtl"] .pr-2 { padding-right: 0; padding-left: 0.5rem; }
[dir="rtl"] .pr-3 { padding-right: 0; padding-left: 0.75rem; }
[dir="rtl"] .pr-4 { padding-right: 0; padding-left: 1rem; }
[dir="rtl"] .pr-6 { padding-right: 0; padding-left: 1.5rem; }
[dir="rtl"] .pr-8 { padding-right: 0; padding-left: 2rem; }
[dir="rtl"] .pr-10 { padding-right: 0; padding-left: 2.5rem; }

/* Position adjustments */
[dir="rtl"] .left-0 { left: auto; right: 0; }
[dir="rtl"] .left-1 { left: auto; right: 0.25rem; }
[dir="rtl"] .left-2 { left: auto; right: 0.5rem; }
[dir="rtl"] .left-3 { left: auto; right: 0.75rem; }
[dir="rtl"] .left-4 { left: auto; right: 1rem; }
[dir="rtl"] .left-6 { left: auto; right: 1.5rem; }

[dir="rtl"] .right-0 { right: auto; left: 0; }
[dir="rtl"] .right-1 { right: auto; left: 0.25rem; }
[dir="rtl"] .right-2 { right: auto; left: 0.5rem; }
[dir="rtl"] .right-3 { right: auto; left: 0.75rem; }
[dir="rtl"] .right-4 { right: auto; left: 1rem; }
[dir="rtl"] .right-6 { right: auto; left: 1.5rem; }

/* Text alignment */
[dir="rtl"] .text-left { text-align: right; }
[dir="rtl"] .text-right { text-align: left; }

/* Border radius adjustments */
[dir="rtl"] .rounded-l { border-radius: 0; border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem; }
[dir="rtl"] .rounded-r { border-radius: 0; border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem; }
[dir="rtl"] .rounded-l-lg { border-radius: 0; border-top-right-radius: 0.5rem; border-bottom-right-radius: 0.5rem; }
[dir="rtl"] .rounded-r-lg { border-radius: 0; border-top-left-radius: 0.5rem; border-bottom-left-radius: 0.5rem; }

/* Transform adjustments for icons */
[dir="rtl"] .transform-flip {
  transform: scaleX(-1);
}

/* Custom RTL utilities */
.rtl-flip {
  transform: scaleX(-1);
}

[dir="rtl"] .rtl-flip {
  transform: scaleX(1);
}

/* Dropdown positioning */
[dir="rtl"] .dropdown-menu {
  left: auto;
  right: 0;
}

/* Form input icons */
[dir="rtl"] .input-icon-left {
  left: auto;
  right: 0.75rem;
}

[dir="rtl"] .input-icon-right {
  right: auto;
  left: 0.75rem;
}

/* Navigation arrows */
[dir="rtl"] .nav-arrow-left {
  transform: rotate(180deg);
}

[dir="rtl"] .nav-arrow-right {
  transform: rotate(180deg);
}

/* Grid and layout adjustments */
[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

[dir="rtl"] .items-start {
  align-items: flex-end;
}

[dir="rtl"] .items-end {
  align-items: flex-start;
}

/* Animation adjustments */
[dir="rtl"] .slide-in-left {
  animation: slideInRight 0.3s ease-out;
}

[dir="rtl"] .slide-in-right {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Typography adjustments for Arabic/Persian */
[dir="rtl"] {
  font-feature-settings: "liga" 1, "kern" 1;
  text-rendering: optimizeLegibility;
}

[dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6 {
  font-weight: 600;
  line-height: 1.4;
}

[dir="rtl"] p {
  line-height: 1.6;
}

/* Form adjustments */
[dir="rtl"] input[type="text"],
[dir="rtl"] input[type="email"],
[dir="rtl"] input[type="password"],
[dir="rtl"] input[type="tel"],
[dir="rtl"] input[type="number"],
[dir="rtl"] textarea,
[dir="rtl"] select {
  text-align: right;
}

/* Button adjustments */
[dir="rtl"] .btn {
  flex-direction: row-reverse;
}

/* Card adjustments */
[dir="rtl"] .card {
  text-align: right;
}

/* Modal adjustments */
[dir="rtl"] .modal {
  text-align: right;
}

/* Notification adjustments */
[dir="rtl"] .notification {
  text-align: right;
}

/* Table adjustments */
[dir="rtl"] table {
  direction: rtl;
}

[dir="rtl"] th,
[dir="rtl"] td {
  text-align: right;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  [dir="rtl"] .mobile-menu {
    right: auto;
    left: 0;
  }
}

/* Print adjustments */
@media print {
  [dir="rtl"] {
    direction: rtl;
    text-align: right;
  }
}
