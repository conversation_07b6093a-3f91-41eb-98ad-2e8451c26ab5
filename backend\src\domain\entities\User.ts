/**
 * User Domain Entity
 * Represents the core business logic and rules for User entities
 */

export enum UserRole {
  USER = 'USER',
  AGENT = 'AGENT',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN'
}

export enum UserStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  DEACTIVATED = 'DEACTIVATED'
}

export enum VerificationStatus {
  PENDING = 'PENDING',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED'
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  bio?: string;
  dateOfBirth?: Date;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  nationality?: string;
  languages?: string[];
  address?: {
    street?: string;
    city?: string;
    province?: string;
    country?: string;
    postalCode?: string;
  };
}

export interface UserPreferences {
  language: string;
  currency: string;
  timezone: string;
  notifications: {
    email: boolean;
    sms: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    showProfile: boolean;
    showContact: boolean;
    showActivity: boolean;
  };
  searchPreferences: {
    defaultLocation?: string;
    priceRange?: {
      min: number;
      max: number;
    };
    propertyTypes?: string[];
  };
}

export interface UserSecuritySettings {
  twoFactorEnabled: boolean;
  twoFactorSecret?: string;
  lastPasswordChange: Date;
  loginAttempts: number;
  lockedUntil?: Date;
  trustedDevices: string[];
  securityQuestions?: {
    question: string;
    answerHash: string;
  }[];
}

export class User {
  constructor(
    public readonly id: string,
    public email: string,
    public passwordHash: string,
    public role: UserRole,
    public status: UserStatus,
    public verificationStatus: VerificationStatus,
    public profile: UserProfile,
    public preferences: UserPreferences,
    public securitySettings: UserSecuritySettings,
    public readonly createdAt: Date,
    public updatedAt: Date,
    public deletedAt?: Date
  ) {}

  // Business Logic Methods

  /**
   * Check if user can perform admin actions
   */
  public isAdmin(): boolean {
    return this.role === UserRole.ADMIN || this.role === UserRole.SUPER_ADMIN;
  }

  /**
   * Check if user can perform agent actions
   */
  public isAgent(): boolean {
    return this.role === UserRole.AGENT || this.isAdmin();
  }

  /**
   * Check if user account is active and verified
   */
  public isActiveAndVerified(): boolean {
    return this.status === UserStatus.ACTIVE && 
           this.verificationStatus === VerificationStatus.VERIFIED;
  }

  /**
   * Check if user can list properties
   */
  public canListProperties(): boolean {
    return this.isActiveAndVerified() && 
           (this.role === UserRole.AGENT || this.role === UserRole.USER);
  }

  /**
   * Check if user account is locked due to failed login attempts
   */
  public isAccountLocked(): boolean {
    return this.securitySettings.lockedUntil ? 
           this.securitySettings.lockedUntil > new Date() : false;
  }

  /**
   * Update user profile
   */
  public updateProfile(profileData: Partial<UserProfile>): void {
    this.profile = { ...this.profile, ...profileData };
    this.updatedAt = new Date();
  }

  /**
   * Update user preferences
   */
  public updatePreferences(preferences: Partial<UserPreferences>): void {
    this.preferences = { ...this.preferences, ...preferences };
    this.updatedAt = new Date();
  }

  /**
   * Enable two-factor authentication
   */
  public enableTwoFactor(secret: string): void {
    this.securitySettings.twoFactorEnabled = true;
    this.securitySettings.twoFactorSecret = secret;
    this.updatedAt = new Date();
  }

  /**
   * Disable two-factor authentication
   */
  public disableTwoFactor(): void {
    this.securitySettings.twoFactorEnabled = false;
    this.securitySettings.twoFactorSecret = undefined;
    this.updatedAt = new Date();
  }

  /**
   * Record failed login attempt
   */
  public recordFailedLogin(): void {
    this.securitySettings.loginAttempts += 1;
    
    // Lock account after 5 failed attempts for 30 minutes
    if (this.securitySettings.loginAttempts >= 5) {
      this.securitySettings.lockedUntil = new Date(Date.now() + 30 * 60 * 1000);
    }
    
    this.updatedAt = new Date();
  }

  /**
   * Reset login attempts after successful login
   */
  public resetLoginAttempts(): void {
    this.securitySettings.loginAttempts = 0;
    this.securitySettings.lockedUntil = undefined;
    this.updatedAt = new Date();
  }

  /**
   * Add trusted device
   */
  public addTrustedDevice(deviceId: string): void {
    if (!this.securitySettings.trustedDevices.includes(deviceId)) {
      this.securitySettings.trustedDevices.push(deviceId);
      this.updatedAt = new Date();
    }
  }

  /**
   * Remove trusted device
   */
  public removeTrustedDevice(deviceId: string): void {
    this.securitySettings.trustedDevices = this.securitySettings.trustedDevices
      .filter(id => id !== deviceId);
    this.updatedAt = new Date();
  }

  /**
   * Verify user account
   */
  public verify(): void {
    this.verificationStatus = VerificationStatus.VERIFIED;
    this.updatedAt = new Date();
  }

  /**
   * Suspend user account
   */
  public suspend(): void {
    this.status = UserStatus.SUSPENDED;
    this.updatedAt = new Date();
  }

  /**
   * Activate user account
   */
  public activate(): void {
    this.status = UserStatus.ACTIVE;
    this.updatedAt = new Date();
  }

  /**
   * Soft delete user account
   */
  public softDelete(): void {
    this.deletedAt = new Date();
    this.status = UserStatus.DEACTIVATED;
    this.updatedAt = new Date();
  }

  /**
   * Get user's full name
   */
  public getFullName(): string {
    return `${this.profile.firstName} ${this.profile.lastName}`.trim();
  }

  /**
   * Get user's display name (full name or email if name not available)
   */
  public getDisplayName(): string {
    const fullName = this.getFullName();
    return fullName || this.email;
  }

  /**
   * Check if user has specific permission
   */
  public hasPermission(permission: string): boolean {
    // Define role-based permissions
    const permissions = {
      [UserRole.USER]: ['read:properties', 'create:properties', 'update:own_properties'],
      [UserRole.AGENT]: ['read:properties', 'create:properties', 'update:properties', 'manage:clients'],
      [UserRole.ADMIN]: ['read:all', 'create:all', 'update:all', 'delete:properties', 'manage:users'],
      [UserRole.SUPER_ADMIN]: ['*'] // All permissions
    };

    const userPermissions = permissions[this.role] || [];
    return userPermissions.includes('*') || userPermissions.includes(permission);
  }

  /**
   * Convert to JSON representation
   */
  public toJSON(): any {
    return {
      id: this.id,
      email: this.email,
      role: this.role,
      status: this.status,
      verificationStatus: this.verificationStatus,
      profile: this.profile,
      preferences: this.preferences,
      securitySettings: {
        twoFactorEnabled: this.securitySettings.twoFactorEnabled,
        lastPasswordChange: this.securitySettings.lastPasswordChange,
        trustedDevices: this.securitySettings.trustedDevices.length
      },
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }
}
