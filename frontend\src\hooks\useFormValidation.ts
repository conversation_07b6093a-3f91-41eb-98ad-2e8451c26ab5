import { useForm, UseFormReturn, FieldValues, DefaultValues } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useCallback } from 'react';
import { showErrorToast, ValidationToasts } from '../utils/toast';

interface UseFormValidationOptions<T extends FieldValues> {
  schema: z.ZodSchema<T>;
  defaultValues?: DefaultValues<T>;
  mode?: 'onChange' | 'onBlur' | 'onSubmit' | 'onTouched' | 'all';
  reValidateMode?: 'onChange' | 'onBlur' | 'onSubmit';
  shouldFocusError?: boolean;
}

interface UseFormValidationReturn<T extends FieldValues> extends UseFormReturn<T> {
  handleSubmitWithValidation: (
    onValid: (data: T) => void | Promise<void>,
    onInvalid?: (errors: any) => void
  ) => (e?: React.BaseSyntheticEvent) => Promise<void>;
  validateField: (fieldName: keyof T) => Promise<boolean>;
  showFieldErrors: (fieldName: keyof T) => void;
  showAllErrors: () => void;
  isFieldValid: (fieldName: keyof T) => boolean;
  getFieldError: (fieldName: keyof T) => string | undefined;
}

export function useFormValidation<T extends FieldValues>({
  schema,
  defaultValues,
  mode = 'onBlur',
  reValidateMode = 'onChange',
  shouldFocusError = true,
}: UseFormValidationOptions<T>): UseFormValidationReturn<T> {
  
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues,
    mode,
    reValidateMode,
    shouldFocusError,
  });

  const { handleSubmit, trigger, formState: { errors }, setFocus } = form;

  // Handle form submission with validation
  const handleSubmitWithValidation = useCallback(
    (onValid: (data: T) => void | Promise<void>, onInvalid?: (errors: any) => void) => {
      return handleSubmit(
        async (data: T) => {
          try {
            await onValid(data);
          } catch (error) {
            console.error('Form submission error:', error);
            showErrorToast('An error occurred while submitting the form. Please try again.');
          }
        },
        (errors) => {
          // Show specific error messages for each field
          showAllErrors();
          
          // Focus on first error field
          if (shouldFocusError) {
            const firstErrorField = Object.keys(errors)[0] as keyof T;
            if (firstErrorField) {
              setFocus(firstErrorField);
            }
          }
          
          if (onInvalid) {
            onInvalid(errors);
          }
        }
      );
    },
    [handleSubmit, setFocus, shouldFocusError]
  );

  // Validate a specific field
  const validateField = useCallback(
    async (fieldName: keyof T): Promise<boolean> => {
      const result = await trigger(fieldName);
      if (!result) {
        showFieldErrors(fieldName);
      }
      return result;
    },
    [trigger]
  );

  // Show errors for a specific field
  const showFieldErrors = useCallback(
    (fieldName: keyof T) => {
      const error = errors[fieldName];
      if (error?.message) {
        showErrorToast(error.message as string);
      }
    },
    [errors]
  );

  // Show all form errors
  const showAllErrors = useCallback(() => {
    const errorMessages: string[] = [];
    
    Object.entries(errors).forEach(([field, error]) => {
      if (error?.message) {
        errorMessages.push(`${field}: ${error.message}`);
      }
    });

    if (errorMessages.length > 0) {
      // Show first error or combine multiple errors
      if (errorMessages.length === 1) {
        showErrorToast(errorMessages[0].split(': ')[1]); // Remove field name prefix
      } else {
        showErrorToast(`Please fix the following errors:\n${errorMessages.map(msg => `• ${msg.split(': ')[1]}`).join('\n')}`);
      }
    }
  }, [errors]);

  // Check if a field is valid
  const isFieldValid = useCallback(
    (fieldName: keyof T): boolean => {
      return !errors[fieldName];
    },
    [errors]
  );

  // Get error message for a field
  const getFieldError = useCallback(
    (fieldName: keyof T): string | undefined => {
      return errors[fieldName]?.message as string | undefined;
    },
    [errors]
  );

  return {
    ...form,
    handleSubmitWithValidation,
    validateField,
    showFieldErrors,
    showAllErrors,
    isFieldValid,
    getFieldError,
  };
}

// Specific validation hooks for common forms
export const useLoginForm = () => {
  return useFormValidation({
    schema: z.object({
      email: z.string().email('Please enter a valid email address').min(1, 'Email is required'),
      password: z.string().min(1, 'Password is required'),
      rememberMe: z.boolean().optional(),
    }),
    defaultValues: {
      email: '',
      password: '',
      rememberMe: false,
    },
  });
};

export const useRegisterForm = () => {
  return useFormValidation({
    schema: z.object({
      name: z.string()
        .min(2, 'Name must be at least 2 characters')
        .max(50, 'Name cannot exceed 50 characters')
        .regex(/^[a-zA-Z\s\u0600-\u06FF]+$/, 'Name can only contain letters and spaces'),
      email: z.string().email('Please enter a valid email address'),
      password: z.string()
        .min(8, 'Password must be at least 8 characters')
        .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
          'Password must contain uppercase, lowercase, number, and special character'),
      confirmPassword: z.string(),
      phone: z.string().optional(),
      role: z.enum(['USER', 'AGENT']).optional(),
      terms: z.boolean().refine(val => val === true, 'You must accept the terms and conditions'),
    }).refine(data => data.password === data.confirmPassword, {
      message: 'Passwords do not match',
      path: ['confirmPassword'],
    }),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      phone: '',
      role: 'USER' as const,
      terms: false,
    },
  });
};

export const usePropertyForm = () => {
  return useFormValidation({
    schema: z.object({
      title: z.string()
        .min(5, 'Title must be at least 5 characters')
        .max(200, 'Title cannot exceed 200 characters'),
      description: z.string()
        .min(20, 'Description must be at least 20 characters')
        .max(2000, 'Description cannot exceed 2000 characters'),
      price: z.number().min(1, 'Price must be greater than 0'),
      currency: z.enum(['AFN', 'USD', 'EUR']),
      category: z.enum(['SALE', 'RENT', 'LEASE']),
      type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'LAND', 'APARTMENT', 'HOUSE', 'VILLA', 'OFFICE', 'SHOP', 'WAREHOUSE', 'FARM']),
      province: z.string().min(2, 'Province is required'),
      city: z.string().min(2, 'City is required'),
      district: z.string().optional(),
      address: z.string().min(10, 'Address must be at least 10 characters'),
      area: z.number().min(1, 'Area must be at least 1 square meter'),
      bedrooms: z.number().min(0).optional(),
      bathrooms: z.number().min(0).optional(),
      yearBuilt: z.number().optional(),
      furnished: z.boolean().optional(),
      parking: z.boolean().optional(),
      garden: z.boolean().optional(),
      balcony: z.boolean().optional(),
      features: z.array(z.string()).optional(),
    }),
    defaultValues: {
      title: '',
      description: '',
      price: 0,
      currency: 'AFN' as const,
      category: 'SALE' as const,
      type: 'HOUSE' as const,
      province: '',
      city: '',
      district: '',
      address: '',
      area: 0,
      bedrooms: 0,
      bathrooms: 0,
      yearBuilt: new Date().getFullYear(),
      furnished: false,
      parking: false,
      garden: false,
      balcony: false,
      features: [],
    },
  });
};

export const useMessageForm = () => {
  return useFormValidation({
    schema: z.object({
      receiverId: z.string().min(1, 'Receiver is required'),
      propertyId: z.string().optional(),
      subject: z.string().max(200, 'Subject cannot exceed 200 characters').optional(),
      content: z.string()
        .min(1, 'Message content is required')
        .max(2000, 'Message cannot exceed 2000 characters'),
    }),
    defaultValues: {
      receiverId: '',
      propertyId: '',
      subject: '',
      content: '',
    },
  });
};

export const useProfileForm = () => {
  return useFormValidation({
    schema: z.object({
      name: z.string()
        .min(2, 'Name must be at least 2 characters')
        .max(50, 'Name cannot exceed 50 characters'),
      phone: z.string().optional(),
      bio: z.string().max(500, 'Bio cannot exceed 500 characters').optional(),
      location: z.string().max(100, 'Location cannot exceed 100 characters').optional(),
      website: z.string().url('Please enter a valid website URL').optional().or(z.literal('')),
    }),
    defaultValues: {
      name: '',
      phone: '',
      bio: '',
      location: '',
      website: '',
    },
  });
};
