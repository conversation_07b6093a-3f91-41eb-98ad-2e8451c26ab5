import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Squares2X2Icon,
  ListBulletIcon,
  MapIcon,
  FunnelIcon,
  ArrowsUpDownIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import PropertyCard from './PropertyCard';
import { PropertyCardSkeleton } from './Loading';
import Button from './Button';
import { useLanguage } from '../../contexts/LanguageContext';

interface Property {
  id: number;
  title: string;
  location: string;
  price: string;
  image: string;
  beds: number;
  baths: number;
  area: string;
  type: string;
  category: string;
  featured: boolean;
  views: number;
  saved: boolean;
}

interface SearchResultsProps {
  properties: Property[];
  loading?: boolean;
  totalCount?: number;
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  onViewModeChange?: (mode: 'grid' | 'list' | 'map') => void;
  onSortChange?: (sort: string) => void;
  onFilterToggle?: () => void;
  viewMode?: 'grid' | 'list' | 'map';
  sortBy?: string;
  className?: string;
}

const SearchResults: React.FC<SearchResultsProps> = ({
  properties,
  loading = false,
  totalCount = 0,
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  onViewModeChange,
  onSortChange,
  onFilterToggle,
  viewMode = 'grid',
  sortBy = 'newest',
  className = ''
}) => {
  const [savedProperties, setSavedProperties] = useState<Set<number>>(new Set());
  const { t, isRTL } = useLanguage();
  
  const sortOptions = [
    { value: 'newest', label: t('sort.newest') },
    { value: 'oldest', label: t('sort.oldest') },
    { value: 'price-low', label: t('sort.priceLow') },
    { value: 'price-high', label: t('sort.priceHigh') },
    { value: 'area-large', label: t('sort.areaLarge') },
    { value: 'area-small', label: t('sort.areaSmall') }
  ];
  
  const handleToggleSaved = (propertyId: number) => {
    setSavedProperties(prev => {
      const newSet = new Set(prev);
      if (newSet.has(propertyId)) {
        newSet.delete(propertyId);
      } else {
        newSet.add(propertyId);
      }
      return newSet;
    });
  };
  
  const renderViewModeButtons = () => (
    <div className="flex items-center bg-gray-100 rounded-lg p-1">
      <button
        onClick={() => onViewModeChange?.('grid')}
        className={`p-2 rounded-md transition-all duration-200 ${
          viewMode === 'grid' 
            ? 'bg-white text-primary-600 shadow-sm' 
            : 'text-gray-500 hover:text-gray-700'
        }`}
      >
        <Squares2X2Icon className="w-5 h-5" />
      </button>
      <button
        onClick={() => onViewModeChange?.('list')}
        className={`p-2 rounded-md transition-all duration-200 ${
          viewMode === 'list' 
            ? 'bg-white text-primary-600 shadow-sm' 
            : 'text-gray-500 hover:text-gray-700'
        }`}
      >
        <ListBulletIcon className="w-5 h-5" />
      </button>
      <button
        onClick={() => onViewModeChange?.('map')}
        className={`p-2 rounded-md transition-all duration-200 ${
          viewMode === 'map' 
            ? 'bg-white text-primary-600 shadow-sm' 
            : 'text-gray-500 hover:text-gray-700'
        }`}
      >
        <MapIcon className="w-5 h-5" />
      </button>
    </div>
  );
  
  const renderSortDropdown = () => (
    <div className="relative">
      <select
        value={sortBy}
        onChange={(e) => onSortChange?.(e.target.value)}
        className="appearance-none bg-white border border-gray-300 rounded-lg px-4 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
      >
        {sortOptions.map(option => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
    </div>
  );
  
  const renderPagination = () => {
    if (totalPages <= 1) return null;
    
    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return (
      <div className="flex items-center justify-center space-x-2 mt-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange?.(currentPage - 1)}
          disabled={currentPage === 1}
        >
          {t('pagination.previous')}
        </Button>
        
        {pages.map(page => (
          <Button
            key={page}
            variant={page === currentPage ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => onPageChange?.(page)}
            className="min-w-[40px]"
          >
            {page}
          </Button>
        ))}
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => onPageChange?.(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          {t('pagination.next')}
        </Button>
      </div>
    );
  };
  
  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="h-6 bg-gray-200 rounded w-48 animate-pulse"></div>
          <div className="flex items-center space-x-4">
            <div className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
            <div className="h-10 bg-gray-200 rounded w-24 animate-pulse"></div>
          </div>
        </div>
        
        <div className={`grid gap-6 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
            : 'grid-cols-1'
        }`}>
          {Array.from({ length: 6 }).map((_, index) => (
            <PropertyCardSkeleton key={index} />
          ))}
        </div>
      </div>
    );
  }
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            {t('search.results')} ({totalCount.toLocaleString()})
          </h2>
          <p className="text-sm text-gray-500 mt-1">
            {t('search.page')} {currentPage} {t('search.of')} {totalPages}
          </p>
        </div>
        
        <div className={`flex items-center space-x-4 ${isRTL ? 'space-x-reverse' : ''}`}>
          <Button
            variant="outline"
            size="sm"
            onClick={onFilterToggle}
            icon={<FunnelIcon className="w-4 h-4" />}
          >
            {t('search.filters')}
          </Button>
          
          {renderSortDropdown()}
          {renderViewModeButtons()}
        </div>
      </div>
      
      {/* Results */}
      <AnimatePresence mode="wait">
        {properties.length > 0 ? (
          <motion.div
            key={viewMode}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' 
                : 'grid-cols-1'
            }`}
          >
            {properties.map((property, index) => (
              <PropertyCard
                key={property.id}
                property={{
                  ...property,
                  saved: savedProperties.has(property.id)
                }}
                viewMode={viewMode}
                onToggleSaved={handleToggleSaved}
                animationDelay={index * 100}
              />
            ))}
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <ArrowsUpDownIcon className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {t('search.noResults')}
            </h3>
            <p className="text-gray-500">
              {t('search.noResultsDescription')}
            </p>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Pagination */}
      {renderPagination()}
    </div>
  );
};

export default SearchResults;
