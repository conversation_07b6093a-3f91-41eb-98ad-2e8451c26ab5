import axios from 'axios';
import { toast } from 'react-hot-toast';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
            refreshToken,
          });

          const { accessToken } = response.data.data.tokens;
          localStorage.setItem('accessToken', accessToken);

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Handle other errors
    const message = error.response?.data?.message || 'An error occurred';
    toast.error(message);

    return Promise.reject(error);
  }
);

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  meta?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  role: 'USER' | 'AGENT' | 'ADMIN' | 'SUPER_ADMIN';
  isActive: boolean;
  isVerified: boolean;
  bio?: string;
  location?: string;
  website?: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  phone?: string;
  role?: string;
}

// Property Types
export interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  category: 'SALE' | 'RENT' | 'LEASE';
  type: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SOLD' | 'RENTED' | 'INACTIVE';
  province: string;
  city: string;
  district?: string;
  address: string;
  latitude?: number;
  longitude?: number;
  bedrooms?: number;
  bathrooms?: number;
  area: number;
  yearBuilt?: number;
  furnished: boolean;
  parking: boolean;
  garden: boolean;
  balcony: boolean;
  features: string[];
  images: PropertyImage[];
  owner: User;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
}

export interface PropertyImage {
  url: string;
  filename: string;
  size?: number;
  mimeType?: string;
  isMain: boolean;
  order: number;
}

export interface PropertyFilters {
  page?: number;
  limit?: number;
  category?: string;
  type?: string;
  province?: string;
  city?: string;
  district?: string;
  minPrice?: number;
  maxPrice?: number;
  minArea?: number;
  maxArea?: number;
  bedrooms?: number;
  bathrooms?: number;
  furnished?: boolean;
  parking?: boolean;
  garden?: boolean;
  balcony?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Message Types
export interface Message {
  id: string;
  subject?: string;
  content: string;
  isRead: boolean;
  sender: User;
  receiver: User;
  property?: Property;
  createdAt: string;
  updatedAt: string;
  readAt?: string;
}

// API Service Class
class ApiService {
  // Auth endpoints
  async login(credentials: LoginCredentials): Promise<ApiResponse<{ user: User; tokens: AuthTokens }>> {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  }

  async register(data: RegisterData): Promise<ApiResponse<{ user: User; tokens: AuthTokens }>> {
    const response = await api.post('/auth/register', data);
    return response.data;
  }

  async logout(): Promise<ApiResponse> {
    const response = await api.post('/auth/logout');
    return response.data;
  }

  async getProfile(): Promise<ApiResponse<{ user: User }>> {
    const response = await api.get('/auth/profile');
    return response.data;
  }

  async updateProfile(data: Partial<User>): Promise<ApiResponse<{ user: User }>> {
    const response = await api.put('/auth/profile', data);
    return response.data;
  }

  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<ApiResponse> {
    const response = await api.put('/auth/change-password', data);
    return response.data;
  }

  // Property endpoints
  async getProperties(filters?: PropertyFilters): Promise<ApiResponse<Property[]>> {
    const response = await api.get('/properties', { params: filters });
    return response.data;
  }

  async getProperty(id: string): Promise<ApiResponse<Property>> {
    const response = await api.get(`/properties/${id}`);
    return response.data;
  }

  async createProperty(data: FormData): Promise<ApiResponse<Property>> {
    const response = await api.post('/properties', data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  }

  async updateProperty(id: string, data: FormData): Promise<ApiResponse<Property>> {
    const response = await api.put(`/properties/${id}`, data, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  }

  async deleteProperty(id: string): Promise<ApiResponse> {
    const response = await api.delete(`/properties/${id}`);
    return response.data;
  }

  async getUserProperties(filters?: { page?: number; limit?: number; status?: string }): Promise<ApiResponse<Property[]>> {
    const response = await api.get('/properties/user/my-properties', { params: filters });
    return response.data;
  }

  async searchProperties(query: string): Promise<ApiResponse<Property[]>> {
    const response = await api.get('/properties/search', { params: { q: query } });
    return response.data;
  }

  // User endpoints
  async getFavorites(filters?: { page?: number; limit?: number }): Promise<ApiResponse<Property[]>> {
    const response = await api.get('/users/favorites', { params: filters });
    return response.data;
  }

  async addToFavorites(propertyId: string): Promise<ApiResponse> {
    const response = await api.post(`/users/favorites/${propertyId}`);
    return response.data;
  }

  async removeFromFavorites(propertyId: string): Promise<ApiResponse> {
    const response = await api.delete(`/users/favorites/${propertyId}`);
    return response.data;
  }

  async getDashboardStats(): Promise<ApiResponse<any>> {
    const response = await api.get('/users/dashboard-stats');
    return response.data;
  }

  // Message endpoints
  async getMessages(filters?: { page?: number; limit?: number; type?: 'sent' | 'received' }): Promise<ApiResponse<Message[]>> {
    const response = await api.get('/messages', { params: filters });
    return response.data;
  }

  async getMessage(id: string): Promise<ApiResponse<Message>> {
    const response = await api.get(`/messages/${id}`);
    return response.data;
  }

  async sendMessage(data: { receiverId: string; propertyId?: string; subject?: string; content: string }): Promise<ApiResponse<Message>> {
    const response = await api.post('/messages', data);
    return response.data;
  }

  async markMessageAsRead(id: string): Promise<ApiResponse> {
    const response = await api.put(`/messages/${id}/read`);
    return response.data;
  }

  async deleteMessage(id: string): Promise<ApiResponse> {
    const response = await api.delete(`/messages/${id}`);
    return response.data;
  }

  async getConversations(): Promise<ApiResponse<any[]>> {
    const response = await api.get('/messages/conversations');
    return response.data;
  }

  async getConversationMessages(otherUserId: string, filters?: { page?: number; limit?: number }): Promise<ApiResponse<Message[]>> {
    const response = await api.get(`/messages/conversations/${otherUserId}`, { params: filters });
    return response.data;
  }

  // File upload endpoints
  async uploadImage(file: File): Promise<ApiResponse<{ url: string; filename: string }>> {
    const formData = new FormData();
    formData.append('image', file);
    const response = await api.post('/upload/image', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  }

  async uploadImages(files: File[]): Promise<ApiResponse<{ url: string; filename: string }[]>> {
    const formData = new FormData();
    files.forEach(file => formData.append('images', file));
    const response = await api.post('/upload/images', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
    });
    return response.data;
  }
}

export const apiService = new ApiService();
export default api;
