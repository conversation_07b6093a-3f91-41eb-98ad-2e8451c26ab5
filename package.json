{"name": "realestate", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^5.2.1", "axios": "^1.6.2", "framer-motion": "^10.16.16", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^7.2.0", "i18next-http-backend": "^2.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.1", "zod": "^3.25.76"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.16", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}