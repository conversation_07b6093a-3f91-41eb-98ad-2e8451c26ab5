import mongoose, { Document, Schema } from 'mongoose';

export interface IPropertyImage {
  url: string;
  filename: string;
  size?: number;
  mimeType?: string;
  isMain: boolean;
  order: number;
}

export interface IProperty extends Document {
  _id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  category: 'SALE' | 'RENT' | 'LEASE';
  type: 'RESIDENTIAL' | 'COMMERCIAL' | 'LAND' | 'APARTMENT' | 'HOUSE' | 'VILLA' | 'OFFICE' | 'SHOP' | 'WAREHOUSE' | 'FARM';
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SOLD' | 'RENTED' | 'INACTIVE';
  
  // Location
  province: string;
  city: string;
  district?: string;
  address: string;
  latitude?: number;
  longitude?: number;
  
  // Property details
  bedrooms?: number;
  bathrooms?: number;
  area: number; // in square meters
  yearBuilt?: number;
  furnished: boolean;
  parking: boolean;
  garden: boolean;
  balcony: boolean;
  
  // Features
  features: string[];
  
  // Media
  images: IPropertyImage[];
  
  // Relations
  owner: mongoose.Types.ObjectId;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
}

const propertyImageSchema = new Schema<IPropertyImage>({
  url: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: true
  },
  size: Number,
  mimeType: String,
  isMain: {
    type: Boolean,
    default: false
  },
  order: {
    type: Number,
    default: 0
  }
}, { _id: false });

const propertySchema = new Schema<IProperty>({
  title: {
    type: String,
    required: [true, 'Property title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    required: [true, 'Property description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  price: {
    type: Number,
    required: [true, 'Property price is required'],
    min: [0, 'Price cannot be negative']
  },
  currency: {
    type: String,
    default: 'AFN',
    enum: ['AFN', 'USD', 'EUR']
  },
  category: {
    type: String,
    required: [true, 'Property category is required'],
    enum: ['SALE', 'RENT', 'LEASE']
  },
  type: {
    type: String,
    required: [true, 'Property type is required'],
    enum: ['RESIDENTIAL', 'COMMERCIAL', 'LAND', 'APARTMENT', 'HOUSE', 'VILLA', 'OFFICE', 'SHOP', 'WAREHOUSE', 'FARM']
  },
  status: {
    type: String,
    enum: ['PENDING', 'APPROVED', 'REJECTED', 'SOLD', 'RENTED', 'INACTIVE'],
    default: 'PENDING'
  },
  
  // Location
  province: {
    type: String,
    required: [true, 'Province is required'],
    trim: true
  },
  city: {
    type: String,
    required: [true, 'City is required'],
    trim: true
  },
  district: {
    type: String,
    trim: true
  },
  address: {
    type: String,
    required: [true, 'Address is required'],
    trim: true
  },
  latitude: {
    type: Number,
    min: [-90, 'Latitude must be between -90 and 90'],
    max: [90, 'Latitude must be between -90 and 90']
  },
  longitude: {
    type: Number,
    min: [-180, 'Longitude must be between -180 and 180'],
    max: [180, 'Longitude must be between -180 and 180']
  },
  
  // Property details
  bedrooms: {
    type: Number,
    min: [0, 'Bedrooms cannot be negative']
  },
  bathrooms: {
    type: Number,
    min: [0, 'Bathrooms cannot be negative']
  },
  area: {
    type: Number,
    required: [true, 'Property area is required'],
    min: [1, 'Area must be at least 1 square meter']
  },
  yearBuilt: {
    type: Number,
    min: [1800, 'Year built cannot be before 1800'],
    max: [new Date().getFullYear() + 5, 'Year built cannot be more than 5 years in the future']
  },
  furnished: {
    type: Boolean,
    default: false
  },
  parking: {
    type: Boolean,
    default: false
  },
  garden: {
    type: Boolean,
    default: false
  },
  balcony: {
    type: Boolean,
    default: false
  },
  
  // Features
  features: [{
    type: String,
    trim: true
  }],
  
  // Media
  images: [propertyImageSchema],
  
  // Relations
  owner: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  publishedAt: Date
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc: any, ret: any) {
      const { _id, __v, ...rest } = ret;
      return { id: _id, ...rest };
    }
  }
});

// Indexes
propertySchema.index({ owner: 1 });
propertySchema.index({ category: 1 });
propertySchema.index({ type: 1 });
propertySchema.index({ status: 1 });
propertySchema.index({ province: 1, city: 1 });
propertySchema.index({ price: 1 });
propertySchema.index({ area: 1 });
propertySchema.index({ createdAt: -1 });
propertySchema.index({ publishedAt: -1 });

// Compound indexes for search
propertySchema.index({ 
  category: 1, 
  type: 1, 
  province: 1, 
  city: 1, 
  status: 1 
});

propertySchema.index({ 
  price: 1, 
  area: 1, 
  bedrooms: 1, 
  bathrooms: 1 
});

// Text index for search
propertySchema.index({
  title: 'text',
  description: 'text',
  address: 'text',
  features: 'text'
});

// Geospatial index for location-based queries
propertySchema.index({ 
  latitude: 1, 
  longitude: 1 
});

export default mongoose.model<IProperty>('Property', propertySchema);
