import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';
import { 
  MagnifyingGlassIcon,
  MapPinIcon,
  HomeIcon,
  UserGroupIcon,
  CheckCircleIcon,
  StarIcon,
  ArrowRightIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

const HomePage: React.FC = () => {
  const { t, isRTL } = useLanguage();
  const featuredProperties = [
    {
      id: 1,
      title: "Modern Villa in Wazir Akbar Khan",
      location: "Kabul, Afghanistan",
      price: "$250,000",
      image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      beds: 4,
      baths: 3,
      area: "2,500 sq ft",
      type: "For Sale",
      featured: true
    },
    {
      id: 2,
      title: "Luxury Apartment in Shahr-e-Naw",
      location: "Kabul, Afghanistan",
      price: "$180,000",
      image: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      beds: 3,
      baths: 2,
      area: "1,800 sq ft",
      type: "For Sale",
      featured: false
    },
    {
      id: 3,
      title: "Commercial Building in Chicken Street",
      location: "Kabul, Afghanistan",
      price: "$450,000",
      image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
      beds: 0,
      baths: 4,
      area: "5,000 sq ft",
      type: "For Sale",
      featured: false
    }
  ];

  const stats = [
    { label: "Properties Listed", value: "1,247", icon: HomeIcon },
    { label: "Happy Customers", value: "856", icon: UserGroupIcon },
    { label: "Cities Covered", value: "12", icon: MapPinIcon },
    { label: "Years Experience", value: "8", icon: StarIcon }
  ];

  const howItWorks = [
    {
      step: "01",
      title: "Search Properties",
      description: "Browse through our extensive collection of verified properties",
      icon: MagnifyingGlassIcon,
      color: "primary"
    },
    {
      step: "02", 
      title: "Contact Owner",
      description: "Connect directly with property owners through our platform",
      icon: UserGroupIcon,
      color: "secondary"
    },
    {
      step: "03",
      title: "Close Deal",
      description: "Complete your property transaction with our expert support",
      icon: CheckCircleIcon,
      color: "accent"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-hero min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.4'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>

        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Find Your Dream
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300">
                Property
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-12 max-w-3xl mx-auto leading-relaxed">
              Discover the best real estate opportunities in Afghanistan with our trusted platform
            </p>

            {/* Search Bar */}
            <div className="max-w-4xl mx-auto mb-12 animate-slide-up">
              <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-large">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="relative">
                    <MapPinIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Location"
                      className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                    />
                  </div>
                  
                  <select className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300">
                    <option>Property Type</option>
                    <option>House</option>
                    <option>Apartment</option>
                    <option>Commercial</option>
                    <option>Land</option>
                  </select>
                  
                  <select className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300">
                    <option>Price Range</option>
                    <option>$50k - $100k</option>
                    <option>$100k - $200k</option>
                    <option>$200k - $500k</option>
                    <option>$500k+</option>
                  </select>
                  
                  <button className="btn-primary w-full flex items-center justify-center space-x-2">
                    <MagnifyingGlassIcon className="w-5 h-5" />
                    <span>Search</span>
                  </button>
                </div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6 animate-slide-up">
              <Link to="/listings" className="btn-primary text-lg px-8 py-4 flex items-center space-x-2">
                <span>Browse Properties</span>
                <ArrowRightIcon className="w-5 h-5" />
              </Link>
              
              <button className="flex items-center space-x-3 text-white hover:text-yellow-300 transition-colors duration-300 group">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300">
                  <PlayIcon className="w-6 h-6 ml-1" />
                </div>
                <span className="text-lg font-medium">Watch Demo</span>
              </button>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float hidden lg:block" />
        <div className="absolute bottom-20 right-10 w-16 h-16 bg-white/10 rounded-full animate-float hidden lg:block" />
        <div className="absolute top-1/2 right-20 w-12 h-12 bg-white/10 rounded-full animate-bounce-gentle hidden lg:block" />
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div 
                  key={stat.label}
                  className="text-center group animate-slide-up"
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Properties */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Featured Properties
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover our handpicked selection of premium properties
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProperties.map((property, index) => (
              <div 
                key={property.id}
                className="card card-hover animate-slide-up"
                style={{ animationDelay: `${index * 150}ms` }}
              >
                <div className="relative overflow-hidden rounded-t-xl">
                  <img 
                    src={property.image} 
                    alt={property.title}
                    className="w-full h-64 object-cover transform group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">{property.type}</span>
                  </div>
                  {property.featured && (
                    <div className="absolute top-4 right-4">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-accent-100 text-accent-800">Featured</span>
                    </div>
                  )}
                  <div className="absolute bottom-4 right-4">
                    <div className="text-2xl font-bold text-white bg-black/50 px-3 py-1 rounded-lg backdrop-blur-sm">
                      {property.price}
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 line-clamp-2">
                    {property.title}
                  </h3>
                  <div className="flex items-center text-gray-600 mb-4">
                    <MapPinIcon className="w-4 h-4 mr-1" />
                    <span className="text-sm">{property.location}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-6">
                    {property.beds > 0 && (
                      <div className="flex items-center">
                        <span className="font-medium">{property.beds}</span>
                        <span className="ml-1">Beds</span>
                      </div>
                    )}
                    <div className="flex items-center">
                      <span className="font-medium">{property.baths}</span>
                      <span className="ml-1">Baths</span>
                    </div>
                    <div className="flex items-center">
                      <span className="font-medium">{property.area}</span>
                    </div>
                  </div>
                  
                  <Link 
                    to={`/property/${property.id}`}
                    className="btn-outline w-full text-center block"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <Link to="/listings" className="btn-primary text-lg px-8 py-4">
              View All Properties
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              How It Works
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Simple steps to find your perfect property
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {howItWorks.map((step, index) => {
              const Icon = step.icon;
              const colorClasses = {
                primary: 'bg-primary-500 text-white',
                secondary: 'bg-secondary-500 text-white', 
                accent: 'bg-accent-500 text-white'
              };
              
              return (
                <div 
                  key={step.step}
                  className="text-center group animate-slide-up"
                  style={{ animationDelay: `${index * 200}ms` }}
                >
                  <div className="relative mb-8">
                    <div className={`w-20 h-20 ${colorClasses[step.color as keyof typeof colorClasses]} rounded-full flex items-center justify-center mx-auto group-hover:scale-110 transition-transform duration-300 shadow-large`}>
                      <Icon className="w-10 h-10" />
                    </div>
                    <div className="absolute -top-2 -right-2 w-8 h-8 bg-gray-900 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {step.step}
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-primary">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="animate-fade-in">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-6">
              Ready to Find Your Dream Property?
            </h2>
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Join thousands of satisfied customers who found their perfect home through our platform
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <Link to="/listings" className="bg-white text-primary-500 hover:bg-gray-100 font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105">
                Browse Properties
              </Link>
              <Link to="/post-property" className="border-2 border-white text-white hover:bg-white hover:text-primary-500 font-semibold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105">
                List Your Property
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
