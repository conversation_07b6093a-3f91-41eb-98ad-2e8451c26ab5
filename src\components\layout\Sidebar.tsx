import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HomeIcon,
  BuildingOfficeIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon,
  UserIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  UsersIcon,
  DocumentTextIcon,
  BellIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';

interface SidebarProps {
  type?: 'dashboard' | 'admin';
}

interface MenuItem {
  id: string;
  label: string;
  icon: React.ComponentType<any>;
  path: string;
  badge?: number;
  children?: MenuItem[];
}

const Sidebar: React.FC<SidebarProps> = ({ type = 'dashboard' }) => {
  const { t, isRTL } = useLanguage();
  const { user, logout } = useAuth();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const dashboardMenuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: HomeIcon,
      path: '/dashboard'
    },
    {
      id: 'listings',
      label: 'My Listings',
      icon: BuildingOfficeIcon,
      path: '/dashboard/listings'
    },
    {
      id: 'messages',
      label: 'Messages',
      icon: ChatBubbleLeftRightIcon,
      path: '/messages',
      badge: 3
    },
    {
      id: 'favorites',
      label: 'Favorites',
      icon: HeartIcon,
      path: '/favorites'
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: UserIcon,
      path: '/profile'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: Cog6ToothIcon,
      path: '/settings'
    }
  ];

  const adminMenuItems: MenuItem[] = [
    {
      id: 'admin-dashboard',
      label: 'Admin Dashboard',
      icon: ChartBarIcon,
      path: '/admin'
    },
    {
      id: 'users',
      label: 'User Management',
      icon: UsersIcon,
      path: '/admin/users'
    },
    {
      id: 'properties',
      label: 'Property Management',
      icon: BuildingOfficeIcon,
      path: '/admin/properties'
    },
    {
      id: 'reports',
      label: 'Reports',
      icon: DocumentTextIcon,
      path: '/admin/reports',
      children: [
        {
          id: 'analytics',
          label: 'Analytics',
          icon: ChartBarIcon,
          path: '/admin/reports/analytics'
        },
        {
          id: 'revenue',
          label: 'Revenue',
          icon: ChartBarIcon,
          path: '/admin/reports/revenue'
        }
      ]
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: BellIcon,
      path: '/admin/notifications',
      badge: 5
    }
  ];

  const menuItems = type === 'admin' ? adminMenuItems : dashboardMenuItems;

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const renderMenuItem = (item: MenuItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = isActive(item.path);

    return (
      <div key={item.id}>
        <div className="relative">
          {hasChildren ? (
            <button
              onClick={() => toggleExpanded(item.id)}
              className={`w-full flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 group ${
                active
                  ? 'bg-gradient-primary text-white shadow-md'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              } ${level > 0 ? 'ml-4' : ''}`}
            >
              <div className="flex items-center">
                <item.icon className={`w-5 h-5 ${isRTL ? 'ml-3' : 'mr-3'} ${
                  active ? 'text-white' : 'text-gray-400 group-hover:text-gray-500'
                }`} />
                <span>{item.label}</span>
              </div>
              <div className="flex items-center space-x-2">
                {item.badge && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    {item.badge}
                  </span>
                )}
                {isExpanded ? (
                  <ChevronDownIcon className="w-4 h-4" />
                ) : (
                  <ChevronRightIcon className="w-4 h-4" />
                )}
              </div>
            </button>
          ) : (
            <Link
              to={item.path}
              className={`flex items-center justify-between px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 group ${
                active
                  ? 'bg-gradient-primary text-white shadow-md'
                  : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
              } ${level > 0 ? 'ml-4' : ''}`}
            >
              <div className="flex items-center">
                <item.icon className={`w-5 h-5 ${isRTL ? 'ml-3' : 'mr-3'} ${
                  active ? 'text-white' : 'text-gray-400 group-hover:text-gray-500'
                }`} />
                <span>{item.label}</span>
              </div>
              {item.badge && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  {item.badge}
                </span>
              )}
            </Link>
          )}
        </div>

        <AnimatePresence>
          {hasChildren && isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden"
            >
              <div className="mt-1 space-y-1">
                {item.children?.map(child => renderMenuItem(child, level + 1))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    );
  };

  return (
    <motion.div
      initial={{ x: isRTL ? 100 : -100 }}
      animate={{ x: 0 }}
      className="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200"
    >
      <div className="flex flex-col h-full">
        {/* User Profile Section */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
              <UserIcon className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.name || 'User Name'}
              </p>
              <p className="text-xs text-gray-500 truncate">
                {user?.email || '<EMAIL>'}
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {menuItems.map(item => renderMenuItem(item))}
        </nav>

        {/* Logout Button */}
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={logout}
            className="w-full flex items-center px-4 py-3 text-sm font-medium text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200"
          >
            <ArrowRightOnRectangleIcon className={`w-5 h-5 ${isRTL ? 'ml-3' : 'mr-3'}`} />
            <span>Logout</span>
          </button>
        </div>
      </div>
    </motion.div>
  );
};

export default Sidebar;
