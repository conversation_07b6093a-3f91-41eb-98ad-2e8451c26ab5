import { Router } from 'express';
import {
  getUserProfile,
  getFavorites,
  addToFavorites,
  removeFromFavorites,
  getDashboardStats,
  getUsers,
  updateUserStatus,
  deleteUser
} from '../controllers/userController';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

// Specific routes MUST come before parameterized routes to avoid conflicts
router.get('/dashboard/stats', authenticate, getDashboardStats);
router.get('/favorites', (req, res, next) => {
  console.log('🔍 /favorites route hit');
  next();
}, authenticate, getFavorites);
router.post('/favorites/:propertyId', authenticate, addToFavorites);
router.delete('/favorites/:propertyId', authenticate, removeFromFavorites);

// Admin only routes (specific paths)
router.get('/', authenticate, authorize('ADMIN', 'SUPER_ADMIN'), getUsers);

// Parameterized routes MUST come last
router.get('/:id', (req, res, next) => {
  console.log('👤 /:id route hit with id:', req.params.id);
  next();
}, getUserProfile);
router.put('/:id/status', authenticate, authorize('ADMIN', 'SUPER_ADMIN'), updateUserStatus);
router.delete('/:id', authenticate, authorize('SUPER_ADMIN'), deleteUser);

export default router;
