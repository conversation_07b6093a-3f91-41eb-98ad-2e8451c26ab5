import { Request, Response, NextFunction } from 'express';

export interface CustomError extends Error {
  statusCode?: number;
  code?: string | number;
  keyValue?: any;
  errors?: any;
  errorCode?: string;
  details?: any;
}

export const errorHandler = (
  err: CustomError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', err);

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Invalid ID format. Please provide a valid resource ID.';
    error = { ...error, message, statusCode: 404, errorCode: 'INVALID_ID' };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const field = Object.keys(err.keyValue || {})[0];
    const value = err.keyValue?.[field];
    let message = 'Duplicate field value entered';

    if (field === 'email') {
      message = `An account with email "${value}" already exists. Please use a different email or try logging in.`;
    } else if (field === 'phone') {
      message = `This phone number "${value}" is already registered. Please use a different phone number.`;
    } else {
      message = `The ${field} "${value}" is already taken. Please choose a different value.`;
    }

    error = { ...error, message, statusCode: 400, errorCode: 'DUPLICATE_FIELD' };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const errors = Object.values(err.errors || {}).map((val: any) => {
      if (val.kind === 'required') {
        return `${val.path} is required`;
      } else if (val.kind === 'minlength') {
        return `${val.path} must be at least ${val.properties?.minlength} characters long`;
      } else if (val.kind === 'maxlength') {
        return `${val.path} cannot exceed ${val.properties?.maxlength} characters`;
      } else if (val.kind === 'min') {
        return `${val.path} must be at least ${val.properties?.min}`;
      } else if (val.kind === 'max') {
        return `${val.path} cannot exceed ${val.properties?.max}`;
      } else if (val.kind === 'enum') {
        return `${val.path} must be one of: ${val.properties?.enumValues?.join(', ')}`;
      } else if (val.kind === 'user defined') {
        return val.message;
      }
      return val.message;
    });

    const message = errors.join('. ');
    error = { ...error, message, statusCode: 400, errorCode: 'VALIDATION_ERROR' };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid authentication token. Please log in again.';
    error = { ...error, message, statusCode: 401, errorCode: 'INVALID_TOKEN' };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Your session has expired. Please log in again.';
    error = { ...error, message, statusCode: 401, errorCode: 'TOKEN_EXPIRED' };
  }

  // Prisma errors
  if (err.code === 'P2002') {
    const message = 'Duplicate field value entered';
    error = { ...error, message, statusCode: 400 };
  }

  if (err.code === 'P2025') {
    const message = 'Record not found';
    error = { ...error, message, statusCode: 404 };
  }

  res.status(error.statusCode || 500).json({
    success: false,
    error: {
      message: error.message || 'An unexpected error occurred. Please try again later.',
      code: error.errorCode || 'INTERNAL_ERROR',
      ...(error.details && { details: error.details }),
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  });
};

export class AppError extends Error {
  statusCode: number;
  status: string;
  isOperational: boolean;
  errorCode?: string;
  details?: any;

  constructor(message: string, statusCode: number, errorCode?: string, details?: any) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.errorCode = errorCode;
    this.details = details;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    fn(req, res, next).catch(next);
  };
};
