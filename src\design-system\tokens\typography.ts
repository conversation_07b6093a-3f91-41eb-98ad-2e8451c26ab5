/**
 * Design System - Typography Tokens
 * Professional typography system with support for multiple languages including RTL
 */

export const typography = {
  // Font Families
  fontFamily: {
    sans: [
      'Inter',
      '-apple-system',
      'BlinkMacSystemFont',
      'Segoe UI',
      'Roboto',
      'Helvetica Neue',
      'Arial',
      'sans-serif'
    ],
    serif: [
      'Georgia',
      'Cambria',
      'Times New Roman',
      'Times',
      'serif'
    ],
    mono: [
      'JetBrains Mono',
      'Fira Code',
      'Monaco',
      'Consolas',
      'Liberation Mono',
      'Courier New',
      'monospace'
    ],
    // RTL Language Support
    arabic: [
      'Noto Sans Arabic',
      'Amiri',
      'Scheherazade',
      'Arial Unicode MS',
      'sans-serif'
    ],
    persian: [
      '<PERSON><PERSON><PERSON>',
      '<PERSON>hom<PERSON>',
      'Arial Unicode MS',
      'sans-serif'
    ],
    pashto: [
      'Noto Sans Arabic',
      '<PERSON>i',
      'Arial Unicode MS',
      'sans-serif'
    ]
  },

  // Font Sizes
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem', // 60px
    '7xl': '4.5rem',  // 72px
    '8xl': '6rem',    // 96px
    '9xl': '8rem'     // 128px
  },

  // Font Weights
  fontWeight: {
    thin: '100',
    extralight: '200',
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
    black: '900'
  },

  // Line Heights
  lineHeight: {
    none: '1',
    tight: '1.25',
    snug: '1.375',
    normal: '1.5',
    relaxed: '1.625',
    loose: '2'
  },

  // Letter Spacing
  letterSpacing: {
    tighter: '-0.05em',
    tight: '-0.025em',
    normal: '0em',
    wide: '0.025em',
    wider: '0.05em',
    widest: '0.1em'
  },

  // Text Styles (Semantic Typography)
  textStyles: {
    // Display Styles
    display: {
      '2xl': {
        fontSize: '4.5rem',
        lineHeight: '1',
        fontWeight: '800',
        letterSpacing: '-0.025em'
      },
      xl: {
        fontSize: '3.75rem',
        lineHeight: '1',
        fontWeight: '800',
        letterSpacing: '-0.025em'
      },
      lg: {
        fontSize: '3rem',
        lineHeight: '1',
        fontWeight: '800',
        letterSpacing: '-0.025em'
      },
      md: {
        fontSize: '2.25rem',
        lineHeight: '1.111',
        fontWeight: '700',
        letterSpacing: '-0.025em'
      },
      sm: {
        fontSize: '1.875rem',
        lineHeight: '1.2',
        fontWeight: '600',
        letterSpacing: '-0.025em'
      }
    },

    // Heading Styles
    heading: {
      h1: {
        fontSize: '2.25rem',
        lineHeight: '1.2',
        fontWeight: '700',
        letterSpacing: '-0.025em'
      },
      h2: {
        fontSize: '1.875rem',
        lineHeight: '1.3',
        fontWeight: '600',
        letterSpacing: '-0.025em'
      },
      h3: {
        fontSize: '1.5rem',
        lineHeight: '1.375',
        fontWeight: '600',
        letterSpacing: '-0.025em'
      },
      h4: {
        fontSize: '1.25rem',
        lineHeight: '1.4',
        fontWeight: '600',
        letterSpacing: '0em'
      },
      h5: {
        fontSize: '1.125rem',
        lineHeight: '1.5',
        fontWeight: '600',
        letterSpacing: '0em'
      },
      h6: {
        fontSize: '1rem',
        lineHeight: '1.5',
        fontWeight: '600',
        letterSpacing: '0em'
      }
    },

    // Body Text Styles
    body: {
      xl: {
        fontSize: '1.25rem',
        lineHeight: '1.6',
        fontWeight: '400',
        letterSpacing: '0em'
      },
      lg: {
        fontSize: '1.125rem',
        lineHeight: '1.6',
        fontWeight: '400',
        letterSpacing: '0em'
      },
      md: {
        fontSize: '1rem',
        lineHeight: '1.5',
        fontWeight: '400',
        letterSpacing: '0em'
      },
      sm: {
        fontSize: '0.875rem',
        lineHeight: '1.5',
        fontWeight: '400',
        letterSpacing: '0em'
      },
      xs: {
        fontSize: '0.75rem',
        lineHeight: '1.5',
        fontWeight: '400',
        letterSpacing: '0em'
      }
    },

    // Label Styles
    label: {
      lg: {
        fontSize: '1rem',
        lineHeight: '1.5',
        fontWeight: '500',
        letterSpacing: '0em'
      },
      md: {
        fontSize: '0.875rem',
        lineHeight: '1.5',
        fontWeight: '500',
        letterSpacing: '0em'
      },
      sm: {
        fontSize: '0.75rem',
        lineHeight: '1.5',
        fontWeight: '500',
        letterSpacing: '0.025em'
      }
    },

    // Caption Styles
    caption: {
      lg: {
        fontSize: '0.875rem',
        lineHeight: '1.4',
        fontWeight: '400',
        letterSpacing: '0em'
      },
      md: {
        fontSize: '0.75rem',
        lineHeight: '1.4',
        fontWeight: '400',
        letterSpacing: '0.025em'
      },
      sm: {
        fontSize: '0.6875rem',
        lineHeight: '1.4',
        fontWeight: '400',
        letterSpacing: '0.025em'
      }
    },

    // Button Styles
    button: {
      lg: {
        fontSize: '1.125rem',
        lineHeight: '1.5',
        fontWeight: '600',
        letterSpacing: '0em'
      },
      md: {
        fontSize: '1rem',
        lineHeight: '1.5',
        fontWeight: '600',
        letterSpacing: '0em'
      },
      sm: {
        fontSize: '0.875rem',
        lineHeight: '1.5',
        fontWeight: '600',
        letterSpacing: '0em'
      },
      xs: {
        fontSize: '0.75rem',
        lineHeight: '1.5',
        fontWeight: '600',
        letterSpacing: '0.025em'
      }
    },

    // Code Styles
    code: {
      lg: {
        fontSize: '1rem',
        lineHeight: '1.6',
        fontWeight: '400',
        letterSpacing: '0em',
        fontFamily: 'mono'
      },
      md: {
        fontSize: '0.875rem',
        lineHeight: '1.6',
        fontWeight: '400',
        letterSpacing: '0em',
        fontFamily: 'mono'
      },
      sm: {
        fontSize: '0.75rem',
        lineHeight: '1.6',
        fontWeight: '400',
        letterSpacing: '0em',
        fontFamily: 'mono'
      }
    }
  },

  // RTL Typography Adjustments
  rtl: {
    // Adjusted line heights for Arabic scripts
    lineHeight: {
      tight: '1.4',
      normal: '1.7',
      relaxed: '1.8'
    },
    // Adjusted letter spacing for RTL languages
    letterSpacing: {
      normal: '0.01em',
      wide: '0.03em',
      wider: '0.06em'
    }
  },

  // Responsive Typography
  responsive: {
    // Mobile-first breakpoints
    sm: {
      display: {
        '2xl': { fontSize: '3rem' },
        xl: { fontSize: '2.5rem' },
        lg: { fontSize: '2rem' },
        md: { fontSize: '1.75rem' },
        sm: { fontSize: '1.5rem' }
      },
      heading: {
        h1: { fontSize: '1.875rem' },
        h2: { fontSize: '1.5rem' },
        h3: { fontSize: '1.25rem' },
        h4: { fontSize: '1.125rem' },
        h5: { fontSize: '1rem' },
        h6: { fontSize: '0.875rem' }
      }
    },
    md: {
      display: {
        '2xl': { fontSize: '4rem' },
        xl: { fontSize: '3.25rem' },
        lg: { fontSize: '2.5rem' },
        md: { fontSize: '2rem' },
        sm: { fontSize: '1.75rem' }
      }
    },
    lg: {
      display: {
        '2xl': { fontSize: '4.5rem' },
        xl: { fontSize: '3.75rem' },
        lg: { fontSize: '3rem' },
        md: { fontSize: '2.25rem' },
        sm: { fontSize: '1.875rem' }
      }
    }
  }
} as const;

// Typography utility functions
export const getTextStyle = (category: string, size: string) => {
  const styles = typography.textStyles as any;
  return styles[category]?.[size] || {};
};

export const getFontFamily = (type: keyof typeof typography.fontFamily): string => {
  return typography.fontFamily[type].join(', ');
};

export const createResponsiveTextStyle = (
  baseStyle: any,
  breakpoints: Record<string, any>
) => {
  return {
    ...baseStyle,
    '@media (min-width: 640px)': breakpoints.sm || {},
    '@media (min-width: 768px)': breakpoints.md || {},
    '@media (min-width: 1024px)': breakpoints.lg || {},
    '@media (min-width: 1280px)': breakpoints.xl || {}
  };
};

// Export types
export type FontFamily = keyof typeof typography.fontFamily;
export type FontSize = keyof typeof typography.fontSize;
export type FontWeight = keyof typeof typography.fontWeight;
export type LineHeight = keyof typeof typography.lineHeight;
export type LetterSpacing = keyof typeof typography.letterSpacing;
export type TextStyleCategory = keyof typeof typography.textStyles;
export type TextStyleSize<T extends TextStyleCategory> = keyof typeof typography.textStyles[T];
