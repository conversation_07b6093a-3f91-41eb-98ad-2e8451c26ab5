import { Request, Response, NextFunction } from 'express';
import { User, Property, Favorite, Message } from '../models';
import { AppError, catchAsync } from '../middleware/errorHandler';

export const getUserProfile = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  console.log('👤 getUserProfile called - id:', req.params.id);
  const { id } = req.params;

  const user = await User.findById(id).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Get user statistics
  const [propertiesCount, favoritesCount] = await Promise.all([
    Property.countDocuments({ owner: id }),
    Favorite.countDocuments({ user: id })
  ]);

  const userWithStats = {
    ...user.toJSON(),
    stats: {
      properties: propertiesCount,
      favorites: favoritesCount
    }
  };

  res.json({
    success: true,
    data: userWithStats
  });
});

export const getFavorites = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  console.log('🔍 getFavorites called - user:', req.user?._id);
  const userId = req.user._id;
  const { page = 1, limit = 20 } = req.query as any;

  const skip = (Number(page) - 1) * Number(limit);

  const [favorites, total] = await Promise.all([
    Favorite.find({ user: userId })
      .populate({
        path: 'property',
        populate: {
          path: 'owner',
          select: 'name email phone avatar'
        }
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit)),
    Favorite.countDocuments({ user: userId })
  ]);

  const totalPages = Math.ceil(total / Number(limit));

  // Always return success, even if no favorites found
  res.json({
    success: true,
    data: favorites.map(fav => fav.property), // Empty array is valid data
    meta: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    },
    message: total === 0 ? 'No favorites found' : undefined
  });
});

export const addToFavorites = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;
  const { propertyId } = req.params;

  // Check if property exists
  const property = await Property.findById(propertyId);
  if (!property) {
    return next(new AppError('Property not found', 404));
  }

  // Check if already favorited
  const existingFavorite = await Favorite.findOne({
    user: userId,
    property: propertyId
  });

  if (existingFavorite) {
    return next(new AppError('Property already in favorites', 400));
  }

  // Add to favorites
  const favorite = new Favorite({
    user: userId,
    property: propertyId
  });

  await favorite.save();

  res.status(201).json({
    success: true,
    message: 'Property added to favorites'
  });
});

export const removeFromFavorites = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;
  const { propertyId } = req.params;

  const favorite = await Favorite.findOneAndDelete({
    user: userId,
    property: propertyId
  });

  if (!favorite) {
    return next(new AppError('Property not in favorites', 404));
  }

  res.json({
    success: true,
    message: 'Property removed from favorites'
  });
});

export const getDashboardStats = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;

  const [
    totalProperties,
    activeProperties,
    totalFavorites,
    unreadMessages,
    recentProperties
  ] = await Promise.all([
    Property.countDocuments({ owner: userId }),
    Property.countDocuments({ owner: userId, status: 'APPROVED' }),
    Favorite.countDocuments({ user: userId }),
    Message.countDocuments({ receiver: userId, isRead: false }),
    Property.find({ owner: userId })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title price category status createdAt')
  ]);

  const stats = {
    totalProperties,
    activeProperties,
    totalFavorites,
    unreadMessages,
    recentProperties
  };

  res.json({
    success: true,
    data: stats
  });
});

export const getUsers = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { page = 1, limit = 20, role, isActive } = req.query as any;

  const filter: any = {};
  if (role) filter.role = role;
  if (isActive !== undefined) filter.isActive = isActive === 'true';

  const skip = (Number(page) - 1) * Number(limit);

  const [users, total] = await Promise.all([
    User.find(filter)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit)),
    User.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(total / Number(limit));

  res.json({
    success: true,
    data: users,
    meta: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    }
  });
});

export const updateUserStatus = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const { isActive } = req.body;

  const user = await User.findByIdAndUpdate(
    id,
    { isActive },
    { new: true }
  ).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.json({
    success: true,
    data: user,
    message: `User ${isActive ? 'activated' : 'deactivated'} successfully`
  });
});

export const deleteUser = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  // Check if user exists
  const user = await User.findById(id);
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Delete user and related data
  await Promise.all([
    User.findByIdAndDelete(id),
    Property.deleteMany({ owner: id }),
    Favorite.deleteMany({ user: id }),
    Message.deleteMany({ $or: [{ sender: id }, { receiver: id }] })
  ]);

  res.json({
    success: true,
    message: 'User and related data deleted successfully'
  });
});
