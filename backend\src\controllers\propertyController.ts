import { Request, Response, NextFunction } from 'express';
import { Property, User, Favorite, Review } from '../models';
import { AppError, catchAsync } from '../middleware/errorHandler';
import { PropertyCreateInput, PropertyUpdateInput, PropertySearchParams } from '../types/api';

export const getProperties = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const {
    page = 1,
    limit = 20,
    category,
    type,
    province,
    city,
    district,
    minPrice,
    maxPrice,
    minArea,
    maxArea,
    bedrooms,
    bathrooms,
    furnished,
    parking,
    garden,
    balcony,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query as any;

  // Build filter object
  const filter: any = { status: 'APPROVED' };

  if (category) filter.category = category;
  if (type) filter.type = type;
  if (province) filter.province = new RegExp(province, 'i');
  if (city) filter.city = new RegExp(city, 'i');
  if (district) filter.district = new RegExp(district, 'i');
  
  if (minPrice || maxPrice) {
    filter.price = {};
    if (minPrice) filter.price.$gte = Number(minPrice);
    if (maxPrice) filter.price.$lte = Number(maxPrice);
  }
  
  if (minArea || maxArea) {
    filter.area = {};
    if (minArea) filter.area.$gte = Number(minArea);
    if (maxArea) filter.area.$lte = Number(maxArea);
  }

  if (bedrooms) filter.bedrooms = Number(bedrooms);
  if (bathrooms) filter.bathrooms = Number(bathrooms);
  if (furnished !== undefined) filter.furnished = furnished === 'true';
  if (parking !== undefined) filter.parking = parking === 'true';
  if (garden !== undefined) filter.garden = garden === 'true';
  if (balcony !== undefined) filter.balcony = balcony === 'true';

  // Calculate pagination
  const skip = (Number(page) - 1) * Number(limit);
  const sortObj: any = {};
  sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;

  // Execute query
  const [properties, total] = await Promise.all([
    Property.find(filter)
      .populate('owner', 'name email phone avatar')
      .sort(sortObj)
      .skip(skip)
      .limit(Number(limit)),
    Property.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(total / Number(limit));

  // Always return success, even if no properties found
  res.json({
    success: true,
    data: properties, // Empty array is valid data
    meta: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    },
    message: total === 0 ? 'No properties found matching your criteria' : undefined
  });
});

export const getProperty = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const property = await Property.findById(id)
    .populate('owner', 'name email phone avatar bio location')
    .populate({
      path: 'reviews',
      populate: {
        path: 'reviewer',
        select: 'name avatar'
      }
    });

  if (!property) {
    return next(new AppError('Property not found', 404));
  }

  // Calculate average rating
  const reviews = await Review.find({ property: id });
  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0;

  // Check if current user has favorited this property
  let isFavorited = false;
  if (req.user) {
    const favorite = await Favorite.findOne({ 
      user: req.user._id, 
      property: id 
    });
    isFavorited = !!favorite;
  }

  const propertyWithDetails = {
    ...property.toJSON(),
    averageRating: Math.round(averageRating * 10) / 10,
    isFavorited,
    _count: {
      favorites: await Favorite.countDocuments({ property: id }),
      reviews: reviews.length
    }
  };

  res.json({
    success: true,
    data: propertyWithDetails
  });
});

export const createProperty = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const propertyData: PropertyCreateInput = req.body;
  const ownerId = req.user._id;

  const property = new Property({
    ...propertyData,
    owner: ownerId
  });

  await property.save();
  await property.populate('owner', 'name email phone avatar');

  res.status(201).json({
    success: true,
    data: property,
    message: 'Property created successfully'
  });
});

export const updateProperty = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const updateData: PropertyUpdateInput = req.body;
  const userId = req.user._id;

  const property = await Property.findById(id);

  if (!property) {
    return next(new AppError('Property not found', 404));
  }

  // Check if user owns the property or is admin
  if (property.owner.toString() !== userId.toString() && req.user.role !== 'ADMIN') {
    return next(new AppError('Not authorized to update this property', 403));
  }

  const updatedProperty = await Property.findByIdAndUpdate(
    id,
    updateData,
    { new: true, runValidators: true }
  ).populate('owner', 'name email phone avatar');

  res.json({
    success: true,
    data: updatedProperty,
    message: 'Property updated successfully'
  });
});

export const deleteProperty = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user._id;

  const property = await Property.findById(id);

  if (!property) {
    return next(new AppError('Property not found', 404));
  }

  // Check if user owns the property or is admin
  if (property.owner.toString() !== userId.toString() && req.user.role !== 'ADMIN') {
    return next(new AppError('Not authorized to delete this property', 403));
  }

  await Property.findByIdAndDelete(id);

  res.json({
    success: true,
    message: 'Property deleted successfully'
  });
});

export const getUserProperties = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;
  const { page = 1, limit = 20, status } = req.query as any;

  const filter: any = { owner: userId };
  if (status) filter.status = status;

  const skip = (Number(page) - 1) * Number(limit);

  const [properties, total] = await Promise.all([
    Property.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit)),
    Property.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(total / Number(limit));

  res.json({
    success: true,
    data: properties,
    meta: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    }
  });
});

export const searchProperties = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { q, ...filters } = req.query as any;

  let searchFilter: any = { status: 'APPROVED' };

  // Text search
  if (q) {
    searchFilter.$text = { $search: q };
  }

  // Apply other filters (reuse logic from getProperties)
  // ... (similar filter logic as in getProperties)

  const properties = await Property.find(searchFilter)
    .populate('owner', 'name email phone avatar')
    .sort({ score: { $meta: 'textScore' } })
    .limit(50);

  res.json({
    success: true,
    data: properties
  });
});
