import React, { useState } from 'react';
import { 
  MagnifyingGlassIcon,
  MapPinIcon,
  HomeIcon,
  CurrencyDollarIcon,
  AdjustmentsHorizontalIcon,
  XMarkIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface SearchFilters {
  query: string;
  location: string;
  propertyType: string;
  category: string;
  minPrice: string;
  maxPrice: string;
  bedrooms: string;
  bathrooms: string;
  minArea: string;
  maxArea: string;
  features: string[];
  sortBy: string;
}

interface AdvancedSearchProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onSearch: () => void;
  isExpanded: boolean;
  onToggleExpanded: () => void;
  resultCount?: number;
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  filters,
  onFiltersChange,
  onSearch,
  isExpanded,
  onToggleExpanded,
  resultCount
}) => {
  const propertyTypes = ['Any', 'House', 'Apartment', 'Villa', 'Commercial', 'Land', 'Office'];
  const categories = ['Any', 'For Sale', 'For Rent'];
  const locations = ['Any Location', 'Kabul', 'Herat', 'Kandahar', 'Mazar-i-Sharif', 'Jalalabad'];
  const bedroomOptions = ['Any', '1', '2', '3', '4', '5', '6+'];
  const bathroomOptions = ['Any', '1', '2', '3', '4', '5+'];
  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'price-low', label: 'Price: Low to High' },
    { value: 'price-high', label: 'Price: High to Low' },
    { value: 'popular', label: 'Most Popular' },
    { value: 'area-large', label: 'Largest Area' }
  ];
  
  const features = [
    'Swimming Pool', 'Gym', 'Security', 'Elevator', 'Central Heating',
    'Air Conditioning', 'Fireplace', 'Terrace', 'Storage Room', 'Laundry Room',
    'Garden', 'Balcony', 'Parking', 'Furnished', 'Pet Friendly'
  ];

  const handleInputChange = (field: keyof SearchFilters, value: string) => {
    onFiltersChange({ ...filters, [field]: value });
  };

  const handleFeatureToggle = (feature: string) => {
    const newFeatures = filters.features.includes(feature)
      ? filters.features.filter(f => f !== feature)
      : [...filters.features, feature];
    onFiltersChange({ ...filters, features: newFeatures });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      query: '',
      location: '',
      propertyType: '',
      category: '',
      minPrice: '',
      maxPrice: '',
      bedrooms: '',
      bathrooms: '',
      minArea: '',
      maxArea: '',
      features: [],
      sortBy: 'newest'
    });
  };

  const hasActiveFilters = () => {
    return filters.query || filters.location || filters.propertyType || 
           filters.category || filters.minPrice || filters.maxPrice ||
           filters.bedrooms || filters.bathrooms || filters.minArea ||
           filters.maxArea || filters.features.length > 0;
  };

  return (
    <div className="bg-white rounded-xl shadow-soft overflow-hidden">
      {/* Main Search Bar */}
      <div className="p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search Input */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search properties, locations, or keywords..."
                value={filters.query}
                onChange={(e) => handleInputChange('query', e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 text-lg"
              />
            </div>
          </div>

          {/* Quick Filters */}
          <div className="flex flex-wrap lg:flex-nowrap gap-3">
            <select
              value={filters.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 min-w-[150px]"
            >
              {locations.map(location => (
                <option key={location} value={location === 'Any Location' ? '' : location}>
                  {location}
                </option>
              ))}
            </select>

            <select
              value={filters.propertyType}
              onChange={(e) => handleInputChange('propertyType', e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 min-w-[130px]"
            >
              {propertyTypes.map(type => (
                <option key={type} value={type === 'Any' ? '' : type}>
                  {type}
                </option>
              ))}
            </select>

            <select
              value={filters.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300 min-w-[120px]"
            >
              {categories.map(category => (
                <option key={category} value={category === 'Any' ? '' : category}>
                  {category}
                </option>
              ))}
            </select>

            {/* Advanced Filters Toggle */}
            <button
              onClick={onToggleExpanded}
              className={`flex items-center space-x-2 px-4 py-3 border rounded-lg transition-all duration-300 ${
                isExpanded || hasActiveFilters()
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-300 text-gray-600 hover:border-gray-400'
              }`}
            >
              <AdjustmentsHorizontalIcon className="w-5 h-5" />
              <span>Filters</span>
              {hasActiveFilters() && (
                <span className="bg-primary-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                  {filters.features.length + 
                   (filters.minPrice ? 1 : 0) + 
                   (filters.maxPrice ? 1 : 0) + 
                   (filters.bedrooms ? 1 : 0) + 
                   (filters.bathrooms ? 1 : 0) + 
                   (filters.minArea ? 1 : 0) + 
                   (filters.maxArea ? 1 : 0)}
                </span>
              )}
            </button>

            {/* Search Button */}
            <button
              onClick={onSearch}
              className="btn-primary flex items-center space-x-2 px-6"
            >
              <MagnifyingGlassIcon className="w-5 h-5" />
              <span>Search</span>
            </button>
          </div>
        </div>

        {/* Results Count */}
        {resultCount !== undefined && (
          <div className="mt-4 text-sm text-gray-600">
            {resultCount} properties found
            {hasActiveFilters() && (
              <button
                onClick={clearAllFilters}
                className="ml-4 text-primary-500 hover:text-primary-600 transition-colors duration-300"
              >
                Clear all filters
              </button>
            )}
          </div>
        )}
      </div>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-6 bg-gray-50 animate-slide-down">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Price Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Price Range
              </label>
              <div className="grid grid-cols-2 gap-3">
                <input
                  type="text"
                  placeholder="Min Price"
                  value={filters.minPrice}
                  onChange={(e) => handleInputChange('minPrice', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                />
                <input
                  type="text"
                  placeholder="Max Price"
                  value={filters.maxPrice}
                  onChange={(e) => handleInputChange('maxPrice', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                />
              </div>
            </div>

            {/* Bedrooms & Bathrooms */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Bedrooms & Bathrooms
              </label>
              <div className="grid grid-cols-2 gap-3">
                <select
                  value={filters.bedrooms}
                  onChange={(e) => handleInputChange('bedrooms', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                >
                  {bedroomOptions.map(option => (
                    <option key={option} value={option === 'Any' ? '' : option}>
                      {option === 'Any' ? 'Any Beds' : `${option} Bed${option !== '1' ? 's' : ''}`}
                    </option>
                  ))}
                </select>
                <select
                  value={filters.bathrooms}
                  onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                >
                  {bathroomOptions.map(option => (
                    <option key={option} value={option === 'Any' ? '' : option}>
                      {option === 'Any' ? 'Any Baths' : `${option} Bath${option !== '1' ? 's' : ''}`}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Area Range */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Area (sq ft)
              </label>
              <div className="grid grid-cols-2 gap-3">
                <input
                  type="text"
                  placeholder="Min Area"
                  value={filters.minArea}
                  onChange={(e) => handleInputChange('minArea', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                />
                <input
                  type="text"
                  placeholder="Max Area"
                  value={filters.maxArea}
                  onChange={(e) => handleInputChange('maxArea', e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                />
              </div>
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Sort By
              </label>
              <select
                value={filters.sortBy}
                onChange={(e) => handleInputChange('sortBy', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Features */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Features & Amenities
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
              {features.map(feature => (
                <label key={feature} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.features.includes(feature)}
                    onChange={() => handleFeatureToggle(feature)}
                    className="rounded border-gray-300 text-primary-500 focus:ring-primary-200 mr-2"
                  />
                  <span className="text-sm text-gray-700">{feature}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="mt-6 flex items-center justify-between">
            <button
              onClick={clearAllFilters}
              className="text-gray-500 hover:text-gray-700 transition-colors duration-300"
            >
              Clear all filters
            </button>
            <div className="flex items-center space-x-3">
              <button
                onClick={onToggleExpanded}
                className="btn-ghost"
              >
                Close Filters
              </button>
              <button
                onClick={onSearch}
                className="btn-primary"
              >
                Apply Filters
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdvancedSearch;
