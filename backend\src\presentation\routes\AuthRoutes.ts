/**
 * Professional Authentication Routes
 * Defines all authentication-related API endpoints with proper validation and middleware
 */

import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { AuthController } from '../controllers/AuthController';
import { AuthMiddleware } from '../middleware/AuthMiddleware';
import { validationErrorMiddleware, asyncHandler } from '../middleware/ErrorMiddleware';
import { rateLimitMiddleware } from '../middleware/RateLimitMiddleware';

export class AuthRoutes {
  private router: Router;

  constructor(
    private authController: AuthController,
    private authMiddleware: AuthMiddleware
  ) {
    this.router = Router();
    this.initializeRoutes();
  }

  private initializeRoutes(): void {
    // Public routes (no authentication required)
    this.router.post(
      '/register',
      rateLimitMiddleware({ windowMs: 15 * 60 * 1000, max: 5 }), // 5 requests per 15 minutes
      this.validateRegistration(),
      validationErrorMiddleware,
      asyncHandler(this.authController.register)
    );

    this.router.post(
      '/login',
      rateLimitMiddleware({ windowMs: 15 * 60 * 1000, max: 10 }), // 10 requests per 15 minutes
      this.validateLogin(),
      validationErrorMiddleware,
      asyncHandler(this.authController.login)
    );

    this.router.post(
      '/forgot-password',
      rateLimitMiddleware({ windowMs: 60 * 60 * 1000, max: 3 }), // 3 requests per hour
      this.validateForgotPassword(),
      validationErrorMiddleware,
      asyncHandler(this.authController.forgotPassword)
    );

    this.router.post(
      '/reset-password',
      rateLimitMiddleware({ windowMs: 60 * 60 * 1000, max: 5 }), // 5 requests per hour
      this.validateResetPassword(),
      validationErrorMiddleware,
      asyncHandler(this.authController.resetPassword)
    );

    this.router.post(
      '/verify-email',
      rateLimitMiddleware({ windowMs: 60 * 60 * 1000, max: 10 }), // 10 requests per hour
      this.validateEmailVerification(),
      validationErrorMiddleware,
      asyncHandler(this.authController.verifyEmail)
    );

    this.router.post(
      '/refresh',
      rateLimitMiddleware({ windowMs: 15 * 60 * 1000, max: 20 }), // 20 requests per 15 minutes
      asyncHandler(this.authController.refreshToken)
    );

    // Protected routes (authentication required)
    this.router.post(
      '/logout',
      this.authMiddleware.authenticate,
      asyncHandler(this.authController.logout)
    );

    this.router.post(
      '/change-password',
      this.authMiddleware.authenticate,
      rateLimitMiddleware({ windowMs: 60 * 60 * 1000, max: 5 }), // 5 requests per hour
      this.validateChangePassword(),
      validationErrorMiddleware,
      asyncHandler(this.authController.changePassword)
    );

    this.router.get(
      '/me',
      this.authMiddleware.authenticate,
      asyncHandler(this.authController.getCurrentUser)
    );

    // Two-factor authentication routes
    this.router.post(
      '/2fa/enable',
      this.authMiddleware.authenticate,
      rateLimitMiddleware({ windowMs: 60 * 60 * 1000, max: 3 }), // 3 requests per hour
      asyncHandler(this.enable2FA)
    );

    this.router.post(
      '/2fa/disable',
      this.authMiddleware.authenticate,
      this.validateDisable2FA(),
      validationErrorMiddleware,
      asyncHandler(this.disable2FA)
    );

    this.router.post(
      '/2fa/verify',
      this.authMiddleware.authenticate,
      this.validateVerify2FA(),
      validationErrorMiddleware,
      asyncHandler(this.verify2FA)
    );

    // Session management routes
    this.router.get(
      '/sessions',
      this.authMiddleware.authenticate,
      asyncHandler(this.getSessions)
    );

    this.router.delete(
      '/sessions/:sessionId',
      this.authMiddleware.authenticate,
      param('sessionId').isUUID().withMessage('Invalid session ID'),
      validationErrorMiddleware,
      asyncHandler(this.revokeSession)
    );

    this.router.delete(
      '/sessions',
      this.authMiddleware.authenticate,
      asyncHandler(this.revokeAllSessions)
    );
  }

  // Validation middleware methods
  private validateRegistration() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Valid email is required'),
      body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain uppercase, lowercase, number, and special character'),
      body('confirmPassword')
        .custom((value, { req }) => {
          if (value !== req.body.password) {
            throw new Error('Passwords do not match');
          }
          return true;
        }),
      body('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
      body('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
      body('phone')
        .optional()
        .isMobilePhone('any')
        .withMessage('Valid phone number is required'),
      body('acceptTerms')
        .isBoolean()
        .custom((value) => {
          if (!value) {
            throw new Error('You must accept the terms of service');
          }
          return true;
        }),
      body('acceptPrivacy')
        .isBoolean()
        .custom((value) => {
          if (!value) {
            throw new Error('You must accept the privacy policy');
          }
          return true;
        }),
      body('role')
        .optional()
        .isIn(['USER', 'AGENT'])
        .withMessage('Invalid role'),
      body('language')
        .optional()
        .isIn(['en', 'ps', 'fa'])
        .withMessage('Invalid language')
    ];
  }

  private validateLogin() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Valid email is required'),
      body('password')
        .notEmpty()
        .withMessage('Password is required'),
      body('twoFactorCode')
        .optional()
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('Two-factor code must be 6 digits'),
      body('rememberMe')
        .optional()
        .isBoolean()
        .withMessage('Remember me must be a boolean'),
      body('deviceId')
        .optional()
        .isUUID()
        .withMessage('Invalid device ID')
    ];
  }

  private validateForgotPassword() {
    return [
      body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Valid email is required')
    ];
  }

  private validateResetPassword() {
    return [
      body('token')
        .notEmpty()
        .withMessage('Reset token is required'),
      body('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('Password must contain uppercase, lowercase, number, and special character'),
      body('confirmPassword')
        .custom((value, { req }) => {
          if (value !== req.body.password) {
            throw new Error('Passwords do not match');
          }
          return true;
        })
    ];
  }

  private validateEmailVerification() {
    return [
      body('token')
        .notEmpty()
        .withMessage('Verification token is required')
    ];
  }

  private validateChangePassword() {
    return [
      body('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
      body('newPassword')
        .isLength({ min: 8 })
        .withMessage('New password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
        .withMessage('New password must contain uppercase, lowercase, number, and special character'),
      body('confirmPassword')
        .custom((value, { req }) => {
          if (value !== req.body.newPassword) {
            throw new Error('Passwords do not match');
          }
          return true;
        })
    ];
  }

  private validateDisable2FA() {
    return [
      body('password')
        .notEmpty()
        .withMessage('Password is required to disable 2FA')
    ];
  }

  private validateVerify2FA() {
    return [
      body('code')
        .isLength({ min: 6, max: 6 })
        .isNumeric()
        .withMessage('Two-factor code must be 6 digits')
    ];
  }

  // Route handler methods (these would be implemented in the controller)
  private enable2FA = async (req: any, res: any, next: any) => {
    // Implementation would be in the controller
    res.status(501).json({ message: 'Not implemented yet' });
  };

  private disable2FA = async (req: any, res: any, next: any) => {
    // Implementation would be in the controller
    res.status(501).json({ message: 'Not implemented yet' });
  };

  private verify2FA = async (req: any, res: any, next: any) => {
    // Implementation would be in the controller
    res.status(501).json({ message: 'Not implemented yet' });
  };

  private getSessions = async (req: any, res: any, next: any) => {
    // Implementation would be in the controller
    res.status(501).json({ message: 'Not implemented yet' });
  };

  private revokeSession = async (req: any, res: any, next: any) => {
    // Implementation would be in the controller
    res.status(501).json({ message: 'Not implemented yet' });
  };

  private revokeAllSessions = async (req: any, res: any, next: any) => {
    // Implementation would be in the controller
    res.status(501).json({ message: 'Not implemented yet' });
  };

  public getRouter(): Router {
    return this.router;
  }
}
