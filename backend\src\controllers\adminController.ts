import { Request, Response, NextFunction } from 'express';
import { User, Property, Message, Review, Favorite } from '../models';
import { AppError, catchAsync } from '../middleware/errorHandler';

export const getDashboardStats = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const [
    totalUsers,
    totalProperties,
    totalMessages,
    totalReviews,
    activeUsers,
    approvedProperties,
    pendingProperties,
    recentUsers,
    recentProperties
  ] = await Promise.all([
    User.countDocuments(),
    Property.countDocuments(),
    Message.countDocuments(),
    Review.countDocuments(),
    User.countDocuments({ isActive: true }),
    Property.countDocuments({ status: 'APPROVED' }),
    Property.countDocuments({ status: 'PENDING' }),
    User.find().sort({ createdAt: -1 }).limit(5).select('name email createdAt role'),
    Property.find().sort({ createdAt: -1 }).limit(5).populate('owner', 'name email')
  ]);

  // Get properties by category
  const propertiesByCategory = await Property.aggregate([
    { $group: { _id: '$category', count: { $sum: 1 } } }
  ]);

  // Get properties by type
  const propertiesByType = await Property.aggregate([
    { $group: { _id: '$type', count: { $sum: 1 } } }
  ]);

  // Get properties by status
  const propertiesByStatus = await Property.aggregate([
    { $group: { _id: '$status', count: { $sum: 1 } } }
  ]);

  // Get users by role
  const usersByRole = await User.aggregate([
    { $group: { _id: '$role', count: { $sum: 1 } } }
  ]);

  const stats = {
    overview: {
      totalUsers,
      totalProperties,
      totalMessages,
      totalReviews,
      activeUsers,
      approvedProperties,
      pendingProperties
    },
    charts: {
      propertiesByCategory: propertiesByCategory.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      propertiesByType: propertiesByType.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      propertiesByStatus: propertiesByStatus.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {}),
      usersByRole: usersByRole.reduce((acc, item) => {
        acc[item._id] = item.count;
        return acc;
      }, {})
    },
    recent: {
      users: recentUsers,
      properties: recentProperties
    }
  };

  res.json({
    success: true,
    data: stats
  });
});

export const getAllUsers = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { 
    page = 1, 
    limit = 20, 
    role, 
    isActive, 
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query as any;

  const filter: any = {};
  if (role) filter.role = role;
  if (isActive !== undefined) filter.isActive = isActive === 'true';
  if (search) {
    filter.$or = [
      { name: new RegExp(search, 'i') },
      { email: new RegExp(search, 'i') }
    ];
  }

  const skip = (Number(page) - 1) * Number(limit);
  const sortObj: any = {};
  sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;

  const [users, total] = await Promise.all([
    User.find(filter)
      .select('-password')
      .sort(sortObj)
      .skip(skip)
      .limit(Number(limit)),
    User.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(total / Number(limit));

  res.json({
    success: true,
    data: users,
    meta: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    }
  });
});

export const getAllProperties = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { 
    page = 1, 
    limit = 20, 
    status, 
    category,
    type,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = req.query as any;

  const filter: any = {};
  if (status) filter.status = status;
  if (category) filter.category = category;
  if (type) filter.type = type;
  if (search) {
    filter.$or = [
      { title: new RegExp(search, 'i') },
      { description: new RegExp(search, 'i') },
      { address: new RegExp(search, 'i') }
    ];
  }

  const skip = (Number(page) - 1) * Number(limit);
  const sortObj: any = {};
  sortObj[sortBy] = sortOrder === 'asc' ? 1 : -1;

  const [properties, total] = await Promise.all([
    Property.find(filter)
      .populate('owner', 'name email phone')
      .sort(sortObj)
      .skip(skip)
      .limit(Number(limit)),
    Property.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(total / Number(limit));

  res.json({
    success: true,
    data: properties,
    meta: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    }
  });
});

export const approveProperty = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const property = await Property.findByIdAndUpdate(
    id,
    { 
      status: 'APPROVED',
      publishedAt: new Date()
    },
    { new: true }
  ).populate('owner', 'name email');

  if (!property) {
    return next(new AppError('Property not found', 404));
  }

  res.json({
    success: true,
    data: property,
    message: 'Property approved successfully'
  });
});

export const rejectProperty = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const { reason } = req.body;

  const property = await Property.findByIdAndUpdate(
    id,
    { status: 'REJECTED' },
    { new: true }
  ).populate('owner', 'name email');

  if (!property) {
    return next(new AppError('Property not found', 404));
  }

  // In a real application, you would send an email to the property owner
  // with the rejection reason

  res.json({
    success: true,
    data: property,
    message: 'Property rejected successfully'
  });
});

export const deleteProperty = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;

  const property = await Property.findById(id);
  if (!property) {
    return next(new AppError('Property not found', 404));
  }

  // Delete related data
  await Promise.all([
    Property.findByIdAndDelete(id),
    Favorite.deleteMany({ property: id }),
    Review.deleteMany({ property: id }),
    Message.deleteMany({ property: id })
  ]);

  res.json({
    success: true,
    message: 'Property and related data deleted successfully'
  });
});

export const updateUserRole = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const { role } = req.body;

  if (!['USER', 'AGENT', 'ADMIN'].includes(role)) {
    return next(new AppError('Invalid role', 400));
  }

  const user = await User.findByIdAndUpdate(
    id,
    { role },
    { new: true }
  ).select('-password');

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.json({
    success: true,
    data: user,
    message: 'User role updated successfully'
  });
});

export const getSystemStats = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { period = '30d' } = req.query;

  let dateFilter: Date;
  switch (period) {
    case '7d':
      dateFilter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      dateFilter = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
      break;
    default:
      dateFilter = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
  }

  const [
    newUsers,
    newProperties,
    newMessages,
    userGrowth,
    propertyGrowth
  ] = await Promise.all([
    User.countDocuments({ createdAt: { $gte: dateFilter } }),
    Property.countDocuments({ createdAt: { $gte: dateFilter } }),
    Message.countDocuments({ createdAt: { $gte: dateFilter } }),
    User.aggregate([
      { $match: { createdAt: { $gte: dateFilter } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]),
    Property.aggregate([
      { $match: { createdAt: { $gte: dateFilter } } },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ])
  ]);

  res.json({
    success: true,
    data: {
      period,
      summary: {
        newUsers,
        newProperties,
        newMessages
      },
      charts: {
        userGrowth,
        propertyGrowth
      }
    }
  });
});
