import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { useResponsive } from '../../hooks/useResponsive';
import NotificationBadge from '../common/NotificationBadge';
import LanguageSelector from '../common/LanguageSelector';
import {
  HomeIcon,
  BuildingOfficeIcon,
  PlusIcon,
  PhoneIcon,
  InformationCircleIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  GlobeAltIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const { user, logout, isAuthenticated } = useAuth();
  const { t, isRTL } = useLanguage();
  const { isMobile, isTablet, isTouchDevice } = useResponsive();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  // Close mobile menu on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen && !isMobile) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isMenuOpen, isMobile]);

  const navigation = [
    { name: 'Home', href: '/', icon: HomeIcon },
    { name: 'Properties', href: '/listings', icon: BuildingOfficeIcon },
    { name: 'Post Property', href: '/post-property', icon: PlusIcon },
    { name: 'About Us', href: '/about', icon: InformationCircleIcon },
    { name: 'Contact Us', href: '/contact', icon: PhoneIcon },
  ];

  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'ps', name: 'پښتو', flag: '🇦🇫' },
    { code: 'fa', name: 'فارسی', flag: '🇦🇫' },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <header className={`navbar-glass shadow-soft sticky top-0 z-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2.5 group">
            <div className="w-9 h-9 bg-gradient-primary rounded-lg flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300">
              <HomeIcon className="w-5 h-5 text-white" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-lg font-bold text-gradient">RealEstate</h1>
              <p className="text-xs text-gray-500">Afghanistan</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-6">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`nav-item nav-link flex items-center space-x-1.5 px-2.5 py-1.5 rounded-md text-sm transition-all duration-300 ${
                    isActive(item.href)
                      ? 'text-primary-600 bg-gradient-to-r from-primary-50 to-secondary-50 shadow-sm'
                      : 'text-gray-600 hover:text-primary-600'
                  }`}
                >
                  <Icon className="w-4 h-4 icon-bounce" />
                  <span className="font-medium">{item.name}</span>
                </Link>
              );
            })}
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Language Selector & Auth Buttons */}
            <div className="hidden sm:flex items-center space-x-3">
              <LanguageSelector variant="compact" />
              {isAuthenticated && user ? (
                <div className="flex items-center space-x-4">
                  {/* Favorites Link */}
                  <Link
                    to="/favorites"
                    className="nav-item relative p-1.5 text-gray-500 hover:text-red-500 rounded-md transition-all duration-300 group"
                  >
                    <HeartIcon className="w-5 h-5 icon-bounce group-hover:text-red-500" />
                    <div className="notification-pulse">
                      <NotificationBadge count={5} />
                    </div>
                  </Link>

                  {/* Messages Link with Notification */}
                  <Link
                    to="/messages"
                    className="nav-item relative p-1.5 text-gray-500 hover:text-primary-600 rounded-md transition-all duration-300 group"
                  >
                    <ChatBubbleLeftRightIcon className="w-5 h-5 icon-bounce" />
                    <div className="notification-pulse">
                      <NotificationBadge count={3} />
                    </div>
                  </Link>

                  <Link
                    to="/dashboard"
                    className="nav-item flex items-center space-x-2 px-2 py-1.5 text-gray-700 hover:text-primary-600 rounded-md transition-all duration-300 group"
                  >
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-7 h-7 rounded-full object-cover ring-2 ring-transparent group-hover:ring-primary-200 transition-all duration-300"
                    />
                    <span className="text-sm font-medium">{user.name.split(' ')[0]}</span>
                  </Link>
                  <button
                    onClick={logout}
                    className="nav-item px-2 py-1.5 text-sm text-gray-500 hover:text-red-600 rounded-md transition-all duration-300 transform hover:scale-105"
                  >
                    Logout
                  </button>
                </div>
              ) : (
                <>
                  <Link
                    to="/login"
                    className="nav-item flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-primary-600 rounded-md transition-all duration-300"
                  >
                    <UserIcon className="w-4 h-4 icon-bounce" />
                    <span>{t('nav.login')}</span>
                  </Link>
                  <Link
                    to="/register"
                    className="btn-shimmer btn-glow flex items-center space-x-1.5 px-3 py-1.5 text-sm font-medium text-white bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 rounded-md transition-all duration-300 shadow-sm hover:shadow-md transform hover:scale-105"
                  >
                    <span>{t('nav.register')}</span>
                  </Link>
                </>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="lg:hidden p-1.5 rounded-md text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-5 h-5" />
              ) : (
                <Bars3Icon className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t border-gray-100 animate-slide-down">
            <div className="space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`flex items-center space-x-2.5 px-3 py-2.5 rounded-md transition-all duration-300 ${
                      isActive(item.href)
                        ? 'text-primary-600 bg-primary-50 shadow-sm'
                        : 'text-gray-600 hover:text-primary-600 hover:bg-primary-50'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="font-medium text-sm">{item.name}</span>
                  </Link>
                );
              })}
              
              {/* Mobile Auth Buttons */}
              <div className="pt-3 border-t border-gray-100 space-y-2">
                <Link
                  to="/login"
                  className="flex items-center space-x-2.5 px-3 py-2.5 rounded-md text-gray-600 hover:text-primary-600 hover:bg-primary-50 transition-all duration-300"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <UserIcon className="w-4 h-4" />
                  <span className="font-medium text-sm">Login</span>
                </Link>
                <Link
                  to="/register"
                  className="flex items-center justify-center space-x-2 mx-3 py-2.5 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-all duration-300 shadow-sm"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="font-medium text-sm">Register</span>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
