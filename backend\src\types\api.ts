import { IUser, IProperty, IPropertyImage, IMessage, IReview, IFavorite } from '../models';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    code?: string;
    details?: any;
  };
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// User types
export interface UserCreateInput {
  email: string;
  password: string;
  name: string;
  phone?: string;
  role?: 'USER' | 'AGENT' | 'ADMIN';
}

export interface UserUpdateInput {
  name?: string;
  phone?: string;
  bio?: string;
  location?: string;
  website?: string;
  avatar?: string;
}

export interface UserLoginInput {
  email: string;
  password: string;
}

export interface UserResponse extends Omit<IUser, 'password' | 'comparePassword' | 'generateAuthToken'> {}

// Property types
export interface PropertyCreateInput {
  title: string;
  description: string;
  price: number;
  currency?: string;
  category: 'SALE' | 'RENT' | 'LEASE';
  type: 'RESIDENTIAL' | 'COMMERCIAL' | 'LAND' | 'APARTMENT' | 'HOUSE' | 'VILLA' | 'OFFICE' | 'SHOP' | 'WAREHOUSE' | 'FARM';
  province: string;
  city: string;
  district?: string;
  address: string;
  latitude?: number;
  longitude?: number;
  bedrooms?: number;
  bathrooms?: number;
  area: number;
  yearBuilt?: number;
  furnished?: boolean;
  parking?: boolean;
  garden?: boolean;
  balcony?: boolean;
  features?: string[];
}

export interface PropertyUpdateInput extends Partial<PropertyCreateInput> {}

export interface PropertySearchParams extends PaginationParams {
  category?: 'SALE' | 'RENT' | 'LEASE';
  type?: string;
  province?: string;
  city?: string;
  district?: string;
  minPrice?: number;
  maxPrice?: number;
  minArea?: number;
  maxArea?: number;
  bedrooms?: number;
  bathrooms?: number;
  furnished?: boolean;
  parking?: boolean;
  garden?: boolean;
  balcony?: boolean;
  features?: string[];
  status?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SOLD' | 'RENTED' | 'INACTIVE';
}

export interface PropertyWithDetails extends Omit<IProperty, 'owner'> {
  owner: UserResponse;
  reviews: (Omit<IReview, 'reviewer'> & { reviewer: UserResponse })[];
  _count: {
    favorites: number;
    reviews: number;
  };
  averageRating?: number;
  isFavorited?: boolean;
}

// Message types
export interface MessageCreateInput {
  receiverId: string;
  propertyId?: string;
  subject?: string;
  content: string;
}

export interface MessageResponse extends Omit<IMessage, 'sender' | 'receiver' | 'property'> {
  sender: UserResponse;
  receiver: UserResponse;
  property?: IProperty;
}

// Review types
export interface ReviewCreateInput {
  propertyId: string;
  rating: number;
  comment?: string;
}

export interface ReviewResponse extends Omit<IReview, 'reviewer' | 'property'> {
  reviewer: UserResponse;
  property: IProperty;
}

// File upload types
export interface FileUploadResponse {
  filename: string;
  originalName: string;
  url: string;
  size: number;
  mimeType: string;
}

// Authentication types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface AuthResponse {
  user: UserResponse;
  tokens: AuthTokens;
}

// Error types
export interface ValidationError {
  field: string;
  message: string;
}

export interface ApiError extends Error {
  statusCode: number;
  code?: string;
  details?: ValidationError[];
}

// Dashboard/Analytics types
export interface DashboardStats {
  totalUsers: number;
  totalProperties: number;
  totalMessages: number;
  totalReviews: number;
  propertiesByCategory: Record<string, number>;
  propertiesByType: Record<string, number>;
  propertiesByStatus: Record<string, number>;
  recentProperties: PropertyWithDetails[];
  recentUsers: UserResponse[];
}

// Search and filter types
export interface LocationFilter {
  province?: string;
  city?: string;
  district?: string;
}

export interface PriceFilter {
  min?: number;
  max?: number;
}

export interface AreaFilter {
  min?: number;
  max?: number;
}

export interface PropertyFilters {
  location?: LocationFilter;
  price?: PriceFilter;
  area?: AreaFilter;
  category?: string[];
  type?: string[];
  bedrooms?: number[];
  bathrooms?: number[];
  features?: string[];
  amenities?: {
    furnished?: boolean;
    parking?: boolean;
    garden?: boolean;
    balcony?: boolean;
  };
}
