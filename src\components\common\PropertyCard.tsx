import React from 'react';
import { Link } from 'react-router-dom';
import { 
  MapPinIcon,
  HomeIcon,
  BuildingOfficeIcon,
  HeartIcon,
  ShareIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

interface Property {
  id: number;
  title: string;
  location: string;
  price: string;
  image: string;
  beds: number;
  baths: number;
  area: string;
  type: string;
  category: string;
  featured: boolean;
  views: number;
  saved: boolean;
}

interface PropertyCardProps {
  property: Property;
  viewMode?: 'grid' | 'list';
  onToggleSaved?: (propertyId: number) => void;
  animationDelay?: number;
}

const PropertyCard: React.FC<PropertyCardProps> = ({ 
  property, 
  viewMode = 'grid', 
  onToggleSaved,
  animationDelay = 0 
}) => {
  const handleToggleSaved = () => {
    if (onToggleSaved) {
      onToggleSaved(property.id);
    }
  };

  return (
    <div 
      className={`card card-hover animate-slide-up ${
        viewMode === 'list' ? 'flex flex-col sm:flex-row' : ''
      }`}
      style={{ animationDelay: `${animationDelay}ms` }}
    >
      <div className={`relative overflow-hidden ${
        viewMode === 'list' 
          ? 'sm:w-80 sm:flex-shrink-0 rounded-l-xl' 
          : 'rounded-t-xl'
      }`}>
        <img 
          src={property.image} 
          alt={property.title}
          className={`object-cover transform group-hover:scale-110 transition-transform duration-500 ${
            viewMode === 'list' ? 'w-full h-48 sm:h-full' : 'w-full h-64'
          }`}
        />
        
        {/* Overlay Badges */}
        <div className="absolute top-4 left-4 flex flex-col space-y-2">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
            {property.category}
          </span>
          {property.featured && (
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-accent-100 text-accent-800">
              Featured
            </span>
          )}
        </div>

        {/* Action Buttons */}
        <div className="absolute top-4 right-4 flex flex-col space-y-2">
          <button
            onClick={handleToggleSaved}
            className="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-all duration-300 transform hover:scale-110"
          >
            {property.saved ? (
              <HeartSolidIcon className="w-5 h-5 text-red-500" />
            ) : (
              <HeartIcon className="w-5 h-5 text-gray-600" />
            )}
          </button>
          <button className="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-all duration-300 transform hover:scale-110">
            <ShareIcon className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Price Badge */}
        <div className="absolute bottom-4 right-4">
          <div className="text-xl font-bold text-white bg-black/50 px-3 py-1 rounded-lg backdrop-blur-sm">
            {property.price}
          </div>
        </div>
      </div>
      
      <div className={`p-6 ${viewMode === 'list' ? 'flex-1' : ''}`}>
        <div className="flex items-start justify-between mb-3">
          <h3 className="text-xl font-semibold text-gray-900 line-clamp-2 flex-1">
            {property.title}
          </h3>
          <span className="ml-2 text-sm text-gray-500 flex items-center">
            <EyeIcon className="w-4 h-4 mr-1" />
            {property.views}
          </span>
        </div>
        
        <div className="flex items-center text-gray-600 mb-4">
          <MapPinIcon className="w-4 h-4 mr-1 flex-shrink-0" />
          <span className="text-sm">{property.location}</span>
        </div>
        
        <div className="flex items-center justify-between text-sm text-gray-600 mb-6">
          {property.beds > 0 && (
            <div className="flex items-center">
              <HomeIcon className="w-4 h-4 mr-1" />
              <span className="font-medium">{property.beds}</span>
              <span className="ml-1">Beds</span>
            </div>
          )}
          <div className="flex items-center">
            <BuildingOfficeIcon className="w-4 h-4 mr-1" />
            <span className="font-medium">{property.baths}</span>
            <span className="ml-1">Baths</span>
          </div>
          <div className="flex items-center">
            <span className="font-medium">{property.area}</span>
          </div>
        </div>
        
        <Link 
          to={`/property/${property.id}`}
          className="btn-outline w-full text-center block"
        >
          View Details
        </Link>
      </div>
    </div>
  );
};

export default PropertyCard;
