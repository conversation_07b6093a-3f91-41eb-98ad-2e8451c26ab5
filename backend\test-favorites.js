const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testFavorites() {
  console.log('🧪 Testing Favorites Route...\n');

  try {
    console.log('Testing /users/favorites...');
    const response = await axios.get(`${BASE_URL}/users/favorites`);
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Error Status:', error.response?.status);
    console.log('❌ Error Message:', error.response?.data?.error?.message || error.message);
    console.log('❌ Full Error:', JSON.stringify(error.response?.data, null, 2));
  }
}

// Run the test
testFavorites();
