/**
 * Class Name Utility
 * Utility function for merging Tailwind CSS classes with proper conflict resolution
 */

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Combines class names with proper Tailwind CSS conflict resolution
 * @param inputs - Class names to merge
 * @returns Merged class names string
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

/**
 * Conditional class name utility
 * @param condition - Boolean condition
 * @param trueClasses - Classes to apply when condition is true
 * @param falseClasses - Classes to apply when condition is false
 * @returns Conditional class names
 */
export function conditionalClass(
  condition: boolean,
  trueClasses: string,
  falseClasses?: string
): string {
  return condition ? trueClasses : falseClasses || '';
}

/**
 * Responsive class name utility
 * @param base - Base classes
 * @param responsive - Responsive classes object
 * @returns Responsive class names
 */
export function responsiveClass(
  base: string,
  responsive: {
    sm?: string;
    md?: string;
    lg?: string;
    xl?: string;
    '2xl'?: string;
  }
): string {
  const classes = [base];
  
  if (responsive.sm) classes.push(`sm:${responsive.sm}`);
  if (responsive.md) classes.push(`md:${responsive.md}`);
  if (responsive.lg) classes.push(`lg:${responsive.lg}`);
  if (responsive.xl) classes.push(`xl:${responsive.xl}`);
  if (responsive['2xl']) classes.push(`2xl:${responsive['2xl']}`);
  
  return classes.join(' ');
}

/**
 * Focus visible utility for better accessibility
 * @param classes - Additional classes to apply on focus
 * @returns Focus visible classes
 */
export function focusVisible(classes?: string): string {
  return cn(
    'focus:outline-none focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500',
    classes
  );
}

/**
 * Hover and focus state utility
 * @param baseClasses - Base classes
 * @param hoverClasses - Hover state classes
 * @param focusClasses - Focus state classes
 * @returns Combined state classes
 */
export function interactiveClass(
  baseClasses: string,
  hoverClasses?: string,
  focusClasses?: string
): string {
  return cn(
    baseClasses,
    hoverClasses && `hover:${hoverClasses}`,
    focusClasses && `focus:${focusClasses}`
  );
}

/**
 * Dark mode utility
 * @param lightClasses - Light mode classes
 * @param darkClasses - Dark mode classes
 * @returns Dark mode classes
 */
export function darkMode(lightClasses: string, darkClasses: string): string {
  return cn(lightClasses, `dark:${darkClasses}`);
}

/**
 * Animation utility
 * @param animation - Animation name
 * @param duration - Animation duration
 * @param delay - Animation delay
 * @param easing - Animation easing
 * @returns Animation classes
 */
export function animationClass(
  animation: string,
  duration?: string,
  delay?: string,
  easing?: string
): string {
  const classes = [`animate-${animation}`];
  
  if (duration) classes.push(`duration-${duration}`);
  if (delay) classes.push(`delay-${delay}`);
  if (easing) classes.push(`ease-${easing}`);
  
  return classes.join(' ');
}

/**
 * Grid utility
 * @param cols - Number of columns
 * @param gap - Grid gap
 * @param responsive - Responsive grid settings
 * @returns Grid classes
 */
export function gridClass(
  cols: number,
  gap?: string,
  responsive?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  }
): string {
  const classes = [`grid-cols-${cols}`];
  
  if (gap) classes.push(`gap-${gap}`);
  if (responsive?.sm) classes.push(`sm:grid-cols-${responsive.sm}`);
  if (responsive?.md) classes.push(`md:grid-cols-${responsive.md}`);
  if (responsive?.lg) classes.push(`lg:grid-cols-${responsive.lg}`);
  if (responsive?.xl) classes.push(`xl:grid-cols-${responsive.xl}`);
  
  return cn('grid', classes.join(' '));
}

/**
 * Flex utility
 * @param direction - Flex direction
 * @param justify - Justify content
 * @param align - Align items
 * @param wrap - Flex wrap
 * @param gap - Gap between items
 * @returns Flex classes
 */
export function flexClass(
  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse',
  justify?: 'start' | 'end' | 'center' | 'between' | 'around' | 'evenly',
  align?: 'start' | 'end' | 'center' | 'baseline' | 'stretch',
  wrap?: 'wrap' | 'nowrap' | 'wrap-reverse',
  gap?: string
): string {
  const classes = ['flex'];
  
  if (direction) classes.push(`flex-${direction}`);
  if (justify) classes.push(`justify-${justify}`);
  if (align) classes.push(`items-${align}`);
  if (wrap) classes.push(`flex-${wrap}`);
  if (gap) classes.push(`gap-${gap}`);
  
  return classes.join(' ');
}

/**
 * Position utility
 * @param position - Position type
 * @param top - Top position
 * @param right - Right position
 * @param bottom - Bottom position
 * @param left - Left position
 * @param zIndex - Z-index
 * @returns Position classes
 */
export function positionClass(
  position: 'static' | 'fixed' | 'absolute' | 'relative' | 'sticky',
  top?: string,
  right?: string,
  bottom?: string,
  left?: string,
  zIndex?: string
): string {
  const classes = [position];
  
  if (top) classes.push(`top-${top}`);
  if (right) classes.push(`right-${right}`);
  if (bottom) classes.push(`bottom-${bottom}`);
  if (left) classes.push(`left-${left}`);
  if (zIndex) classes.push(`z-${zIndex}`);
  
  return classes.join(' ');
}

/**
 * Shadow utility with hover effects
 * @param shadow - Shadow size
 * @param hoverShadow - Hover shadow size
 * @returns Shadow classes
 */
export function shadowClass(shadow: string, hoverShadow?: string): string {
  const classes = [`shadow-${shadow}`];
  
  if (hoverShadow) classes.push(`hover:shadow-${hoverShadow}`);
  
  return classes.join(' ');
}

/**
 * Border utility
 * @param width - Border width
 * @param color - Border color
 * @param radius - Border radius
 * @param style - Border style
 * @returns Border classes
 */
export function borderClass(
  width?: string,
  color?: string,
  radius?: string,
  style?: 'solid' | 'dashed' | 'dotted' | 'double' | 'none'
): string {
  const classes = [];
  
  if (width) classes.push(`border-${width}`);
  if (color) classes.push(`border-${color}`);
  if (radius) classes.push(`rounded-${radius}`);
  if (style && style !== 'solid') classes.push(`border-${style}`);
  
  return classes.join(' ');
}

/**
 * Text utility
 * @param size - Text size
 * @param weight - Font weight
 * @param color - Text color
 * @param align - Text alignment
 * @param transform - Text transform
 * @returns Text classes
 */
export function textClass(
  size?: string,
  weight?: string,
  color?: string,
  align?: 'left' | 'center' | 'right' | 'justify',
  transform?: 'uppercase' | 'lowercase' | 'capitalize' | 'normal-case'
): string {
  const classes = [];
  
  if (size) classes.push(`text-${size}`);
  if (weight) classes.push(`font-${weight}`);
  if (color) classes.push(`text-${color}`);
  if (align) classes.push(`text-${align}`);
  if (transform) classes.push(transform);
  
  return classes.join(' ');
}

/**
 * Spacing utility
 * @param margin - Margin values
 * @param padding - Padding values
 * @returns Spacing classes
 */
export function spacingClass(
  margin?: {
    all?: string;
    x?: string;
    y?: string;
    t?: string;
    r?: string;
    b?: string;
    l?: string;
  },
  padding?: {
    all?: string;
    x?: string;
    y?: string;
    t?: string;
    r?: string;
    b?: string;
    l?: string;
  }
): string {
  const classes = [];
  
  // Margin classes
  if (margin?.all) classes.push(`m-${margin.all}`);
  if (margin?.x) classes.push(`mx-${margin.x}`);
  if (margin?.y) classes.push(`my-${margin.y}`);
  if (margin?.t) classes.push(`mt-${margin.t}`);
  if (margin?.r) classes.push(`mr-${margin.r}`);
  if (margin?.b) classes.push(`mb-${margin.b}`);
  if (margin?.l) classes.push(`ml-${margin.l}`);
  
  // Padding classes
  if (padding?.all) classes.push(`p-${padding.all}`);
  if (padding?.x) classes.push(`px-${padding.x}`);
  if (padding?.y) classes.push(`py-${padding.y}`);
  if (padding?.t) classes.push(`pt-${padding.t}`);
  if (padding?.r) classes.push(`pr-${padding.r}`);
  if (padding?.b) classes.push(`pb-${padding.b}`);
  if (padding?.l) classes.push(`pl-${padding.l}`);
  
  return classes.join(' ');
}
