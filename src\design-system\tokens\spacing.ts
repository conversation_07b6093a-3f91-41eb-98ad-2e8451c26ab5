/**
 * Design System - Spacing and Layout Tokens
 * Consistent spacing system for the Real Estate Platform
 */

export const spacing = {
  // Base spacing scale (rem units)
  0: '0',
  px: '1px',
  0.5: '0.125rem',  // 2px
  1: '0.25rem',     // 4px
  1.5: '0.375rem',  // 6px
  2: '0.5rem',      // 8px
  2.5: '0.625rem',  // 10px
  3: '0.75rem',     // 12px
  3.5: '0.875rem',  // 14px
  4: '1rem',        // 16px
  5: '1.25rem',     // 20px
  6: '1.5rem',      // 24px
  7: '1.75rem',     // 28px
  8: '2rem',        // 32px
  9: '2.25rem',     // 36px
  10: '2.5rem',     // 40px
  11: '2.75rem',    // 44px
  12: '3rem',       // 48px
  14: '3.5rem',     // 56px
  16: '4rem',       // 64px
  20: '5rem',       // 80px
  24: '6rem',       // 96px
  28: '7rem',       // 112px
  32: '8rem',       // 128px
  36: '9rem',       // 144px
  40: '10rem',      // 160px
  44: '11rem',      // 176px
  48: '12rem',      // 192px
  52: '13rem',      // 208px
  56: '14rem',      // 224px
  60: '15rem',      // 240px
  64: '16rem',      // 256px
  72: '18rem',      // 288px
  80: '20rem',      // 320px
  96: '24rem'       // 384px
} as const;

// Semantic spacing tokens
export const semanticSpacing = {
  // Component spacing
  component: {
    xs: spacing[1],      // 4px
    sm: spacing[2],      // 8px
    md: spacing[4],      // 16px
    lg: spacing[6],      // 24px
    xl: spacing[8],      // 32px
    '2xl': spacing[12],  // 48px
    '3xl': spacing[16],  // 64px
    '4xl': spacing[24]   // 96px
  },

  // Layout spacing
  layout: {
    xs: spacing[4],      // 16px
    sm: spacing[6],      // 24px
    md: spacing[8],      // 32px
    lg: spacing[12],     // 48px
    xl: spacing[16],     // 64px
    '2xl': spacing[24],  // 96px
    '3xl': spacing[32],  // 128px
    '4xl': spacing[40],  // 160px
    '5xl': spacing[48],  // 192px
    '6xl': spacing[64]   // 256px
  },

  // Container spacing
  container: {
    xs: spacing[4],      // 16px
    sm: spacing[6],      // 24px
    md: spacing[8],      // 32px
    lg: spacing[12],     // 48px
    xl: spacing[16],     // 64px
    '2xl': spacing[20]   // 80px
  },

  // Section spacing
  section: {
    xs: spacing[8],      // 32px
    sm: spacing[12],     // 48px
    md: spacing[16],     // 64px
    lg: spacing[24],     // 96px
    xl: spacing[32],     // 128px
    '2xl': spacing[40],  // 160px
    '3xl': spacing[48]   // 192px
  }
} as const;

// Grid system
export const grid = {
  // Grid columns
  columns: {
    1: '1',
    2: '2',
    3: '3',
    4: '4',
    5: '5',
    6: '6',
    7: '7',
    8: '8',
    9: '9',
    10: '10',
    11: '11',
    12: '12'
  },

  // Grid gaps
  gap: {
    0: '0',
    1: spacing[1],       // 4px
    2: spacing[2],       // 8px
    3: spacing[3],       // 12px
    4: spacing[4],       // 16px
    5: spacing[5],       // 20px
    6: spacing[6],       // 24px
    8: spacing[8],       // 32px
    10: spacing[10],     // 40px
    12: spacing[12],     // 48px
    16: spacing[16],     // 64px
    20: spacing[20],     // 80px
    24: spacing[24]      // 96px
  }
} as const;

// Breakpoints
export const breakpoints = {
  xs: '0px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
} as const;

// Container max widths
export const containers = {
  xs: '100%',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
  '3xl': '1600px',
  '4xl': '1920px'
} as const;

// Z-index scale
export const zIndex = {
  auto: 'auto',
  0: '0',
  10: '10',
  20: '20',
  30: '30',
  40: '40',
  50: '50',
  // Semantic z-index values
  dropdown: '1000',
  sticky: '1020',
  fixed: '1030',
  modalBackdrop: '1040',
  modal: '1050',
  popover: '1060',
  tooltip: '1070',
  toast: '1080',
  loading: '1090'
} as const;

// Border radius
export const borderRadius = {
  none: '0',
  sm: '0.125rem',      // 2px
  base: '0.25rem',     // 4px
  md: '0.375rem',      // 6px
  lg: '0.5rem',        // 8px
  xl: '0.75rem',       // 12px
  '2xl': '1rem',       // 16px
  '3xl': '1.5rem',     // 24px
  full: '9999px'
} as const;

// Border width
export const borderWidth = {
  0: '0',
  1: '1px',
  2: '2px',
  4: '4px',
  8: '8px'
} as const;

// Component-specific spacing
export const componentSpacing = {
  // Button spacing
  button: {
    padding: {
      xs: { x: spacing[2], y: spacing[1] },      // 8px x 4px
      sm: { x: spacing[3], y: spacing[1.5] },    // 12px x 6px
      md: { x: spacing[4], y: spacing[2] },      // 16px x 8px
      lg: { x: spacing[6], y: spacing[3] },      // 24px x 12px
      xl: { x: spacing[8], y: spacing[4] }       // 32px x 16px
    },
    gap: {
      xs: spacing[1],    // 4px
      sm: spacing[1.5],  // 6px
      md: spacing[2],    // 8px
      lg: spacing[2.5],  // 10px
      xl: spacing[3]     // 12px
    }
  },

  // Input spacing
  input: {
    padding: {
      xs: { x: spacing[2], y: spacing[1] },      // 8px x 4px
      sm: { x: spacing[3], y: spacing[1.5] },    // 12px x 6px
      md: { x: spacing[3], y: spacing[2] },      // 12px x 8px
      lg: { x: spacing[4], y: spacing[2.5] },    // 16px x 10px
      xl: { x: spacing[4], y: spacing[3] }       // 16px x 12px
    }
  },

  // Card spacing
  card: {
    padding: {
      xs: spacing[3],    // 12px
      sm: spacing[4],    // 16px
      md: spacing[6],    // 24px
      lg: spacing[8],    // 32px
      xl: spacing[10]    // 40px
    },
    gap: {
      xs: spacing[2],    // 8px
      sm: spacing[3],    // 12px
      md: spacing[4],    // 16px
      lg: spacing[6],    // 24px
      xl: spacing[8]     // 32px
    }
  },

  // Modal spacing
  modal: {
    padding: {
      xs: spacing[4],    // 16px
      sm: spacing[6],    // 24px
      md: spacing[8],    // 32px
      lg: spacing[10],   // 40px
      xl: spacing[12]    // 48px
    },
    margin: {
      xs: spacing[4],    // 16px
      sm: spacing[6],    // 24px
      md: spacing[8],    // 32px
      lg: spacing[12],   // 48px
      xl: spacing[16]    // 64px
    }
  },

  // Navigation spacing
  navigation: {
    padding: {
      horizontal: spacing[6],  // 24px
      vertical: spacing[4]     // 16px
    },
    gap: spacing[6]            // 24px
  },

  // Property card spacing
  propertyCard: {
    padding: spacing[4],       // 16px
    gap: spacing[3],           // 12px
    imageHeight: '200px',
    imageRadius: borderRadius.lg
  }
} as const;

// Responsive spacing utilities
export const responsiveSpacing = {
  // Mobile-first responsive spacing
  responsive: {
    xs: {
      container: semanticSpacing.container.xs,
      section: semanticSpacing.section.xs,
      component: semanticSpacing.component.xs
    },
    sm: {
      container: semanticSpacing.container.sm,
      section: semanticSpacing.section.sm,
      component: semanticSpacing.component.sm
    },
    md: {
      container: semanticSpacing.container.md,
      section: semanticSpacing.section.md,
      component: semanticSpacing.component.md
    },
    lg: {
      container: semanticSpacing.container.lg,
      section: semanticSpacing.section.lg,
      component: semanticSpacing.component.lg
    },
    xl: {
      container: semanticSpacing.container.xl,
      section: semanticSpacing.section.xl,
      component: semanticSpacing.component.xl
    }
  }
} as const;

// Utility functions
export const getSpacing = (value: keyof typeof spacing): string => {
  return spacing[value];
};

export const getResponsiveSpacing = (
  property: 'container' | 'section' | 'component',
  breakpoint: keyof typeof responsiveSpacing.responsive
): string => {
  return responsiveSpacing.responsive[breakpoint][property];
};

export const createSpacingClasses = (prefix: string = '') => {
  const classes: Record<string, string> = {};
  
  Object.entries(spacing).forEach(([key, value]) => {
    classes[`${prefix}${key}`] = value;
  });
  
  return classes;
};

// Export types
export type SpacingToken = keyof typeof spacing;
export type SemanticSpacingToken = keyof typeof semanticSpacing;
export type BreakpointToken = keyof typeof breakpoints;
export type ContainerToken = keyof typeof containers;
export type ZIndexToken = keyof typeof zIndex;
export type BorderRadiusToken = keyof typeof borderRadius;
