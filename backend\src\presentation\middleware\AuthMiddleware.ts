/**
 * Professional Authentication Middleware
 * Handles JWT token validation, user authentication, and authorization
 */

import { Request, Response, NextFunction } from 'express';
import { JwtService } from '../../shared/services/JwtService';
import { IUserRepository } from '../../domain/repositories/IUserRepository';
import { UnauthorizedError, ForbiddenError } from '../../shared/errors/ApplicationErrors';
import { UserRole } from '../../domain/entities/User';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        role: UserRole;
        permissions: string[];
      };
    }
  }
}

export class AuthMiddleware {
  constructor(
    private jwtService: JwtService,
    private userRepository: IUserRepository
  ) {}

  /**
   * Authenticate user using JWT token
   */
  authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const token = this.extractToken(req);
      
      if (!token) {
        throw new UnauthorizedError('Authentication token required');
      }

      // Verify token
      const payload = await this.jwtService.verifyAccessToken(token);

      // Check if token is blacklisted
      const isBlacklisted = await this.jwtService.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new UnauthorizedError('Token has been revoked');
      }

      // Get user from database
      const user = await this.userRepository.findById(payload.sub);
      if (!user) {
        throw new UnauthorizedError('User not found');
      }

      // Check if user is active and verified
      if (!user.isActiveAndVerified()) {
        throw new UnauthorizedError('Account is not active or verified');
      }

      // Attach user to request
      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        permissions: payload.scope || []
      };

      next();
    } catch (error) {
      next(error);
    }
  };

  /**
   * Optional authentication - doesn't fail if no token provided
   */
  optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const token = this.extractToken(req);
      
      if (token) {
        const payload = await this.jwtService.verifyAccessToken(token);
        const user = await this.userRepository.findById(payload.sub);
        
        if (user && user.isActiveAndVerified()) {
          req.user = {
            id: user.id,
            email: user.email,
            role: user.role,
            permissions: payload.scope || []
          };
        }
      }

      next();
    } catch (error) {
      // Don't fail on optional auth errors
      next();
    }
  };

  /**
   * Authorize user based on roles
   */
  authorize = (...allowedRoles: UserRole[]) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!req.user) {
        return next(new UnauthorizedError('Authentication required'));
      }

      if (!allowedRoles.includes(req.user.role)) {
        return next(new ForbiddenError('Insufficient permissions'));
      }

      next();
    };
  };

  /**
   * Authorize user based on permissions
   */
  requirePermission = (...requiredPermissions: string[]) => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!req.user) {
        return next(new UnauthorizedError('Authentication required'));
      }

      const hasPermission = requiredPermissions.every(permission => 
        req.user!.permissions.includes(permission) || 
        req.user!.permissions.includes('*')
      );

      if (!hasPermission) {
        return next(new ForbiddenError('Insufficient permissions'));
      }

      next();
    };
  };

  /**
   * Check if user owns the resource
   */
  requireOwnership = (resourceIdParam: string = 'id') => {
    return (req: Request, res: Response, next: NextFunction): void => {
      if (!req.user) {
        return next(new UnauthorizedError('Authentication required'));
      }

      const resourceId = req.params[resourceIdParam];
      const userId = req.user.id;

      // Admin and super admin can access any resource
      if (req.user.role === UserRole.ADMIN || req.user.role === UserRole.SUPER_ADMIN) {
        return next();
      }

      // Check ownership (this might need to be customized based on your domain logic)
      if (resourceId !== userId) {
        return next(new ForbiddenError('Access denied: You can only access your own resources'));
      }

      next();
    };
  };

  /**
   * Rate limiting per user
   */
  userRateLimit = (maxRequests: number, windowMs: number) => {
    const userRequests = new Map<string, { count: number; resetTime: number }>();

    return (req: Request, res: Response, next: NextFunction): void => {
      const userId = req.user?.id || req.ip;
      const now = Date.now();
      const userLimit = userRequests.get(userId);

      if (!userLimit || now > userLimit.resetTime) {
        userRequests.set(userId, { count: 1, resetTime: now + windowMs });
        return next();
      }

      if (userLimit.count >= maxRequests) {
        return next(new ForbiddenError('Rate limit exceeded'));
      }

      userLimit.count++;
      next();
    };
  };

  /**
   * API key authentication (for external integrations)
   */
  authenticateApiKey = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const apiKey = req.headers['x-api-key'] as string;
      
      if (!apiKey) {
        throw new UnauthorizedError('API key required');
      }

      // Verify API key (implement your API key validation logic)
      const isValidApiKey = await this.validateApiKey(apiKey);
      if (!isValidApiKey) {
        throw new UnauthorizedError('Invalid API key');
      }

      // Get user associated with API key
      const user = await this.getUserByApiKey(apiKey);
      if (!user) {
        throw new UnauthorizedError('API key not associated with any user');
      }

      req.user = {
        id: user.id,
        email: user.email,
        role: user.role,
        permissions: ['api:access']
      };

      next();
    } catch (error) {
      next(error);
    }
  };

  /**
   * Two-factor authentication check
   */
  requireTwoFactor = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('Authentication required');
      }

      const user = await this.userRepository.findById(req.user.id);
      if (!user) {
        throw new UnauthorizedError('User not found');
      }

      if (!user.securitySettings.twoFactorEnabled) {
        throw new ForbiddenError('Two-factor authentication required');
      }

      // Check if 2FA was verified in this session
      const twoFactorVerified = req.headers['x-2fa-verified'] === 'true';
      if (!twoFactorVerified) {
        throw new ForbiddenError('Two-factor authentication verification required');
      }

      next();
    } catch (error) {
      next(error);
    }
  };

  /**
   * Extract token from request headers
   */
  private extractToken(req: Request): string | null {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Also check for token in query parameter (for WebSocket connections)
    if (req.query.token && typeof req.query.token === 'string') {
      return req.query.token;
    }

    return null;
  }

  /**
   * Validate API key
   */
  private async validateApiKey(apiKey: string): Promise<boolean> {
    // Implement your API key validation logic
    // This could involve checking against a database, cache, or external service
    return apiKey.startsWith('ak_') && apiKey.length === 32;
  }

  /**
   * Get user by API key
   */
  private async getUserByApiKey(apiKey: string): Promise<any> {
    // Implement logic to get user associated with API key
    // This would typically involve a database lookup
    return null;
  }
}

/**
 * Create middleware instances
 */
export const createAuthMiddleware = (
  jwtService: JwtService,
  userRepository: IUserRepository
) => {
  return new AuthMiddleware(jwtService, userRepository);
};

/**
 * Admin only middleware
 */
export const adminOnly = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    return next(new UnauthorizedError('Authentication required'));
  }

  if (req.user.role !== UserRole.ADMIN && req.user.role !== UserRole.SUPER_ADMIN) {
    return next(new ForbiddenError('Admin access required'));
  }

  next();
};

/**
 * Agent or higher middleware
 */
export const agentOrHigher = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    return next(new UnauthorizedError('Authentication required'));
  }

  const allowedRoles = [UserRole.AGENT, UserRole.ADMIN, UserRole.SUPER_ADMIN];
  if (!allowedRoles.includes(req.user.role)) {
    return next(new ForbiddenError('Agent access or higher required'));
  }

  next();
};
