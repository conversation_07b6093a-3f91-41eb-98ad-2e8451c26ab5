import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  XMarkIcon,
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  EnvelopeIcon,
  PaperAirplaneIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../../contexts/AuthContext';

interface ContactModalProps {
  isOpen: boolean;
  onClose: () => void;
  property: {
    id: number;
    title: string;
    image: string;
    price: string;
  };
  owner: {
    name: string;
    photo: string;
    phone: string;
    email: string;
    verified: boolean;
  };
}

const ContactModal: React.FC<ContactModalProps> = ({ 
  isOpen, 
  onClose, 
  property, 
  owner 
}) => {
  const { user, isAuthenticated } = useAuth();
  const [message, setMessage] = useState(`Hi ${owner.name}, I'm interested in your property "${property.title}". Could you please provide more information?`);
  const [contactMethod, setContactMethod] = useState<'message' | 'phone' | 'email'>('message');
  const [isLoading, setIsLoading] = useState(false);
  const [messageSent, setMessageSent] = useState(false);

  if (!isOpen) return null;

  const handleSendMessage = async () => {
    if (!isAuthenticated) {
      // Redirect to login
      onClose();
      return;
    }

    setIsLoading(true);
    
    // Simulate sending message
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    setMessageSent(true);
    setIsLoading(false);
    
    // Auto close after success
    setTimeout(() => {
      setMessageSent(false);
      onClose();
    }, 2000);
  };

  const handlePhoneCall = () => {
    window.open(`tel:${owner.phone}`, '_self');
  };

  const handleEmail = () => {
    const subject = encodeURIComponent(`Inquiry about ${property.title}`);
    const body = encodeURIComponent(message);
    window.open(`mailto:${owner.email}?subject=${subject}&body=${body}`, '_self');
  };

  if (messageSent) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-white rounded-xl shadow-large max-w-md w-full p-6 animate-scale-in">
          <div className="text-center">
            <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <ChatBubbleLeftRightIcon className="w-8 h-8 text-secondary-500" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Message Sent!</h3>
            <p className="text-gray-600 mb-6">
              Your message has been sent to {owner.name}. They will get back to you soon.
            </p>
            <Link
              to="/messages"
              className="btn-primary w-full"
              onClick={onClose}
            >
              View Messages
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-large max-w-lg w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">Contact Owner</h3>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-300"
            >
              <XMarkIcon className="w-5 h-5 text-gray-500" />
            </button>
          </div>

          {/* Property Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <img
                src={property.image}
                alt={property.title}
                className="w-16 h-16 rounded-lg object-cover"
              />
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-1">{property.title}</h4>
                <p className="text-primary-600 font-bold">{property.price}</p>
              </div>
            </div>
          </div>

          {/* Owner Info */}
          <div className="flex items-center space-x-3 mb-6">
            <img
              src={owner.photo}
              alt={owner.name}
              className="w-12 h-12 rounded-full object-cover"
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h4 className="font-semibold text-gray-900">{owner.name}</h4>
                {owner.verified && (
                  <span className="text-xs bg-secondary-100 text-secondary-800 px-2 py-1 rounded-full">
                    Verified
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600">Property Owner</p>
            </div>
          </div>

          {!isAuthenticated ? (
            /* Login Required */
            <div className="text-center py-8">
              <UserIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                Login Required
              </h4>
              <p className="text-gray-600 mb-6">
                Please log in to contact the property owner
              </p>
              <div className="space-y-3">
                <Link
                  to="/login"
                  className="btn-primary w-full"
                  onClick={onClose}
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className="btn-outline w-full"
                  onClick={onClose}
                >
                  Create Account
                </Link>
              </div>
            </div>
          ) : (
            /* Contact Options */
            <>
              {/* Contact Method Tabs */}
              <div className="flex space-x-1 mb-6 bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setContactMethod('message')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all duration-300 ${
                    contactMethod === 'message'
                      ? 'bg-white text-primary-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <ChatBubbleLeftRightIcon className="w-4 h-4" />
                  <span>Message</span>
                </button>
                <button
                  onClick={() => setContactMethod('phone')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all duration-300 ${
                    contactMethod === 'phone'
                      ? 'bg-white text-primary-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <PhoneIcon className="w-4 h-4" />
                  <span>Call</span>
                </button>
                <button
                  onClick={() => setContactMethod('email')}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm font-medium transition-all duration-300 ${
                    contactMethod === 'email'
                      ? 'bg-white text-primary-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  <EnvelopeIcon className="w-4 h-4" />
                  <span>Email</span>
                </button>
              </div>

              {/* Contact Content */}
              {contactMethod === 'message' && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your Message
                    </label>
                    <textarea
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-4 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                      placeholder="Type your message here..."
                    />
                  </div>
                  <button
                    onClick={handleSendMessage}
                    disabled={!message.trim() || isLoading}
                    className={`w-full flex items-center justify-center space-x-2 py-3 px-4 rounded-lg font-medium transition-all duration-300 ${
                      message.trim() && !isLoading
                        ? 'bg-primary-500 text-white hover:bg-primary-600 transform hover:scale-105'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span>Sending...</span>
                      </>
                    ) : (
                      <>
                        <PaperAirplaneIcon className="w-5 h-5" />
                        <span>Send Message</span>
                      </>
                    )}
                  </button>
                </div>
              )}

              {contactMethod === 'phone' && (
                <div className="text-center py-6">
                  <PhoneIcon className="w-16 h-16 text-primary-500 mx-auto mb-4" />
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Call {owner.name}
                  </h4>
                  <p className="text-2xl font-bold text-primary-600 mb-6">
                    {owner.phone}
                  </p>
                  <button
                    onClick={handlePhoneCall}
                    className="btn-primary w-full"
                  >
                    Call Now
                  </button>
                </div>
              )}

              {contactMethod === 'email' && (
                <div className="text-center py-6">
                  <EnvelopeIcon className="w-16 h-16 text-primary-500 mx-auto mb-4" />
                  <h4 className="text-lg font-semibold text-gray-900 mb-2">
                    Email {owner.name}
                  </h4>
                  <p className="text-lg text-gray-600 mb-6">
                    {owner.email}
                  </p>
                  <button
                    onClick={handleEmail}
                    className="btn-primary w-full"
                  >
                    Send Email
                  </button>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContactModal;
