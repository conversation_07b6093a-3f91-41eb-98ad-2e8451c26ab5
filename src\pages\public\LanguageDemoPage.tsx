import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../../contexts/LanguageContext';
import LanguageSelector from '../../components/common/LanguageSelector';
import { 
  HomeIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  CurrencyDollarIcon,
  MapPinIcon,
  UserIcon,
  BuildingOfficeIcon,
  SparklesIcon,
  CheckCircleIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const LanguageDemoPage: React.FC = () => {
  const { t, isRTL, language } = useLanguage();

  const demoContent = {
    navigation: [
      { key: 'nav.home', icon: HomeIcon },
      { key: 'nav.listings', icon: BuildingOfficeIcon },
      { key: 'nav.favorites', icon: HeartIcon },
      { key: 'nav.messages', icon: ChatBubbleLeftRightIcon },
      { key: 'nav.dashboard', icon: UserIcon }
    ],
    propertyTypes: [
      { key: 'type.house', icon: HomeIcon },
      { key: 'type.apartment', icon: BuildingOfficeIcon },
      { key: 'type.villa', icon: SparklesIcon },
      { key: 'type.commercial', icon: CurrencyDollarIcon }
    ],
    categories: [
      { key: 'category.forSale', icon: CurrencyDollarIcon },
      { key: 'category.forRent', icon: HomeIcon }
    ],
    commonActions: [
      { key: 'common.search', icon: MapPinIcon },
      { key: 'common.save', icon: HeartIcon },
      { key: 'common.share', icon: ChatBubbleLeftRightIcon },
      { key: 'common.viewDetails', icon: CheckCircleIcon }
    ]
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link to="/" className="text-gray-500 hover:text-primary-500 transition-colors duration-300">
                ← {t('common.back')}
              </Link>
              <h1 className="text-xl font-bold text-gray-900">
                Multi-Language Demo
              </h1>
            </div>
            <LanguageSelector />
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Language Info */}
        <div className="bg-white rounded-xl shadow-soft p-6 mb-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Language Support Demo
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div className="text-center">
                <div className="text-4xl mb-2">🇺🇸</div>
                <h3 className="font-semibold text-gray-900">English</h3>
                <p className="text-sm text-gray-600">Left-to-Right (LTR)</p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-2">🇦🇫</div>
                <h3 className="font-semibold text-gray-900">پښتو (Pashto)</h3>
                <p className="text-sm text-gray-600">Right-to-Left (RTL)</p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-2">🇦🇫</div>
                <h3 className="font-semibold text-gray-900">دری (Dari)</h3>
                <p className="text-sm text-gray-600">Right-to-Left (RTL)</p>
              </div>
            </div>
            <div className="bg-primary-50 rounded-lg p-4">
              <p className="text-primary-800">
                <strong>Current Language:</strong> {language.toUpperCase()} | 
                <strong> Direction:</strong> {isRTL ? 'RTL' : 'LTR'}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Navigation Demo */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              Navigation Items
            </h3>
            <div className="space-y-3">
              {demoContent.navigation.map((item, index) => {
                const Icon = item.icon;
                return (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <Icon className="w-5 h-5 text-primary-500" />
                    <span className="font-medium text-gray-900">{t(item.key)}</span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Property Types Demo */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              Property Types
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {demoContent.propertyTypes.map((item, index) => {
                const Icon = item.icon;
                return (
                  <div key={index} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                    <Icon className="w-4 h-4 text-secondary-500" />
                    <span className="text-sm font-medium text-gray-900">{t(item.key)}</span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Categories Demo */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              Categories
            </h3>
            <div className="space-y-3">
              {demoContent.categories.map((item, index) => {
                const Icon = item.icon;
                return (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <Icon className="w-5 h-5 text-accent-500" />
                    <span className="font-medium text-gray-900">{t(item.key)}</span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Common Actions Demo */}
          <div className="bg-white rounded-xl shadow-soft p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              Common Actions
            </h3>
            <div className="grid grid-cols-2 gap-3">
              {demoContent.commonActions.map((item, index) => {
                const Icon = item.icon;
                return (
                  <div key={index} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                    <Icon className="w-4 h-4 text-primary-500" />
                    <span className="text-sm font-medium text-gray-900">{t(item.key)}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Sample Property Card */}
        <div className="bg-white rounded-xl shadow-soft p-6 mt-8">
          <h3 className="text-lg font-bold text-gray-900 mb-4">
            Sample Property Card
          </h3>
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="flex items-start space-x-4">
              <img
                src="https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80"
                alt="Property"
                className="w-24 h-24 rounded-lg object-cover"
              />
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-semibold text-gray-900">
                    {language === 'en' && 'Modern Villa in Wazir Akbar Khan'}
                    {language === 'ps' && 'په وزیر اکبر خان کې عصري ویلا'}
                    {language === 'fa' && 'ویلای مدرن در وزیر اکبر خان'}
                  </h4>
                  <HeartIcon className="w-5 h-5 text-gray-400 hover:text-red-500 cursor-pointer transition-colors duration-300" />
                </div>
                
                <div className="flex items-center text-gray-600 mb-2">
                  <MapPinIcon className="w-4 h-4 mr-1" />
                  <span className="text-sm">
                    {language === 'en' && 'Wazir Akbar Khan, Kabul'}
                    {language === 'ps' && 'وزیر اکبر خان، کابل'}
                    {language === 'fa' && 'وزیر اکبر خان، کابل'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
                  <span>4 {t('common.bedrooms')}</span>
                  <span>3 {t('common.bathrooms')}</span>
                  <span>2,500 {t('units.sqft')}</span>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="text-xl font-bold text-primary-500">$250,000</div>
                  <button className="btn-primary text-sm">
                    {t('common.viewDetails')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* RTL Layout Demo */}
        <div className="bg-white rounded-xl shadow-soft p-6 mt-8">
          <h3 className="text-lg font-bold text-gray-900 mb-4">
            RTL Layout Demo
          </h3>
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Text Alignment</h4>
                <p className="text-gray-700 leading-relaxed">
                  {language === 'en' && 'This text is aligned to the left in English (LTR) and will automatically align to the right in Arabic/Persian (RTL) languages.'}
                  {language === 'ps' && 'دا متن په انګلیسي کې کیڼ اړخ ته سمون لري (LTR) او په عربي/فارسي (RTL) ژبو کې به په اتوماتيک ډول ښي اړخ ته سمون ومومي.'}
                  {language === 'fa' && 'این متن در انگلیسی به سمت چپ تراز شده (LTR) و در زبان‌های عربی/فارسی (RTL) به طور خودکار به سمت راست تراز می‌شود.'}
                </p>
              </div>
              
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">Layout Direction</h4>
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                  <div className="w-8 h-8 bg-secondary-500 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                  <div className="w-8 h-8 bg-accent-500 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                </div>
                <p className="text-sm text-gray-600">
                  {language === 'en' && 'Elements flow from left to right in LTR languages.'}
                  {language === 'ps' && 'عناصر په LTR ژبو کې له کیڼ څخه ښي ته جریان لري.'}
                  {language === 'fa' && 'عناصر در زبان‌های LTR از چپ به راست جریان دارند.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-8">
          <Link to="/" className="btn-primary">
            {t('common.back')} {t('nav.home')}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LanguageDemoPage;
