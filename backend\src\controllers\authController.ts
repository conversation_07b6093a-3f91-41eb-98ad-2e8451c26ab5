import { Request, Response, NextFunction } from 'express';
import { User } from '../models';
import { generateTokens, verifyRefreshToken } from '../utils/jwt';
import { AppError, catchAsync } from '../middleware/errorHandler';
import { UserCreateInput, UserLoginInput, AuthResponse } from '../types/api';

export const register = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { email, password, name, phone, role } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ email });
  if (existingUser) {
    return next(new AppError(
      `An account with email "${email}" already exists. Please use a different email or try logging in.`,
      400,
      'EMAIL_ALREADY_EXISTS'
    ));
  }

  // Check if phone number is already used (if provided)
  if (phone) {
    const existingPhone = await User.findOne({ phone });
    if (existingPhone) {
      return next(new AppError(
        `This phone number "${phone}" is already registered. Please use a different phone number.`,
        400,
        'PHONE_ALREADY_EXISTS'
      ));
    }
  }

  // Create new user
  const user = new User({
    email: email.toLowerCase().trim(),
    password,
    name: name.trim(),
    phone: phone?.trim(),
    role: role || 'USER'
  });

  await user.save();

  // Generate tokens
  const tokens = generateTokens(user);

  // Remove password from response
  const userResponse = user.toJSON();

  res.status(201).json({
    success: true,
    data: {
      user: userResponse,
      tokens
    },
    message: 'User registered successfully'
  });
});

export const login = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { email, password } = req.body;

  // Find user with password
  const user = await User.findOne({ email: email.toLowerCase().trim() }).select('+password');
  if (!user) {
    return next(new AppError(
      'Invalid email or password. Please check your credentials and try again.',
      401,
      'INVALID_CREDENTIALS'
    ));
  }

  // Check if user is active
  if (!user.isActive) {
    return next(new AppError(
      'Your account has been deactivated. Please contact support for assistance.',
      401,
      'ACCOUNT_DEACTIVATED'
    ));
  }

  // Verify password
  const isPasswordValid = await user.comparePassword(password);
  if (!isPasswordValid) {
    return next(new AppError(
      'Invalid email or password. Please check your credentials and try again.',
      401,
      'INVALID_CREDENTIALS'
    ));
  }

  // Update last login
  user.lastLoginAt = new Date();
  await user.save();

  // Generate tokens
  const tokens = generateTokens(user);

  // Remove password from response
  const userResponse = user.toJSON();

  res.json({
    success: true,
    data: {
      user: userResponse,
      tokens
    },
    message: 'Login successful'
  });
});

export const refreshToken = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    return next(new AppError('Refresh token is required', 400));
  }

  try {
    const decoded = verifyRefreshToken(refreshToken);
    
    // Find user
    const user = await User.findById(decoded.userId);
    if (!user || !user.isActive) {
      return next(new AppError('Invalid refresh token', 401));
    }

    // Generate new tokens
    const tokens = generateTokens(user);

    res.json({
      success: true,
      data: { tokens },
      message: 'Tokens refreshed successfully'
    });
  } catch (error) {
    return next(new AppError('Invalid refresh token', 401));
  }
});

export const logout = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  // In a production app, you might want to blacklist the token
  // For now, we'll just return success
  res.json({
    success: true,
    message: 'Logout successful'
  });
});

export const getProfile = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const user = req.user;

  res.json({
    success: true,
    data: { user },
    message: 'Profile retrieved successfully'
  });
});

export const updateProfile = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;
  const { name, phone, bio, location, website, avatar } = req.body;

  const user = await User.findByIdAndUpdate(
    userId,
    {
      name,
      phone,
      bio,
      location,
      website,
      avatar
    },
    { new: true, runValidators: true }
  );

  if (!user) {
    return next(new AppError('User not found', 404));
  }

  res.json({
    success: true,
    data: { user },
    message: 'Profile updated successfully'
  });
});

export const changePassword = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;
  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    return next(new AppError('Current password and new password are required', 400));
  }

  // Find user with password
  const user = await User.findById(userId).select('+password');
  if (!user) {
    return next(new AppError('User not found', 404));
  }

  // Verify current password
  const isCurrentPasswordValid = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    return next(new AppError('Current password is incorrect', 400));
  }

  // Update password
  user.password = newPassword;
  await user.save();

  res.json({
    success: true,
    message: 'Password changed successfully'
  });
});

export const forgotPassword = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { email } = req.body;

  if (!email) {
    return next(new AppError('Email is required', 400));
  }

  const user = await User.findOne({ email });
  if (!user) {
    return next(new AppError('User with this email does not exist', 404));
  }

  // In a real application, you would:
  // 1. Generate a password reset token
  // 2. Save it to the database with expiration
  // 3. Send an email with the reset link
  
  // For now, we'll just return success
  res.json({
    success: true,
    message: 'Password reset instructions sent to your email'
  });
});

export const resetPassword = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { token, newPassword } = req.body;

  if (!token || !newPassword) {
    return next(new AppError('Token and new password are required', 400));
  }

  // In a real application, you would:
  // 1. Verify the reset token
  // 2. Find the user by token
  // 3. Check if token is not expired
  // 4. Update the password
  
  // For now, we'll just return success
  res.json({
    success: true,
    message: 'Password reset successful'
  });
});
