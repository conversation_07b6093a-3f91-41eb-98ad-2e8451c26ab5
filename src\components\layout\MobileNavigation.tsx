import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  HomeIcon,
  MagnifyingGlassIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  UserIcon,
  PlusIcon,
  Bars3Icon,
  XMarkIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline';
import {
  HomeIcon as HomeSolidIcon,
  MagnifyingGlassIcon as SearchSolidIcon,
  HeartIcon as HeartSolidIcon,
  ChatBubbleLeftRightIcon as MessageSolidIcon,
  UserIcon as UserSolidIcon
} from '@heroicons/react/24/solid';
import { useLanguage } from '../../contexts/LanguageContext';
import { useAuth } from '../../contexts/AuthContext';

interface MobileNavItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType<any>;
  activeIcon: React.ComponentType<any>;
  badge?: number;
}

const MobileNavigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t, isRTL } = useLanguage();
  const { user, isAuthenticated } = useAuth();
  const location = useLocation();

  const navItems: MobileNavItem[] = [
    {
      id: 'home',
      label: 'Home',
      path: '/',
      icon: HomeIcon,
      activeIcon: HomeSolidIcon
    },
    {
      id: 'search',
      label: 'Search',
      path: '/listings',
      icon: MagnifyingGlassIcon,
      activeIcon: SearchSolidIcon
    },
    {
      id: 'add',
      label: 'Add',
      path: '/post-property',
      icon: PlusIcon,
      activeIcon: PlusIcon
    },
    {
      id: 'favorites',
      label: 'Favorites',
      path: '/favorites',
      icon: HeartIcon,
      activeIcon: HeartSolidIcon
    },
    {
      id: 'profile',
      label: 'Profile',
      path: isAuthenticated ? '/dashboard' : '/login',
      icon: UserIcon,
      activeIcon: UserSolidIcon
    }
  ];

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  const menuItems = [
    { label: 'All Properties', path: '/listings', icon: BuildingOfficeIcon },
    { label: 'My Listings', path: '/dashboard/listings', icon: BuildingOfficeIcon },
    { label: 'Messages', path: '/messages', icon: ChatBubbleLeftRightIcon, badge: 3 },
    { label: 'Favorites', path: '/favorites', icon: HeartIcon },
    { label: 'About', path: '/about', icon: HomeIcon },
    { label: 'Contact', path: '/contact', icon: ChatBubbleLeftRightIcon }
  ];

  return (
    <>
      {/* Bottom Navigation Bar */}
      <motion.nav
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        className="fixed bottom-0 left-0 right-0 z-50 bg-white border-t border-gray-200 safe-area-bottom lg:hidden"
      >
        <div className="flex items-center justify-around px-2 py-2">
          {navItems.map((item) => {
            const active = isActive(item.path);
            const Icon = active ? item.activeIcon : item.icon;
            
            return (
              <Link
                key={item.id}
                to={item.path}
                className="flex flex-col items-center justify-center p-2 min-w-0 flex-1 relative"
              >
                <motion.div
                  whileTap={{ scale: 0.9 }}
                  className={`relative p-2 rounded-xl transition-colors duration-200 ${
                    active 
                      ? 'bg-primary-100 text-primary-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Icon className="w-6 h-6" />
                  {item.badge && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                    >
                      {item.badge}
                    </motion.span>
                  )}
                </motion.div>
                <span className={`text-xs mt-1 font-medium truncate ${
                  active ? 'text-primary-600' : 'text-gray-500'
                }`}>
                  {item.label}
                </span>
              </Link>
            );
          })}
        </div>
      </motion.nav>

      {/* Mobile Menu Button */}
      <motion.button
        whileTap={{ scale: 0.9 }}
        onClick={() => setIsMenuOpen(true)}
        className="fixed top-4 right-4 z-40 lg:hidden bg-white rounded-full p-3 shadow-lg border border-gray-200"
      >
        <Bars3Icon className="w-6 h-6 text-gray-700" />
      </motion.button>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden"
              onClick={() => setIsMenuOpen(false)}
            />
            
            <motion.div
              initial={{ x: isRTL ? '-100%' : '100%' }}
              animate={{ x: 0 }}
              exit={{ x: isRTL ? '-100%' : '100%' }}
              transition={{ type: 'spring', damping: 25, stiffness: 300 }}
              className={`fixed top-0 ${isRTL ? 'left-0' : 'right-0'} h-full w-80 bg-white shadow-2xl z-50 overflow-y-auto safe-area-inset`}
            >
              {/* Menu Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center">
                    <HomeIcon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
                    {user && (
                      <p className="text-sm text-gray-500">{user.name}</p>
                    )}
                  </div>
                </div>
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <XMarkIcon className="w-6 h-6 text-gray-500" />
                </button>
              </div>

              {/* Menu Items */}
              <div className="py-4">
                {menuItems.map((item, index) => (
                  <motion.div
                    key={item.path}
                    initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Link
                      to={item.path}
                      onClick={() => setIsMenuOpen(false)}
                      className="flex items-center justify-between px-6 py-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-3">
                        <item.icon className="w-5 h-5 text-gray-400" />
                        <span className="text-gray-700 font-medium">{item.label}</span>
                      </div>
                      {item.badge && (
                        <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </Link>
                  </motion.div>
                ))}
              </div>

              {/* User Actions */}
              {isAuthenticated ? (
                <div className="border-t border-gray-200 p-6">
                  <Link
                    to="/dashboard"
                    onClick={() => setIsMenuOpen(false)}
                    className="block w-full bg-primary-600 text-white text-center py-3 rounded-lg font-medium mb-3 hover:bg-primary-700 transition-colors"
                  >
                    Dashboard
                  </Link>
                  <button className="block w-full text-red-600 text-center py-2 font-medium hover:text-red-700 transition-colors">
                    Logout
                  </button>
                </div>
              ) : (
                <div className="border-t border-gray-200 p-6 space-y-3">
                  <Link
                    to="/login"
                    onClick={() => setIsMenuOpen(false)}
                    className="block w-full bg-primary-600 text-white text-center py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors"
                  >
                    Login
                  </Link>
                  <Link
                    to="/register"
                    onClick={() => setIsMenuOpen(false)}
                    className="block w-full border border-primary-600 text-primary-600 text-center py-3 rounded-lg font-medium hover:bg-primary-50 transition-colors"
                  >
                    Sign Up
                  </Link>
                </div>
              )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default MobileNavigation;
