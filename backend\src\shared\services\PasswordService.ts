/**
 * Password Service
 * Handles password hashing, verification, and security policies
 */

import bcrypt from 'bcryptjs';
import crypto from 'crypto';

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxAge: number; // days
  preventReuse: number; // number of previous passwords to check
}

export interface PasswordStrength {
  score: number; // 0-100
  level: 'weak' | 'fair' | 'good' | 'strong' | 'very-strong';
  feedback: string[];
  estimatedCrackTime: string;
}

export class PasswordService {
  private readonly saltRounds = 12;
  private readonly defaultPolicy: PasswordPolicy = {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: true,
    maxAge: 90,
    preventReuse: 5
  };

  /**
   * Hash a password using bcrypt
   */
  async hash(password: string): Promise<string> {
    return bcrypt.hash(password, this.saltRounds);
  }

  /**
   * Verify a password against its hash
   */
  async verify(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Generate a secure random password
   */
  generateSecurePassword(length: number = 16): string {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';
    
    const allChars = uppercase + lowercase + numbers + specialChars;
    let password = '';
    
    // Ensure at least one character from each required set
    password += this.getRandomChar(uppercase);
    password += this.getRandomChar(lowercase);
    password += this.getRandomChar(numbers);
    password += this.getRandomChar(specialChars);
    
    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += this.getRandomChar(allChars);
    }
    
    // Shuffle the password
    return this.shuffleString(password);
  }

  /**
   * Validate password against policy
   */
  validatePassword(password: string, policy: Partial<PasswordPolicy> = {}): {
    isValid: boolean;
    errors: string[];
  } {
    const activePolicy = { ...this.defaultPolicy, ...policy };
    const errors: string[] = [];

    // Length check
    if (password.length < activePolicy.minLength) {
      errors.push(`Password must be at least ${activePolicy.minLength} characters long`);
    }

    // Uppercase check
    if (activePolicy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    // Lowercase check
    if (activePolicy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    // Numbers check
    if (activePolicy.requireNumbers && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    // Special characters check
    if (activePolicy.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    // Common password check
    if (this.isCommonPassword(password)) {
      errors.push('Password is too common. Please choose a more unique password');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Calculate password strength
   */
  calculatePasswordStrength(password: string): PasswordStrength {
    let score = 0;
    const feedback: string[] = [];

    // Length scoring
    if (password.length >= 8) score += 10;
    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 10;
    else if (password.length < 8) feedback.push('Use at least 8 characters');

    // Character variety scoring
    if (/[a-z]/.test(password)) score += 10;
    else feedback.push('Add lowercase letters');

    if (/[A-Z]/.test(password)) score += 10;
    else feedback.push('Add uppercase letters');

    if (/\d/.test(password)) score += 10;
    else feedback.push('Add numbers');

    if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) score += 15;
    else feedback.push('Add special characters');

    // Pattern checks
    if (!/(.)\1{2,}/.test(password)) score += 10; // No repeated characters
    else feedback.push('Avoid repeated characters');

    if (!/012|123|234|345|456|567|678|789|890|abc|bcd|cde|def/.test(password.toLowerCase())) {
      score += 10; // No sequential characters
    } else {
      feedback.push('Avoid sequential characters');
    }

    // Dictionary word check
    if (!this.containsDictionaryWord(password)) score += 15;
    else feedback.push('Avoid common words');

    // Determine level and crack time
    let level: PasswordStrength['level'];
    let estimatedCrackTime: string;

    if (score < 30) {
      level = 'weak';
      estimatedCrackTime = 'Less than a day';
    } else if (score < 50) {
      level = 'fair';
      estimatedCrackTime = 'A few days';
    } else if (score < 70) {
      level = 'good';
      estimatedCrackTime = 'A few months';
    } else if (score < 90) {
      level = 'strong';
      estimatedCrackTime = 'Several years';
    } else {
      level = 'very-strong';
      estimatedCrackTime = 'Centuries';
    }

    return {
      score: Math.min(score, 100),
      level,
      feedback,
      estimatedCrackTime
    };
  }

  /**
   * Generate password reset token
   */
  generateResetToken(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * Check if password has expired
   */
  isPasswordExpired(lastChanged: Date, maxAge: number = this.defaultPolicy.maxAge): boolean {
    const ageInDays = (Date.now() - lastChanged.getTime()) / (1000 * 60 * 60 * 24);
    return ageInDays > maxAge;
  }

  /**
   * Hash password for comparison (used in password history)
   */
  async hashForHistory(password: string): Promise<string> {
    // Use a different salt for history to prevent rainbow table attacks
    return crypto.createHash('sha256').update(password + process.env.PASSWORD_HISTORY_SALT).digest('hex');
  }

  private getRandomChar(chars: string): string {
    return chars.charAt(Math.floor(Math.random() * chars.length));
  }

  private shuffleString(str: string): string {
    const arr = str.split('');
    for (let i = arr.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [arr[i], arr[j]] = [arr[j], arr[i]];
    }
    return arr.join('');
  }

  private isCommonPassword(password: string): boolean {
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      'dragon', 'master', 'shadow', 'superman', 'michael',
      'football', 'baseball', 'liverpool', 'jordan', 'princess'
    ];
    
    return commonPasswords.includes(password.toLowerCase());
  }

  private containsDictionaryWord(password: string): boolean {
    // Simple dictionary word check
    // In production, you might want to use a more comprehensive dictionary
    const commonWords = [
      'password', 'admin', 'user', 'login', 'welcome', 'hello',
      'world', 'computer', 'internet', 'email', 'phone', 'house',
      'money', 'love', 'family', 'friend', 'work', 'school'
    ];
    
    const lowerPassword = password.toLowerCase();
    return commonWords.some(word => lowerPassword.includes(word));
  }
}
