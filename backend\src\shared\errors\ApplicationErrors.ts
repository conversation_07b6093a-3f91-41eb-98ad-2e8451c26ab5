/**
 * Application Error Classes
 * Defines custom error types for better error handling
 */

export abstract class ApplicationError extends Error {
  abstract readonly statusCode: number;
  abstract readonly errorCode: string;
  abstract readonly isOperational: boolean;

  constructor(
    message: string,
    public readonly details?: any,
    public readonly metadata?: Record<string, any>
  ) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      statusCode: this.statusCode,
      errorCode: this.errorCode,
      details: this.details,
      metadata: this.metadata,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Validation Error (400)
 */
export class ValidationError extends ApplicationError {
  readonly statusCode = 400;
  readonly errorCode = 'VALIDATION_ERROR';
  readonly isOperational = true;

  constructor(message: string = 'Validation failed', details?: string[] | Record<string, string>) {
    super(message, details);
  }
}

/**
 * Unauthorized Error (401)
 */
export class UnauthorizedError extends ApplicationError {
  readonly statusCode = 401;
  readonly errorCode = 'UNAUTHORIZED';
  readonly isOperational = true;

  constructor(message: string = 'Authentication required') {
    super(message);
  }
}

/**
 * Forbidden Error (403)
 */
export class ForbiddenError extends ApplicationError {
  readonly statusCode = 403;
  readonly errorCode = 'FORBIDDEN';
  readonly isOperational = true;

  constructor(message: string = 'Access denied') {
    super(message);
  }
}

/**
 * Not Found Error (404)
 */
export class NotFoundError extends ApplicationError {
  readonly statusCode = 404;
  readonly errorCode = 'NOT_FOUND';
  readonly isOperational = true;

  constructor(message: string = 'Resource not found', resourceType?: string, resourceId?: string) {
    super(message, { resourceType, resourceId });
  }
}

/**
 * Conflict Error (409)
 */
export class ConflictError extends ApplicationError {
  readonly statusCode = 409;
  readonly errorCode = 'CONFLICT';
  readonly isOperational = true;

  constructor(message: string = 'Resource conflict', conflictType?: string) {
    super(message, { conflictType });
  }
}

/**
 * Rate Limit Error (429)
 */
export class RateLimitError extends ApplicationError {
  readonly statusCode = 429;
  readonly errorCode = 'RATE_LIMIT_EXCEEDED';
  readonly isOperational = true;

  constructor(
    message: string = 'Rate limit exceeded',
    retryAfter?: number,
    limit?: number,
    remaining?: number
  ) {
    super(message, { retryAfter, limit, remaining });
  }
}

/**
 * Internal Server Error (500)
 */
export class InternalServerError extends ApplicationError {
  readonly statusCode = 500;
  readonly errorCode = 'INTERNAL_SERVER_ERROR';
  readonly isOperational = false;

  constructor(message: string = 'Internal server error', originalError?: Error) {
    super(message, { originalError: originalError?.message });
  }
}

/**
 * Service Unavailable Error (503)
 */
export class ServiceUnavailableError extends ApplicationError {
  readonly statusCode = 503;
  readonly errorCode = 'SERVICE_UNAVAILABLE';
  readonly isOperational = true;

  constructor(message: string = 'Service temporarily unavailable', retryAfter?: number) {
    super(message, { retryAfter });
  }
}

/**
 * Database Error
 */
export class DatabaseError extends ApplicationError {
  readonly statusCode = 500;
  readonly errorCode = 'DATABASE_ERROR';
  readonly isOperational = false;

  constructor(message: string = 'Database operation failed', operation?: string, table?: string) {
    super(message, { operation, table });
  }
}

/**
 * External Service Error
 */
export class ExternalServiceError extends ApplicationError {
  readonly statusCode = 502;
  readonly errorCode = 'EXTERNAL_SERVICE_ERROR';
  readonly isOperational = true;

  constructor(
    message: string = 'External service error',
    serviceName?: string,
    serviceResponse?: any
  ) {
    super(message, { serviceName, serviceResponse });
  }
}

/**
 * File Upload Error
 */
export class FileUploadError extends ApplicationError {
  readonly statusCode = 400;
  readonly errorCode = 'FILE_UPLOAD_ERROR';
  readonly isOperational = true;

  constructor(
    message: string = 'File upload failed',
    fileName?: string,
    fileSize?: number,
    maxSize?: number
  ) {
    super(message, { fileName, fileSize, maxSize });
  }
}

/**
 * Payment Error
 */
export class PaymentError extends ApplicationError {
  readonly statusCode = 402;
  readonly errorCode = 'PAYMENT_ERROR';
  readonly isOperational = true;

  constructor(
    message: string = 'Payment processing failed',
    paymentMethod?: string,
    transactionId?: string
  ) {
    super(message, { paymentMethod, transactionId });
  }
}

/**
 * Business Logic Error
 */
export class BusinessLogicError extends ApplicationError {
  readonly statusCode = 422;
  readonly errorCode = 'BUSINESS_LOGIC_ERROR';
  readonly isOperational = true;

  constructor(message: string, businessRule?: string) {
    super(message, { businessRule });
  }
}

/**
 * Configuration Error
 */
export class ConfigurationError extends ApplicationError {
  readonly statusCode = 500;
  readonly errorCode = 'CONFIGURATION_ERROR';
  readonly isOperational = false;

  constructor(message: string = 'Configuration error', configKey?: string) {
    super(message, { configKey });
  }
}

/**
 * Network Error
 */
export class NetworkError extends ApplicationError {
  readonly statusCode = 503;
  readonly errorCode = 'NETWORK_ERROR';
  readonly isOperational = true;

  constructor(message: string = 'Network error', host?: string, timeout?: number) {
    super(message, { host, timeout });
  }
}

/**
 * Security Error
 */
export class SecurityError extends ApplicationError {
  readonly statusCode = 403;
  readonly errorCode = 'SECURITY_ERROR';
  readonly isOperational = true;

  constructor(message: string = 'Security violation detected', violationType?: string) {
    super(message, { violationType });
  }
}

/**
 * Error Factory for creating appropriate error instances
 */
export class ErrorFactory {
  static createFromHttpStatus(statusCode: number, message?: string): ApplicationError {
    switch (statusCode) {
      case 400:
        return new ValidationError(message);
      case 401:
        return new UnauthorizedError(message);
      case 403:
        return new ForbiddenError(message);
      case 404:
        return new NotFoundError(message);
      case 409:
        return new ConflictError(message);
      case 422:
        return new BusinessLogicError(message || 'Business logic error');
      case 429:
        return new RateLimitError(message);
      case 500:
        return new InternalServerError(message);
      case 502:
        return new ExternalServiceError(message);
      case 503:
        return new ServiceUnavailableError(message);
      default:
        return new InternalServerError(message || `HTTP ${statusCode} error`);
    }
  }

  static isOperationalError(error: Error): boolean {
    if (error instanceof ApplicationError) {
      return error.isOperational;
    }
    return false;
  }
}

/**
 * Error Handler Utility
 */
export class ErrorHandler {
  static handle(error: Error): {
    statusCode: number;
    errorCode: string;
    message: string;
    details?: any;
    stack?: string;
  } {
    if (error instanceof ApplicationError) {
      return {
        statusCode: error.statusCode,
        errorCode: error.errorCode,
        message: error.message,
        details: error.details,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      };
    }

    // Handle known error types
    if (error.name === 'ValidationError') {
      return {
        statusCode: 400,
        errorCode: 'VALIDATION_ERROR',
        message: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      };
    }

    if (error.name === 'CastError') {
      return {
        statusCode: 400,
        errorCode: 'INVALID_ID',
        message: 'Invalid ID format',
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      };
    }

    // Default to internal server error
    return {
      statusCode: 500,
      errorCode: 'INTERNAL_SERVER_ERROR',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    };
  }
}
