import { z } from 'zod';

// Common validation patterns
const phoneRegex = /^(\+93|0)?[7-9]\d{8}$/; // Afghan phone numbers
const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;

// User validation schemas
export const registerSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name cannot exceed 50 characters')
    .regex(/^[a-zA-Z\s\u0600-\u06FF]+$/, 'Name can only contain letters and spaces'),
  
  email: z
    .string()
    .email('Please enter a valid email address')
    .min(5, 'Email must be at least 5 characters')
    .max(100, 'Email cannot exceed 100 characters')
    .toLowerCase(),
  
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password cannot exceed 128 characters')
    .regex(passwordRegex, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  
  confirmPassword: z.string(),
  
  phone: z
    .string()
    .optional()
    .refine((val) => !val || phoneRegex.test(val), {
      message: 'Please enter a valid Afghan phone number (e.g., +93701234567 or 0701234567)'
    }),
  
  role: z.enum(['USER', 'AGENT'], {
    errorMap: () => ({ message: 'Please select a valid role' })
  }).optional(),
  
  terms: z
    .boolean()
    .refine((val) => val === true, {
      message: 'You must accept the terms and conditions'
    })
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
});

export const loginSchema = z.object({
  email: z
    .string()
    .email('Please enter a valid email address')
    .min(1, 'Email is required'),
  
  password: z
    .string()
    .min(1, 'Password is required'),
  
  rememberMe: z.boolean().optional()
});

export const forgotPasswordSchema = z.object({
  email: z
    .string()
    .email('Please enter a valid email address')
    .min(1, 'Email is required')
});

export const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(passwordRegex, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: z
    .string()
    .min(8, 'New password must be at least 8 characters')
    .regex(passwordRegex, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword']
});

// Profile validation schema
export const profileUpdateSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name cannot exceed 50 characters')
    .regex(/^[a-zA-Z\s\u0600-\u06FF]+$/, 'Name can only contain letters and spaces'),
  
  phone: z
    .string()
    .optional()
    .refine((val) => !val || phoneRegex.test(val), {
      message: 'Please enter a valid Afghan phone number'
    }),
  
  bio: z
    .string()
    .max(500, 'Bio cannot exceed 500 characters')
    .optional(),
  
  location: z
    .string()
    .max(100, 'Location cannot exceed 100 characters')
    .optional(),
  
  website: z
    .string()
    .url('Please enter a valid website URL')
    .optional()
    .or(z.literal(''))
});

// Property validation schema
export const propertySchema = z.object({
  title: z
    .string()
    .min(5, 'Title must be at least 5 characters')
    .max(200, 'Title cannot exceed 200 characters'),
  
  description: z
    .string()
    .min(20, 'Description must be at least 20 characters')
    .max(2000, 'Description cannot exceed 2000 characters'),
  
  price: z
    .number()
    .min(1, 'Price must be greater than 0')
    .max(999999999, 'Price is too high'),
  
  currency: z.enum(['AFN', 'USD', 'EUR'], {
    errorMap: () => ({ message: 'Please select a valid currency' })
  }),
  
  category: z.enum(['SALE', 'RENT', 'LEASE'], {
    errorMap: () => ({ message: 'Please select a valid category' })
  }),
  
  type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'LAND', 'APARTMENT', 'HOUSE', 'VILLA', 'OFFICE', 'SHOP', 'WAREHOUSE', 'FARM'], {
    errorMap: () => ({ message: 'Please select a valid property type' })
  }),
  
  province: z
    .string()
    .min(2, 'Province is required')
    .max(50, 'Province name is too long'),
  
  city: z
    .string()
    .min(2, 'City is required')
    .max(50, 'City name is too long'),
  
  district: z
    .string()
    .max(50, 'District name is too long')
    .optional(),
  
  address: z
    .string()
    .min(10, 'Address must be at least 10 characters')
    .max(200, 'Address cannot exceed 200 characters'),
  
  latitude: z
    .number()
    .min(-90, 'Invalid latitude')
    .max(90, 'Invalid latitude')
    .optional(),
  
  longitude: z
    .number()
    .min(-180, 'Invalid longitude')
    .max(180, 'Invalid longitude')
    .optional(),
  
  bedrooms: z
    .number()
    .min(0, 'Bedrooms cannot be negative')
    .max(20, 'Too many bedrooms')
    .optional(),
  
  bathrooms: z
    .number()
    .min(0, 'Bathrooms cannot be negative')
    .max(20, 'Too many bathrooms')
    .optional(),
  
  area: z
    .number()
    .min(1, 'Area must be at least 1 square meter')
    .max(100000, 'Area is too large'),
  
  yearBuilt: z
    .number()
    .min(1800, 'Year built cannot be before 1800')
    .max(new Date().getFullYear() + 5, 'Year built cannot be more than 5 years in the future')
    .optional(),
  
  furnished: z.boolean().optional(),
  parking: z.boolean().optional(),
  garden: z.boolean().optional(),
  balcony: z.boolean().optional(),
  
  features: z
    .array(z.string().min(1, 'Feature cannot be empty'))
    .max(20, 'Too many features')
    .optional()
});

// Message validation schema
export const messageSchema = z.object({
  receiverId: z.string().min(1, 'Receiver is required'),
  propertyId: z.string().optional(),
  subject: z
    .string()
    .max(200, 'Subject cannot exceed 200 characters')
    .optional(),
  content: z
    .string()
    .min(1, 'Message content is required')
    .max(2000, 'Message cannot exceed 2000 characters')
});

// Search validation schema
export const searchSchema = z.object({
  query: z.string().optional(),
  category: z.enum(['SALE', 'RENT', 'LEASE']).optional(),
  type: z.enum(['RESIDENTIAL', 'COMMERCIAL', 'LAND', 'APARTMENT', 'HOUSE', 'VILLA', 'OFFICE', 'SHOP', 'WAREHOUSE', 'FARM']).optional(),
  province: z.string().optional(),
  city: z.string().optional(),
  district: z.string().optional(),
  minPrice: z.number().min(0, 'Minimum price cannot be negative').optional(),
  maxPrice: z.number().min(0, 'Maximum price cannot be negative').optional(),
  minArea: z.number().min(0, 'Minimum area cannot be negative').optional(),
  maxArea: z.number().min(0, 'Maximum area cannot be negative').optional(),
  bedrooms: z.number().min(0, 'Bedrooms cannot be negative').optional(),
  bathrooms: z.number().min(0, 'Bathrooms cannot be negative').optional(),
  furnished: z.boolean().optional(),
  parking: z.boolean().optional(),
  garden: z.boolean().optional(),
  balcony: z.boolean().optional()
}).refine((data) => {
  if (data.minPrice && data.maxPrice && data.minPrice > data.maxPrice) {
    return false;
  }
  if (data.minArea && data.maxArea && data.minArea > data.maxArea) {
    return false;
  }
  return true;
}, {
  message: 'Minimum values cannot be greater than maximum values'
});

// Type exports
export type RegisterFormData = z.infer<typeof registerSchema>;
export type LoginFormData = z.infer<typeof loginSchema>;
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>;
export type ChangePasswordFormData = z.infer<typeof changePasswordSchema>;
export type ProfileUpdateFormData = z.infer<typeof profileUpdateSchema>;
export type PropertyFormData = z.infer<typeof propertySchema>;
export type MessageFormData = z.infer<typeof messageSchema>;
export type SearchFormData = z.infer<typeof searchSchema>;
