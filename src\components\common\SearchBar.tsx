import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MagnifyingGlassIcon, 
  MapPinIcon, 
  HomeIcon,
  BuildingOfficeIcon,
  XMarkIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { useLanguage } from '../../contexts/LanguageContext';

interface SearchSuggestion {
  id: string;
  type: 'location' | 'property' | 'recent';
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
}

interface SearchBarProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  onSuggestionSelect?: (suggestion: SearchSuggestion) => void;
  suggestions?: SearchSuggestion[];
  showSuggestions?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'hero';
  className?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder,
  onSearch,
  onSuggestionSelect,
  suggestions = [],
  showSuggestions = true,
  size = 'md',
  variant = 'default',
  className = ''
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<SearchSuggestion[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { t, isRTL } = useLanguage();
  
  // Default suggestions
  const defaultSuggestions: SearchSuggestion[] = [
    {
      id: '1',
      type: 'location',
      title: 'Kabul, Afghanistan',
      subtitle: '1,234 properties',
      icon: <MapPinIcon className="w-4 h-4" />
    },
    {
      id: '2',
      type: 'location',
      title: 'Herat, Afghanistan',
      subtitle: '567 properties',
      icon: <MapPinIcon className="w-4 h-4" />
    },
    {
      id: '3',
      type: 'property',
      title: 'Apartments',
      subtitle: 'All apartments',
      icon: <BuildingOfficeIcon className="w-4 h-4" />
    },
    {
      id: '4',
      type: 'property',
      title: 'Houses',
      subtitle: 'All houses',
      icon: <HomeIcon className="w-4 h-4" />
    }
  ];
  
  const allSuggestions = [...suggestions, ...defaultSuggestions];
  
  useEffect(() => {
    if (query.trim()) {
      const filtered = allSuggestions.filter(suggestion =>
        suggestion.title.toLowerCase().includes(query.toLowerCase()) ||
        suggestion.subtitle?.toLowerCase().includes(query.toLowerCase())
      );
      setFilteredSuggestions(filtered);
    } else {
      setFilteredSuggestions(allSuggestions.slice(0, 6));
    }
  }, [query, suggestions]);
  
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    setIsOpen(true);
  };
  
  const handleSearch = () => {
    if (query.trim()) {
      onSearch?.(query);
      setIsOpen(false);
    }
  };
  
  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setQuery(suggestion.title);
    setIsOpen(false);
    onSuggestionSelect?.(suggestion);
  };
  
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    } else if (e.key === 'Escape') {
      setIsOpen(false);
    }
  };
  
  const clearSearch = () => {
    setQuery('');
    setIsOpen(false);
    inputRef.current?.focus();
  };
  
  const sizeClasses = {
    sm: 'h-10 text-sm',
    md: 'h-12 text-base',
    lg: 'h-14 text-lg'
  };
  
  const variantClasses = {
    default: 'bg-white border border-gray-300 shadow-sm',
    hero: 'bg-white/95 backdrop-blur-md border border-white/20 shadow-xl'
  };
  
  return (
    <div ref={searchRef} className={`relative w-full ${className}`}>
      <div className={`
        flex items-center rounded-xl transition-all duration-300 focus-within:ring-2 focus-within:ring-primary-500 focus-within:border-primary-500
        ${sizeClasses[size]} ${variantClasses[variant]}
        ${isRTL ? 'flex-row-reverse' : ''}
      `}>
        <div className={`flex items-center px-4 ${isRTL ? 'order-2' : ''}`}>
          <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder || t('search.placeholder')}
          className={`
            flex-1 bg-transparent border-none outline-none placeholder-gray-400
            ${isRTL ? 'text-right pr-4' : 'text-left pl-0'}
          `}
        />
        
        {query && (
          <button
            onClick={clearSearch}
            className={`p-2 text-gray-400 hover:text-gray-600 transition-colors ${isRTL ? 'order-1' : ''}`}
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        )}
        
        <button
          onClick={handleSearch}
          className={`
            px-6 py-2 bg-gradient-primary text-white rounded-lg font-medium hover:shadow-lg transition-all duration-300 transform hover:scale-105
            ${isRTL ? 'order-1 mr-2' : 'mr-2'}
          `}
        >
          {t('search.button')}
        </button>
      </div>
      
      {/* Suggestions Dropdown */}
      <AnimatePresence>
        {isOpen && showSuggestions && filteredSuggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-xl border border-gray-200 z-50 max-h-80 overflow-y-auto"
          >
            {filteredSuggestions.map((suggestion, index) => (
              <motion.button
                key={suggestion.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => handleSuggestionClick(suggestion)}
                className={`
                  w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-3 border-b border-gray-100 last:border-b-0
                  ${isRTL ? 'flex-row-reverse space-x-reverse text-right' : ''}
                `}
              >
                <div className={`flex-shrink-0 text-gray-400 ${suggestion.type === 'recent' ? 'text-primary-500' : ''}`}>
                  {suggestion.type === 'recent' ? (
                    <ClockIcon className="w-4 h-4" />
                  ) : (
                    suggestion.icon
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {suggestion.title}
                  </p>
                  {suggestion.subtitle && (
                    <p className="text-xs text-gray-500 truncate">
                      {suggestion.subtitle}
                    </p>
                  )}
                </div>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SearchBar;
