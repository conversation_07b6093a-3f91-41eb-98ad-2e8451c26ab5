// Simple validation test
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function simpleTest() {
  console.log('🧪 Simple Validation Test...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check...');
    const health = await axios.get('http://localhost:3001/health');
    console.log('✅ Health check:', health.data.status);

    // Test 2: Invalid registration
    console.log('\n2. Testing invalid registration...');
    try {
      await axios.post(`${BASE_URL}/auth/register`, {
        email: 'invalid-email',
        password: '123',
        name: 'A'
      });
    } catch (error) {
      console.log('✅ Invalid registration caught:', error.response?.status);
    }

    // Test 3: Valid registration
    console.log('\n3. Testing valid registration...');
    try {
      const response = await axios.post(`${BASE_URL}/auth/register`, {
        email: '<EMAIL>',
        password: 'ValidPass123!',
        name: 'Test User'
      });
      console.log('✅ Valid registration successful');
      console.log('User created:', response.data.data.user.name);
    } catch (error) {
      if (error.response?.data?.error?.message?.includes('already exists')) {
        console.log('⚠️  User already exists - validation working');
      } else {
        console.log('❌ Registration error:', error.response?.data?.error?.message);
      }
    }

    console.log('\n🎉 Simple validation test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

simpleTest();
