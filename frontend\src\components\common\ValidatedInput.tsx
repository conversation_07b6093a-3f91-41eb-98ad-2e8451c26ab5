import React, { forwardRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { EyeIcon, EyeSlashIcon, ExclamationCircleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface ValidatedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  isValid?: boolean;
  isLoading?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  showPasswordToggle?: boolean;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

const ValidatedInput = forwardRef<HTMLInputElement, ValidatedInputProps>(
  ({
    label,
    error,
    helperText,
    isValid,
    isLoading,
    leftIcon,
    rightIcon,
    showPasswordToggle = false,
    variant = 'outlined',
    size = 'md',
    fullWidth = true,
    type = 'text',
    className = '',
    disabled,
    ...props
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const [isFocused, setIsFocused] = useState(false);

    const inputType = showPasswordToggle && type === 'password' 
      ? (showPassword ? 'text' : 'password') 
      : type;

    const hasError = !!error;
    const hasSuccess = isValid && !hasError && !isFocused;

    // Size classes
    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-5 py-4 text-lg'
    };

    // Variant classes
    const variantClasses = {
      default: 'border-0 bg-gray-50 focus:bg-white',
      filled: 'border-0 bg-gray-100 focus:bg-gray-50',
      outlined: 'border border-gray-300 bg-white'
    };

    // State classes
    const stateClasses = hasError
      ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
      : hasSuccess
      ? 'border-green-500 focus:border-green-500 focus:ring-green-500'
      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500';

    const inputClasses = `
      ${fullWidth ? 'w-full' : ''}
      ${sizeClasses[size]}
      ${variantClasses[variant]}
      ${stateClasses}
      ${leftIcon ? 'pl-10' : ''}
      ${rightIcon || showPasswordToggle || hasError || hasSuccess ? 'pr-10' : ''}
      ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      rounded-lg
      transition-all
      duration-200
      focus:outline-none
      focus:ring-2
      focus:ring-opacity-50
      placeholder-gray-400
      ${className}
    `.trim().replace(/\s+/g, ' ');

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className={`${fullWidth ? 'w-full' : ''} space-y-1`}>
        {/* Label */}
        {label && (
          <motion.label
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`
              block text-sm font-medium
              ${hasError ? 'text-red-700' : hasSuccess ? 'text-green-700' : 'text-gray-700'}
              ${disabled ? 'opacity-50' : ''}
            `}
          >
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </motion.label>
        )}

        {/* Input Container */}
        <div className="relative">
          {/* Left Icon */}
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className={`
                ${hasError ? 'text-red-500' : hasSuccess ? 'text-green-500' : 'text-gray-400'}
                ${size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'}
              `}>
                {leftIcon}
              </div>
            </div>
          )}

          {/* Input Field */}
          <input
            ref={ref}
            type={inputType}
            disabled={disabled || isLoading}
            className={inputClasses}
            onFocus={(e) => {
              setIsFocused(true);
              props.onFocus?.(e);
            }}
            onBlur={(e) => {
              setIsFocused(false);
              props.onBlur?.(e);
            }}
            {...props}
          />

          {/* Right Icons */}
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center space-x-1">
            {/* Loading Spinner */}
            {isLoading && (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full"
              />
            )}

            {/* Success Icon */}
            {hasSuccess && !isLoading && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="text-green-500"
              >
                <CheckCircleIcon className="w-5 h-5" />
              </motion.div>
            )}

            {/* Error Icon */}
            {hasError && !isLoading && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="text-red-500"
              >
                <ExclamationCircleIcon className="w-5 h-5" />
              </motion.div>
            )}

            {/* Password Toggle */}
            {showPasswordToggle && !isLoading && (
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className={`
                  text-gray-400 hover:text-gray-600 focus:outline-none
                  ${hasError ? 'text-red-400 hover:text-red-600' : ''}
                  ${hasSuccess ? 'text-green-400 hover:text-green-600' : ''}
                `}
              >
                {showPassword ? (
                  <EyeSlashIcon className="w-5 h-5" />
                ) : (
                  <EyeIcon className="w-5 h-5" />
                )}
              </button>
            )}

            {/* Custom Right Icon */}
            {rightIcon && !isLoading && !hasError && !hasSuccess && (
              <div className={`
                text-gray-400
                ${size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : 'w-5 h-5'}
              `}>
                {rightIcon}
              </div>
            )}
          </div>
        </div>

        {/* Helper Text / Error Message */}
        <AnimatePresence mode="wait">
          {(error || helperText) && (
            <motion.div
              initial={{ opacity: 0, y: -10, height: 0 }}
              animate={{ opacity: 1, y: 0, height: 'auto' }}
              exit={{ opacity: 0, y: -10, height: 0 }}
              transition={{ duration: 0.2 }}
              className={`
                text-sm
                ${hasError ? 'text-red-600' : 'text-gray-500'}
                ${disabled ? 'opacity-50' : ''}
              `}
            >
              {error || helperText}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Focus Ring Animation */}
        {isFocused && (
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.95, opacity: 0 }}
            className={`
              absolute inset-0 rounded-lg pointer-events-none
              ${hasError ? 'ring-2 ring-red-500 ring-opacity-20' : 'ring-2 ring-blue-500 ring-opacity-20'}
            `}
          />
        )}
      </div>
    );
  }
);

ValidatedInput.displayName = 'ValidatedInput';

export default ValidatedInput;
