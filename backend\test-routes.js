const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function testRoutes() {
  console.log('🧪 Testing API Routes...\n');

  try {
    // Test API documentation endpoint
    console.log('1. Testing API documentation...');
    const apiResponse = await axios.get(`${BASE_URL}`);
    console.log('✅ API docs accessible');
    console.log('📚 Available endpoints:', Object.keys(apiResponse.data.endpoints));

    // Test health check
    console.log('\n2. Testing health check...');
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log('✅ Health check:', healthResponse.data.status);

    // Test specific routes
    console.log('\n3. Testing specific routes...');
    
    const routes = [
      { method: 'GET', path: '/users/favorites', expectAuth: true },
      { method: 'GET', path: '/users/dashboard/stats', expectAuth: true },
      { method: 'GET', path: '/properties', expectAuth: false },
      { method: 'POST', path: '/properties', expectAuth: true },
      { method: 'GET', path: '/auth/profile', expectAuth: true }
    ];

    for (const route of routes) {
      try {
        const response = await axios({
          method: route.method,
          url: `${BASE_URL}${route.path}`
        });
        
        if (route.expectAuth) {
          console.log(`❌ ${route.method} ${route.path} - Should require auth but didn't`);
        } else {
          console.log(`✅ ${route.method} ${route.path} - Accessible without auth`);
        }
      } catch (error) {
        if (route.expectAuth && error.response?.status === 401) {
          console.log(`✅ ${route.method} ${route.path} - Correctly requires auth`);
        } else if (!route.expectAuth && error.response?.status === 404) {
          console.log(`❌ ${route.method} ${route.path} - Route not found`);
        } else {
          console.log(`⚠️  ${route.method} ${route.path} - Status: ${error.response?.status || 'Unknown'}, Message: ${error.response?.data?.message || error.message}`);
        }
      }
    }

    console.log('\n🎉 Route tests completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testRoutes();
