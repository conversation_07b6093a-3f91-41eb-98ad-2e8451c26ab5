import React from 'react';
import { Link } from 'react-router-dom';
import { 
  CheckCircleIcon,
  XMarkIcon,
  HomeIcon,
  EyeIcon,
  ShareIcon
} from '@heroicons/react/24/outline';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  propertyId?: number;
}

const SuccessModal: React.FC<SuccessModalProps> = ({ 
  isOpen, 
  onClose, 
  title, 
  message, 
  propertyId 
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-large max-w-md w-full animate-scale-in">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-secondary-100 rounded-full flex items-center justify-center">
                <CheckCircleIcon className="w-8 h-8 text-secondary-500" />
              </div>
              <h3 className="text-xl font-bold text-gray-900">{title}</h3>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-300"
            >
              <XMarkIcon className="w-5 h-5 text-gray-500" />
            </button>
          </div>
          
          <p className="text-gray-600 mb-8 leading-relaxed">
            {message}
          </p>
          
          <div className="space-y-3">
            {propertyId && (
              <Link
                to={`/property/${propertyId}`}
                className="btn-primary w-full flex items-center justify-center space-x-2"
                onClick={onClose}
              >
                <EyeIcon className="w-5 h-5" />
                <span>View Your Listing</span>
              </Link>
            )}
            
            <Link
              to="/listings"
              className="btn-outline w-full flex items-center justify-center space-x-2"
              onClick={onClose}
            >
              <HomeIcon className="w-5 h-5" />
              <span>Browse Properties</span>
            </Link>
            
            <button
              onClick={onClose}
              className="w-full text-center py-3 text-gray-600 hover:text-gray-800 transition-colors duration-300"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
