// Form data types for property posting
export interface PropertyStep1FormData {
  title: string;
  propertyType: string;
  category: string;
  province: string;
  city: string;
  district: string;
  address: string;
}

export interface PropertyStep2FormData {
  bedrooms: number;
  bathrooms: number;
  area: number;
  description: string;
}

export interface PropertyStep4FormData {
  price: number;
  contactName: string;
  phone: string;
}

// Other form types
export interface LoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  role?: 'USER' | 'AGENT';
  agreeToTerms: boolean;
}

export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
}

export interface SearchFormData {
  query?: string;
  location?: string;
  propertyType?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: number;
  bathrooms?: number;
  minArea?: number;
  maxArea?: number;
}
