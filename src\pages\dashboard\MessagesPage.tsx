import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon,
  PhotoIcon,
  FaceSmileIcon,
  MagnifyingGlassIcon,
  EllipsisVerticalIcon,
  PhoneIcon,
  VideoCameraIcon,
  InformationCircleIcon,
  HomeIcon,
  CheckIcon
} from '@heroicons/react/24/outline';

interface Message {
  id: number;
  senderId: number;
  receiverId: number;
  content: string;
  timestamp: Date;
  read: boolean;
  type: 'text' | 'image' | 'property';
  propertyId?: number;
}

interface Conversation {
  id: number;
  participants: {
    id: number;
    name: string;
    avatar: string;
    online: boolean;
  }[];
  lastMessage: Message;
  unreadCount: number;
  property?: {
    id: number;
    title: string;
    image: string;
    price: string;
  };
}

const MessagesPage: React.FC = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Mock data
  useEffect(() => {
    const mockConversations: Conversation[] = [
      {
        id: 1,
        participants: [
          {
            id: 2,
            name: 'Ahmad Rezai',
            avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
            online: true
          }
        ],
        lastMessage: {
          id: 1,
          senderId: 2,
          receiverId: 1,
          content: 'Hi! I\'m interested in your villa. Is it still available?',
          timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          read: false,
          type: 'text'
        },
        unreadCount: 2,
        property: {
          id: 1,
          title: 'Modern Villa in Wazir Akbar Khan',
          image: 'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
          price: '$250,000'
        }
      },
      {
        id: 2,
        participants: [
          {
            id: 3,
            name: 'Fatima Khan',
            avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80',
            online: false
          }
        ],
        lastMessage: {
          id: 2,
          senderId: 1,
          receiverId: 3,
          content: 'Thank you for your interest. Yes, it\'s available for viewing.',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          read: true,
          type: 'text'
        },
        unreadCount: 0,
        property: {
          id: 2,
          title: 'Luxury Apartment in Shahr-e-Naw',
          image: 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80',
          price: '$180,000'
        }
      }
    ];

    const mockMessages: Message[] = [
      {
        id: 1,
        senderId: 2,
        receiverId: 1,
        content: 'Hello! I saw your villa listing and I\'m very interested.',
        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        read: true,
        type: 'text'
      },
      {
        id: 2,
        senderId: 1,
        receiverId: 2,
        content: 'Hi Ahmad! Thank you for your interest. The villa is still available.',
        timestamp: new Date(Date.now() - 1000 * 60 * 50), // 50 minutes ago
        read: true,
        type: 'text'
      },
      {
        id: 3,
        senderId: 2,
        receiverId: 1,
        content: 'Great! Could we schedule a viewing? I\'m available this weekend.',
        timestamp: new Date(Date.now() - 1000 * 60 * 40), // 40 minutes ago
        read: true,
        type: 'text'
      },
      {
        id: 4,
        senderId: 1,
        receiverId: 2,
        content: 'Absolutely! Saturday at 2 PM works for me. Here\'s the property details:',
        timestamp: new Date(Date.now() - 1000 * 60 * 35), // 35 minutes ago
        read: true,
        type: 'text'
      },
      {
        id: 5,
        senderId: 2,
        receiverId: 1,
        content: 'Perfect! I\'ll be there. Is parking available?',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        read: false,
        type: 'text'
      }
    ];

    setConversations(mockConversations);
    setSelectedConversation(mockConversations[0]);
    setMessages(mockMessages);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedConversation || !user) return;

    const message: Message = {
      id: Date.now(),
      senderId: user.id,
      receiverId: selectedConversation.participants[0].id,
      content: newMessage,
      timestamp: new Date(),
      read: false,
      type: 'text'
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    // Update conversation's last message
    setConversations(prev => 
      prev.map(conv => 
        conv.id === selectedConversation.id 
          ? { ...conv, lastMessage: message }
          : conv
      )
    );
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'now';
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return `${days}d`;
  };

  const filteredConversations = conversations.filter(conv =>
    conv.participants[0].name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    conv.property?.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ChatBubbleLeftRightIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Please log in to view messages</h2>
          <Link to="/login" className="btn-primary">
            Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link to="/dashboard" className="text-gray-500 hover:text-primary-500 transition-colors duration-300">
                ← Dashboard
              </Link>
              <h1 className="text-xl font-bold text-gray-900">Messages</h1>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="bg-white rounded-xl shadow-soft overflow-hidden" style={{ height: 'calc(100vh - 200px)' }}>
          <div className="flex h-full">
            {/* Conversations List */}
            <div className="w-1/3 border-r border-gray-200 flex flex-col">
              {/* Search */}
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search conversations..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                  />
                </div>
              </div>

              {/* Conversations */}
              <div className="flex-1 overflow-y-auto">
                {filteredConversations.map((conversation) => {
                  const participant = conversation.participants[0];
                  const isSelected = selectedConversation?.id === conversation.id;
                  
                  return (
                    <div
                      key={conversation.id}
                      onClick={() => setSelectedConversation(conversation)}
                      className={`p-4 border-b border-gray-100 cursor-pointer transition-all duration-300 ${
                        isSelected ? 'bg-primary-50 border-primary-200' : 'hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="relative">
                          <img
                            src={participant.avatar}
                            alt={participant.name}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                          {participant.online && (
                            <div className="absolute bottom-0 right-0 w-3 h-3 bg-secondary-500 border-2 border-white rounded-full"></div>
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="text-sm font-semibold text-gray-900 truncate">
                              {participant.name}
                            </h3>
                            <span className="text-xs text-gray-500">
                              {formatTime(conversation.lastMessage.timestamp)}
                            </span>
                          </div>
                          
                          {conversation.property && (
                            <p className="text-xs text-primary-600 mb-1 truncate">
                              {conversation.property.title}
                            </p>
                          )}
                          
                          <div className="flex items-center justify-between">
                            <p className="text-sm text-gray-600 truncate">
                              {conversation.lastMessage.content}
                            </p>
                            {conversation.unreadCount > 0 && (
                              <span className="ml-2 bg-primary-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                                {conversation.unreadCount}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Chat Area */}
            {selectedConversation ? (
              <div className="flex-1 flex flex-col">
                {/* Chat Header */}
                <div className="p-4 border-b border-gray-200 bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <img
                          src={selectedConversation.participants[0].avatar}
                          alt={selectedConversation.participants[0].name}
                          className="w-10 h-10 rounded-full object-cover"
                        />
                        {selectedConversation.participants[0].online && (
                          <div className="absolute bottom-0 right-0 w-3 h-3 bg-secondary-500 border-2 border-white rounded-full"></div>
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">
                          {selectedConversation.participants[0].name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {selectedConversation.participants[0].online ? 'Online' : 'Offline'}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300">
                        <PhoneIcon className="w-5 h-5" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300">
                        <VideoCameraIcon className="w-5 h-5" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300">
                        <InformationCircleIcon className="w-5 h-5" />
                      </button>
                    </div>
                  </div>
                  
                  {/* Property Info */}
                  {selectedConversation.property && (
                    <div className="mt-3 p-3 bg-white rounded-lg border border-gray-200">
                      <div className="flex items-center space-x-3">
                        <img
                          src={selectedConversation.property.image}
                          alt={selectedConversation.property.title}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900">
                            {selectedConversation.property.title}
                          </h4>
                          <p className="text-sm text-primary-600 font-semibold">
                            {selectedConversation.property.price}
                          </p>
                        </div>
                        <Link
                          to={`/property/${selectedConversation.property.id}`}
                          className="text-primary-500 hover:text-primary-600 transition-colors duration-300"
                        >
                          <HomeIcon className="w-5 h-5" />
                        </Link>
                      </div>
                    </div>
                  )}
                </div>

                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {messages.map((message) => {
                    const isOwn = message.senderId === user?.id;
                    const participant = selectedConversation.participants[0];

                    return (
                      <div
                        key={message.id}
                        className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`flex items-end space-x-2 max-w-xs lg:max-w-md ${isOwn ? 'flex-row-reverse space-x-reverse' : ''}`}>
                          {!isOwn && (
                            <img
                              src={participant.avatar}
                              alt={participant.name}
                              className="w-8 h-8 rounded-full object-cover"
                            />
                          )}

                          <div className={`px-4 py-2 rounded-2xl ${
                            isOwn
                              ? 'bg-primary-500 text-white'
                              : 'bg-gray-200 text-gray-900'
                          }`}>
                            <p className="text-sm">{message.content}</p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  <div ref={messagesEndRef} />
                </div>

                {/* Message Input */}
                <div className="p-4 border-t border-gray-200 bg-white">
                  <div className="flex items-center space-x-3">
                    <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-300">
                      <PhotoIcon className="w-5 h-5" />
                    </button>

                    <div className="flex-1 relative">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                        placeholder="Type a message..."
                        className="w-full px-4 py-2 pr-12 border border-gray-300 rounded-full focus:ring-2 focus:ring-primary-200 focus:border-primary-500 transition-all duration-300"
                      />
                      <button className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-300">
                        <FaceSmileIcon className="w-5 h-5" />
                      </button>
                    </div>

                    <button
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim()}
                      className={`p-2 rounded-full transition-all duration-300 ${
                        newMessage.trim()
                          ? 'bg-primary-500 text-white hover:bg-primary-600 transform hover:scale-110'
                          : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      <PaperAirplaneIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex-1 flex items-center justify-center bg-gray-50">
                <div className="text-center">
                  <ChatBubbleLeftRightIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Select a conversation
                  </h3>
                  <p className="text-gray-500">
                    Choose a conversation from the list to start messaging
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessagesPage;
