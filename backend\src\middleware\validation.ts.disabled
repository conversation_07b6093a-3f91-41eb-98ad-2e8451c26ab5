import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { AppError } from './errorHandler';

// Common validation patterns
const phoneRegex = /^(\+93|0)?[7-9]\d{8}$/; // Afghan phone numbers
const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/;
const objectIdRegex = /^[0-9a-fA-F]{24}$/;

// User validation schemas
export const registerValidation = z.object({
  body: z.object({
    name: z
      .string({
        required_error: 'Full name is required',
        invalid_type_error: 'Name must be a string'
      })
      .min(2, 'Name must be at least 2 characters long')
      .max(50, 'Name cannot exceed 50 characters')
      .regex(/^[a-zA-Z\s\u0600-\u06FF]+$/, 'Name can only contain letters and spaces')
      .trim(),
    
    email: z
      .string({
        required_error: 'Email address is required',
        invalid_type_error: 'Email must be a string'
      })
      .email('Please provide a valid email address')
      .min(5, 'Email must be at least 5 characters long')
      .max(100, 'Email cannot exceed 100 characters')
      .toLowerCase()
      .trim(),
    
    password: z
      .string({
        required_error: 'Password is required',
        invalid_type_error: 'Password must be a string'
      })
      .min(8, 'Password must be at least 8 characters long')
      .max(128, 'Password cannot exceed 128 characters')
      .regex(passwordRegex, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
    phone: z
      .string()
      .optional()
      .refine((val) => !val || phoneRegex.test(val), {
        message: 'Please provide a valid Afghan phone number (e.g., +93701234567 or 0701234567)'
      }),
    
    role: z
      .enum(['USER', 'AGENT'], {
        errorMap: () => ({ message: 'Role must be either USER or AGENT' })
      })
      .optional()
      .default('USER')
  })
});

export const loginValidation = z.object({
  body: z.object({
    email: z
      .string({
        required_error: 'Email address is required',
        invalid_type_error: 'Email must be a string'
      })
      .email('Please provide a valid email address')
      .toLowerCase()
      .trim(),
    
    password: z
      .string({
        required_error: 'Password is required',
        invalid_type_error: 'Password must be a string'
      })
      .min(1, 'Password cannot be empty')
  })
});

export const changePasswordValidation = z.object({
  body: z.object({
    currentPassword: z
      .string({
        required_error: 'Current password is required',
        invalid_type_error: 'Current password must be a string'
      })
      .min(1, 'Current password cannot be empty'),
    
    newPassword: z
      .string({
        required_error: 'New password is required',
        invalid_type_error: 'New password must be a string'
      })
      .min(8, 'New password must be at least 8 characters long')
      .regex(passwordRegex, 'New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
  })
});

export const profileUpdateValidation = z.object({
  body: z.object({
    name: z
      .string()
      .min(2, 'Name must be at least 2 characters long')
      .max(50, 'Name cannot exceed 50 characters')
      .regex(/^[a-zA-Z\s\u0600-\u06FF]+$/, 'Name can only contain letters and spaces')
      .trim()
      .optional(),
    
    phone: z
      .string()
      .optional()
      .refine((val) => !val || phoneRegex.test(val), {
        message: 'Please provide a valid Afghan phone number'
      }),
    
    bio: z
      .string()
      .max(500, 'Bio cannot exceed 500 characters')
      .trim()
      .optional(),
    
    location: z
      .string()
      .max(100, 'Location cannot exceed 100 characters')
      .trim()
      .optional(),
    
    website: z
      .string()
      .url('Please provide a valid website URL')
      .optional()
      .or(z.literal(''))
  })
});

// Property validation schemas
export const propertyValidation = z.object({
  body: z.object({
    title: z
      .string({
        required_error: 'Property title is required',
        invalid_type_error: 'Title must be a string'
      })
      .min(5, 'Title must be at least 5 characters long')
      .max(200, 'Title cannot exceed 200 characters')
      .trim(),
    
    description: z
      .string({
        required_error: 'Property description is required',
        invalid_type_error: 'Description must be a string'
      })
      .min(20, 'Description must be at least 20 characters long')
      .max(2000, 'Description cannot exceed 2000 characters')
      .trim(),
    
    price: z
      .number({
        required_error: 'Property price is required',
        invalid_type_error: 'Price must be a number'
      })
      .min(1, 'Price must be greater than 0')
      .max(999999999, 'Price is too high'),
    
    currency: z
      .enum(['AFN', 'USD', 'EUR'], {
        errorMap: () => ({ message: 'Currency must be AFN, USD, or EUR' })
      })
      .default('AFN'),
    
    category: z
      .enum(['SALE', 'RENT', 'LEASE'], {
        errorMap: () => ({ message: 'Category must be SALE, RENT, or LEASE' })
      }),
    
    type: z
      .enum(['RESIDENTIAL', 'COMMERCIAL', 'LAND', 'APARTMENT', 'HOUSE', 'VILLA', 'OFFICE', 'SHOP', 'WAREHOUSE', 'FARM'], {
        errorMap: () => ({ message: 'Please select a valid property type' })
      }),
    
    province: z
      .string({
        required_error: 'Province is required',
        invalid_type_error: 'Province must be a string'
      })
      .min(2, 'Province name is required')
      .max(50, 'Province name is too long')
      .trim(),
    
    city: z
      .string({
        required_error: 'City is required',
        invalid_type_error: 'City must be a string'
      })
      .min(2, 'City name is required')
      .max(50, 'City name is too long')
      .trim(),
    
    district: z
      .string()
      .max(50, 'District name is too long')
      .trim()
      .optional(),
    
    address: z
      .string({
        required_error: 'Property address is required',
        invalid_type_error: 'Address must be a string'
      })
      .min(10, 'Address must be at least 10 characters long')
      .max(200, 'Address cannot exceed 200 characters')
      .trim(),
    
    latitude: z
      .number()
      .min(-90, 'Latitude must be between -90 and 90')
      .max(90, 'Latitude must be between -90 and 90')
      .optional(),
    
    longitude: z
      .number()
      .min(-180, 'Longitude must be between -180 and 180')
      .max(180, 'Longitude must be between -180 and 180')
      .optional(),
    
    bedrooms: z
      .number()
      .min(0, 'Bedrooms cannot be negative')
      .max(20, 'Too many bedrooms specified')
      .optional(),
    
    bathrooms: z
      .number()
      .min(0, 'Bathrooms cannot be negative')
      .max(20, 'Too many bathrooms specified')
      .optional(),
    
    area: z
      .number({
        required_error: 'Property area is required',
        invalid_type_error: 'Area must be a number'
      })
      .min(1, 'Area must be at least 1 square meter')
      .max(100000, 'Area is too large'),
    
    yearBuilt: z
      .number()
      .min(1800, 'Year built cannot be before 1800')
      .max(new Date().getFullYear() + 5, 'Year built cannot be more than 5 years in the future')
      .optional(),
    
    furnished: z.boolean().optional().default(false),
    parking: z.boolean().optional().default(false),
    garden: z.boolean().optional().default(false),
    balcony: z.boolean().optional().default(false),
    
    features: z
      .array(z.string().min(1, 'Feature cannot be empty'))
      .max(20, 'Too many features specified')
      .optional()
      .default([])
  })
});

// Message validation schema
export const messageValidation = z.object({
  body: z.object({
    receiverId: z
      .string({
        required_error: 'Message receiver is required',
        invalid_type_error: 'Receiver ID must be a string'
      })
      .regex(objectIdRegex, 'Invalid receiver ID format'),
    
    propertyId: z
      .string()
      .regex(objectIdRegex, 'Invalid property ID format')
      .optional(),
    
    subject: z
      .string()
      .max(200, 'Subject cannot exceed 200 characters')
      .trim()
      .optional(),
    
    content: z
      .string({
        required_error: 'Message content is required',
        invalid_type_error: 'Message content must be a string'
      })
      .min(1, 'Message content cannot be empty')
      .max(2000, 'Message cannot exceed 2000 characters')
      .trim()
  })
});

// ID parameter validation
export const idValidation = z.object({
  params: z.object({
    id: z
      .string({
        required_error: 'ID parameter is required',
        invalid_type_error: 'ID must be a string'
      })
      .regex(objectIdRegex, 'Invalid ID format')
  })
});

// Query validation for pagination and filtering
export const paginationValidation = z.object({
  query: z.object({
    page: z
      .string()
      .optional()
      .transform((val) => val ? parseInt(val, 10) : 1)
      .refine((val) => val > 0, 'Page must be greater than 0'),
    
    limit: z
      .string()
      .optional()
      .transform((val) => val ? parseInt(val, 10) : 20)
      .refine((val) => val > 0 && val <= 100, 'Limit must be between 1 and 100')
  })
});

// Validation middleware factory
export const validate = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const result = schema.parse({
        body: req.body,
        query: req.query,
        params: req.params
      });
      
      // Update request with validated data
      req.body = result.body || req.body;
      req.query = result.query || req.query;
      req.params = result.params || req.params;
      
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map((err) => {
          const path = err.path.slice(1).join('.');
          return `${path}: ${err.message}`;
        });
        
        return next(new AppError(
          `Validation failed: ${errorMessages.join(', ')}`,
          400,
          'VALIDATION_ERROR',
          { errors: error.errors }
        ));
      }
      
      next(new AppError('Invalid request data', 400));
    }
  };
};
