# 🏠 Real Estate Platform - Afghanistan

A comprehensive, modern real estate platform built specifically for the Afghan market with multi-language support (Pashto, Dari, English) and RTL text support.

## ✨ Features

### 🏘️ Core Functionality

- **Property Listings**: Complete CRUD operations for property management
- **Advanced Search**: Multi-criteria search with filters for location, price, type, amenities
- **User Management**: Registration, authentication, and profile management
- **Messaging System**: Direct communication between buyers and sellers
- **Favorites**: Save and manage favorite properties
- **File Upload**: Image and document upload with validation

### 🌐 Localization & Accessibility

- **Multi-language Support**: Pashto, Dari, English with seamless switching
- **RTL Support**: Right-to-left text support for Pashto and Dari
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Accessibility**: WCAG compliant design patterns

### 👥 User Roles

- **Buyers**: Browse, search, save properties, contact sellers
- **Sellers/Agents**: List properties, manage listings, respond to inquiries
- **Admins**: User management, property moderation, system analytics

### 🛡️ Security & Performance

- **JWT Authentication**: Secure token-based authentication
- **Role-based Access Control**: Granular permissions system
- **Input Validation**: Comprehensive server-side validation
- **Rate Limiting**: API protection against abuse
- **Image Optimization**: Automatic image compression and optimization

## 🚀 Tech Stack

### Frontend

- **React 19** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **React Router** for navigation
- **React Hook Form** for form handling
- **Framer Motion** for animations
- **React i18next** for internationalization
- **Axios** for HTTP requests

### Backend

- **Node.js** with Express.js
- **MongoDB** with Mongoose
- **JWT** for authentication
- **Multer** for file uploads
- **Nodemailer** for email services
- **Helmet** for security
- **Joi** for validation

## 🏗️ Architecture

### System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (React/TS)    │◄──►│   (Node.js)     │◄──►│   (MongoDB)     │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   File Storage  │    │   Email Service │    │   External APIs │
│   (Local/Cloud) │    │   (SMTP/Cloud)  │    │   (Maps, etc.)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher)
- **MongoDB** (v5.0 or higher)
- **npm** or **yarn**

### Installation

1. **Clone the repository:**

```bash
git clone <repository-url>
cd realestate
```

2. **Install dependencies:**

```bash
# Frontend dependencies
npm install

# Backend dependencies
cd backend
npm install
cd ..
```

3. **Environment Setup:**

```bash
# Copy environment files
cp .env.example .env
cp backend/.env.example backend/.env
```

4. **Configure Environment Variables:**

**Frontend (.env):**

```env
VITE_API_URL=http://localhost:3001/api
VITE_APP_NAME=Real Estate Platform
VITE_GOOGLE_MAPS_API_KEY=your-google-maps-api-key
```

**Backend (backend/.env):**

```env
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/realestate_db
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12
```

5. **Start Development Servers:**

**Terminal 1 - Backend:**

```bash
cd backend
npm run dev
```

**Terminal 2 - Frontend:**

```bash
npm run dev
```

6. **Access the Application:**

- Frontend: http://localhost:5173
- Backend API: http://localhost:3001
- API Health Check: http://localhost:3001/health

## 📁 Project Structure

```
realestate/
├── 📁 src/                     # Frontend source code
│   ├── 📁 components/          # React components
│   │   ├── 📁 common/          # Reusable components
│   │   ├── 📁 layout/          # Layout components
│   │   ├── 📁 property/        # Property components
│   │   ├── 📁 user/            # User components
│   │   └── 📁 search/          # Search components
│   ├── 📁 pages/               # Page components
│   │   ├── 📁 public/          # Public pages
│   │   ├── 📁 dashboard/       # Dashboard pages
│   │   └── 📁 admin/           # Admin pages
│   ├── 📁 contexts/            # React contexts
│   ├── 📁 hooks/               # Custom hooks
│   ├── 📁 services/            # API services
│   ├── 📁 utils/               # Utilities
│   ├── 📁 locales/             # Translations
│   └── 📁 styles/              # Styles
├── 📁 backend/                 # Backend source code
│   ├── 📁 src/
│   │   ├── 📁 controllers/     # Route controllers
│   │   ├── 📁 models/          # Database models
│   │   ├── 📁 routes/          # API routes
│   │   ├── 📁 middleware/      # Express middleware
│   │   ├── 📁 services/        # Business services
│   │   └── 📁 utils/           # Backend utilities
│   └── 📁 uploads/             # File uploads
├── 📁 docs/                    # Documentation
├── 📁 public/                  # Static assets
└── 📄 README.md                # This file
```

## 🔧 Available Scripts

### Frontend

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

### Backend

- `npm run dev` - Start development server with nodemon
- `npm run build` - Compile TypeScript
- `npm run start` - Start production server
- `npm run test` - Run tests
- `npm run lint` - Run ESLint

## 🌟 Features Overview

### User Management

- ✅ User registration and authentication
- ✅ Profile management with avatar upload
- ✅ Role-based access control (User, Agent, Admin)
- ✅ Password reset functionality
- ✅ Email verification

### Property Management

- ✅ Create, read, update, delete properties
- ✅ Multiple image upload with drag & drop
- ✅ Property status management (Pending, Approved, Rejected)
- ✅ Advanced search and filtering
- ✅ Property favorites system
- ✅ Property views tracking

### Messaging System

- ✅ Direct messaging between users
- ✅ Property inquiry system
- ✅ Message threads and conversations
- ✅ Real-time notifications
- ✅ Message read status

### Admin Panel

- ✅ User management and moderation
- ✅ Property approval/rejection
- ✅ System analytics and statistics
- ✅ Content management
- ✅ System configuration

### Search & Filtering

- ✅ Text-based search
- ✅ Location-based filtering
- ✅ Price range filtering
- ✅ Property type filtering
- ✅ Amenities filtering
- ✅ Advanced sorting options

## 🔌 API Documentation

The API follows RESTful conventions and includes the following endpoints:

### Authentication

```
POST   /api/auth/register      # User registration
POST   /api/auth/login         # User login
POST   /api/auth/logout        # User logout
POST   /api/auth/refresh       # Refresh access token
GET    /api/auth/profile       # Get user profile
PUT    /api/auth/profile       # Update user profile
PUT    /api/auth/change-password # Change password
POST   /api/auth/forgot-password # Request password reset
POST   /api/auth/reset-password  # Reset password
```

### Properties

```
GET    /api/properties         # Get all properties (with filters)
GET    /api/properties/:id     # Get property by ID
POST   /api/properties         # Create new property
PUT    /api/properties/:id     # Update property
DELETE /api/properties/:id     # Delete property
GET    /api/properties/search  # Search properties
GET    /api/properties/user/my-properties # Get user's properties
```

### Users

```
GET    /api/users/favorites    # Get user favorites
POST   /api/users/favorites/:id # Add to favorites
DELETE /api/users/favorites/:id # Remove from favorites
GET    /api/users/dashboard-stats # Get dashboard statistics
```

### Messages

```
GET    /api/messages           # Get user messages
GET    /api/messages/:id       # Get message by ID
POST   /api/messages           # Send message
PUT    /api/messages/:id/read  # Mark message as read
DELETE /api/messages/:id       # Delete message
GET    /api/messages/conversations # Get conversations
GET    /api/messages/conversations/:userId # Get conversation with user
```

### File Upload

```
POST   /api/upload/image       # Upload single image
POST   /api/upload/images      # Upload multiple images
```

### Admin (Admin only)

```
GET    /api/admin/users        # Get all users
PUT    /api/admin/users/:id    # Update user
DELETE /api/admin/users/:id    # Delete user
GET    /api/admin/properties   # Get all properties
PUT    /api/admin/properties/:id/approve # Approve property
PUT    /api/admin/properties/:id/reject  # Reject property
GET    /api/admin/analytics    # Get system analytics
```
