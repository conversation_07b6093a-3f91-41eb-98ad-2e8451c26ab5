import { Router } from 'express';
import {
  uploadSingle,
  uploadMultiple,
  uploadImage,
  uploadImages,
  deleteImage,
  handleUploadError
} from '../controllers/uploadController';
import { authenticate } from '../middleware/auth';

const router = Router();

// All upload routes require authentication
router.use(authenticate);

router.post('/image', uploadSingle, handleUploadError, uploadImage);
router.post('/images', uploadMultiple, handleUploadError, uploadImages);
router.delete('/image/:filename', deleteImage);

export default router;
