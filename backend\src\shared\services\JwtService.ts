/**
 * JWT Service
 * Handles JWT token generation, verification, and management
 */

import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { User } from '../../domain/entities/User';

export interface JwtPayload {
  sub: string; // user ID
  email: string;
  role: string;
  iat: number;
  exp: number;
  jti?: string; // JWT ID for token tracking
  scope?: string[]; // permissions/scopes
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenPayload {
  sub: string;
  type: 'refresh';
  jti: string;
  iat: number;
  exp: number;
}

export class JwtService {
  private readonly accessTokenSecret: string;
  private readonly refreshTokenSecret: string;
  private readonly issuer: string;
  private readonly audience: string;

  constructor() {
    this.accessTokenSecret = process.env.JWT_ACCESS_SECRET || 'your-access-secret';
    this.refreshTokenSecret = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret';
    this.issuer = process.env.JWT_ISSUER || 'real-estate-platform';
    this.audience = process.env.JWT_AUDIENCE || 'real-estate-users';
  }

  /**
   * Generate access token
   */
  async generateAccessToken(user: User, expiresIn: string = '15m'): Promise<string> {
    const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
      sub: user.id,
      email: user.email,
      role: user.role,
      jti: this.generateJti(),
      scope: this.getUserScopes(user)
    };

    return jwt.sign(payload, this.accessTokenSecret, {
      expiresIn,
      issuer: this.issuer,
      audience: this.audience,
      algorithm: 'HS256'
    });
  }

  /**
   * Generate refresh token
   */
  async generateRefreshToken(userId: string, expiresIn: string = '7d'): Promise<string> {
    const payload: Omit<RefreshTokenPayload, 'iat' | 'exp'> = {
      sub: userId,
      type: 'refresh',
      jti: this.generateJti()
    };

    return jwt.sign(payload, this.refreshTokenSecret, {
      expiresIn,
      issuer: this.issuer,
      audience: this.audience,
      algorithm: 'HS256'
    });
  }

  /**
   * Generate temporary token (for 2FA, email verification, etc.)
   */
  async generateTempToken(userId: string, expiresIn: string = '10m', purpose?: string): Promise<string> {
    const payload = {
      sub: userId,
      type: 'temp',
      purpose,
      jti: this.generateJti()
    };

    return jwt.sign(payload, this.accessTokenSecret, {
      expiresIn,
      issuer: this.issuer,
      audience: this.audience,
      algorithm: 'HS256'
    });
  }

  /**
   * Verify access token
   */
  async verifyAccessToken(token: string): Promise<JwtPayload> {
    try {
      const payload = jwt.verify(token, this.accessTokenSecret, {
        issuer: this.issuer,
        audience: this.audience,
        algorithms: ['HS256']
      }) as JwtPayload;

      return payload;
    } catch (error) {
      throw new Error('Invalid or expired access token');
    }
  }

  /**
   * Verify refresh token
   */
  async verifyRefreshToken(token: string): Promise<RefreshTokenPayload> {
    try {
      const payload = jwt.verify(token, this.refreshTokenSecret, {
        issuer: this.issuer,
        audience: this.audience,
        algorithms: ['HS256']
      }) as RefreshTokenPayload;

      if (payload.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      return payload;
    } catch (error) {
      throw new Error('Invalid or expired refresh token');
    }
  }

  /**
   * Verify temporary token
   */
  async verifyTempToken(token: string, expectedPurpose?: string): Promise<any> {
    try {
      const payload = jwt.verify(token, this.accessTokenSecret, {
        issuer: this.issuer,
        audience: this.audience,
        algorithms: ['HS256']
      }) as any;

      if (payload.type !== 'temp') {
        throw new Error('Invalid token type');
      }

      if (expectedPurpose && payload.purpose !== expectedPurpose) {
        throw new Error('Invalid token purpose');
      }

      return payload;
    } catch (error) {
      throw new Error('Invalid or expired temporary token');
    }
  }

  /**
   * Decode token without verification (for debugging)
   */
  decodeToken(token: string): any {
    return jwt.decode(token);
  }

  /**
   * Get token expiration time
   */
  getTokenExpiration(token: string): Date | null {
    const decoded = this.decodeToken(token);
    if (decoded && decoded.exp) {
      return new Date(decoded.exp * 1000);
    }
    return null;
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token: string): boolean {
    const expiration = this.getTokenExpiration(token);
    if (!expiration) return true;
    return expiration < new Date();
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshAccessToken(refreshToken: string, user: User): Promise<TokenPair> {
    // Verify refresh token
    const refreshPayload = await this.verifyRefreshToken(refreshToken);
    
    if (refreshPayload.sub !== user.id) {
      throw new Error('Token user mismatch');
    }

    // Generate new tokens
    const accessToken = await this.generateAccessToken(user);
    const newRefreshToken = await this.generateRefreshToken(user.id);

    return {
      accessToken,
      refreshToken: newRefreshToken,
      expiresIn: 15 * 60 // 15 minutes in seconds
    };
  }

  /**
   * Blacklist token (for logout)
   */
  async blacklistToken(token: string): Promise<void> {
    const decoded = this.decodeToken(token);
    if (decoded && decoded.jti) {
      // Store JTI in blacklist (Redis, database, etc.)
      // Implementation depends on your storage choice
      console.log(`Blacklisting token: ${decoded.jti}`);
    }
  }

  /**
   * Check if token is blacklisted
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    const decoded = this.decodeToken(token);
    if (decoded && decoded.jti) {
      // Check if JTI is in blacklist
      // Implementation depends on your storage choice
      return false; // Placeholder
    }
    return false;
  }

  /**
   * Generate JWT ID for token tracking
   */
  private generateJti(): string {
    return crypto.randomBytes(16).toString('hex');
  }

  /**
   * Get user scopes/permissions
   */
  private getUserScopes(user: User): string[] {
    const scopes: string[] = ['read:profile', 'update:profile'];

    switch (user.role) {
      case 'USER':
        scopes.push('create:property', 'read:properties', 'update:own_property');
        break;
      case 'AGENT':
        scopes.push('create:property', 'read:properties', 'update:property', 'manage:clients');
        break;
      case 'ADMIN':
        scopes.push('read:all', 'create:all', 'update:all', 'delete:property', 'manage:users');
        break;
      case 'SUPER_ADMIN':
        scopes.push('*'); // All permissions
        break;
    }

    return scopes;
  }

  /**
   * Create token for API access
   */
  async generateApiToken(userId: string, scopes: string[], expiresIn: string = '1y'): Promise<string> {
    const payload = {
      sub: userId,
      type: 'api',
      scope: scopes,
      jti: this.generateJti()
    };

    return jwt.sign(payload, this.accessTokenSecret, {
      expiresIn,
      issuer: this.issuer,
      audience: this.audience,
      algorithm: 'HS256'
    });
  }

  /**
   * Verify API token
   */
  async verifyApiToken(token: string): Promise<any> {
    try {
      const payload = jwt.verify(token, this.accessTokenSecret, {
        issuer: this.issuer,
        audience: this.audience,
        algorithms: ['HS256']
      }) as any;

      if (payload.type !== 'api') {
        throw new Error('Invalid token type');
      }

      return payload;
    } catch (error) {
      throw new Error('Invalid or expired API token');
    }
  }
}
