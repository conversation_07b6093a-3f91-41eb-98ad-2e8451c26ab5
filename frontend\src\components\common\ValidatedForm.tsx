import React from 'react';
import { motion } from 'framer-motion';
import { UseFormReturn, FieldValues, Controller } from 'react-hook-form';
import ValidatedInput from './ValidatedInput';
import Button from './Button';

interface ValidatedFormProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  onSubmit: (data: T) => void | Promise<void>;
  children?: React.ReactNode;
  className?: string;
  submitText?: string;
  isSubmitting?: boolean;
  showResetButton?: boolean;
  resetText?: string;
  formTitle?: string;
  formDescription?: string;
}

function ValidatedForm<T extends FieldValues>({
  form,
  onSubmit,
  children,
  className = '',
  submitText = 'Submit',
  isSubmitting = false,
  showResetButton = false,
  resetText = 'Reset',
  formTitle,
  formDescription,
}: ValidatedFormProps<T>) {
  const { handleSubmit, reset, formState: { errors, isValid, isDirty } } = form;

  const handleFormSubmit = handleSubmit(
    async (data: T) => {
      try {
        await onSubmit(data);
      } catch (error) {
        console.error('Form submission error:', error);
      }
    },
    (errors) => {
      console.log('Form validation errors:', errors);
    }
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={`w-full max-w-md mx-auto ${className}`}
    >
      {/* Form Header */}
      {(formTitle || formDescription) && (
        <div className="mb-6 text-center">
          {formTitle && (
            <motion.h2
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-2xl font-bold text-gray-900 mb-2"
            >
              {formTitle}
            </motion.h2>
          )}
          {formDescription && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-gray-600"
            >
              {formDescription}
            </motion.p>
          )}
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleFormSubmit} className="space-y-4">
        {children}

        {/* Form Actions */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col sm:flex-row gap-3 pt-4"
        >
          <Button
            type="submit"
            variant="primary"
            size="lg"
            fullWidth
            isLoading={isSubmitting}
            disabled={isSubmitting}
            className="order-2 sm:order-1"
          >
            {submitText}
          </Button>

          {showResetButton && (
            <Button
              type="button"
              variant="outline"
              size="lg"
              onClick={() => reset()}
              disabled={isSubmitting || !isDirty}
              className="order-1 sm:order-2 sm:w-auto"
            >
              {resetText}
            </Button>
          )}
        </motion.div>
      </form>
    </motion.div>
  );
}

// Form Field Component
interface FormFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  name: keyof T;
  label?: string;
  placeholder?: string;
  type?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  showPasswordToggle?: boolean;
  required?: boolean;
  disabled?: boolean;
  variant?: 'default' | 'filled' | 'outlined';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  validate?: (value: any) => boolean | string;
}

export function FormField<T extends FieldValues>({
  form,
  name,
  label,
  placeholder,
  type = 'text',
  helperText,
  leftIcon,
  rightIcon,
  showPasswordToggle = false,
  required = false,
  disabled = false,
  variant = 'outlined',
  size = 'md',
  fullWidth = true,
  validate,
}: FormFieldProps<T>) {
  const { control, formState: { errors }, trigger, watch } = form;
  const fieldValue = watch(name);
  const fieldError = errors[name]?.message as string | undefined;

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: required ? `${label || name} is required` : false,
        validate: validate,
      }}
      render={({ field }) => (
        <ValidatedInput
          {...field}
          label={label}
          placeholder={placeholder}
          type={type}
          error={fieldError}
          helperText={!fieldError ? helperText : undefined}
          isValid={!fieldError && fieldValue && fieldValue.length > 0}
          leftIcon={leftIcon}
          rightIcon={rightIcon}
          showPasswordToggle={showPasswordToggle}
          required={required}
          disabled={disabled}
          variant={variant}
          size={size}
          fullWidth={fullWidth}
          onBlur={async () => {
            field.onBlur();
            await trigger(name);
          }}
        />
      )}
    />
  );
}

// Textarea Field Component
interface TextareaFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  name: keyof T;
  label?: string;
  placeholder?: string;
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
  rows?: number;
  maxLength?: number;
  showCharCount?: boolean;
}

export function TextareaField<T extends FieldValues>({
  form,
  name,
  label,
  placeholder,
  helperText,
  required = false,
  disabled = false,
  rows = 4,
  maxLength,
  showCharCount = false,
}: TextareaFieldProps<T>) {
  const { control, formState: { errors }, trigger, watch } = form;
  const fieldValue = watch(name) || '';
  const fieldError = errors[name]?.message as string | undefined;
  const charCount = fieldValue.length;

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: required ? `${label || name} is required` : false,
        maxLength: maxLength ? {
          value: maxLength,
          message: `${label || name} cannot exceed ${maxLength} characters`
        } : undefined,
      }}
      render={({ field }) => (
        <div className="space-y-1">
          {label && (
            <label className={`
              block text-sm font-medium
              ${fieldError ? 'text-red-700' : 'text-gray-700'}
              ${disabled ? 'opacity-50' : ''}
            `}>
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          
          <textarea
            {...field}
            placeholder={placeholder}
            disabled={disabled}
            rows={rows}
            maxLength={maxLength}
            className={`
              w-full px-4 py-3 text-base
              border rounded-lg
              transition-all duration-200
              focus:outline-none focus:ring-2 focus:ring-opacity-50
              placeholder-gray-400 resize-vertical
              ${fieldError
                ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed bg-gray-50' : 'bg-white'}
            `}
            onBlur={async () => {
              field.onBlur();
              await trigger(name);
            }}
          />
          
          <div className="flex justify-between items-center">
            <div className={`text-sm ${fieldError ? 'text-red-600' : 'text-gray-500'}`}>
              {fieldError || helperText}
            </div>
            
            {showCharCount && maxLength && (
              <div className={`text-sm ${
                charCount > maxLength * 0.9 ? 'text-orange-500' : 'text-gray-400'
              }`}>
                {charCount}/{maxLength}
              </div>
            )}
          </div>
        </div>
      )}
    />
  );
}

// Select Field Component
interface SelectFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  name: keyof T;
  label?: string;
  placeholder?: string;
  options: { value: string; label: string }[];
  helperText?: string;
  required?: boolean;
  disabled?: boolean;
}

export function SelectField<T extends FieldValues>({
  form,
  name,
  label,
  placeholder = 'Select an option',
  options,
  helperText,
  required = false,
  disabled = false,
}: SelectFieldProps<T>) {
  const { control, formState: { errors }, trigger } = form;
  const fieldError = errors[name]?.message as string | undefined;

  return (
    <Controller
      name={name}
      control={control}
      rules={{
        required: required ? `${label || name} is required` : false,
      }}
      render={({ field }) => (
        <div className="space-y-1">
          {label && (
            <label className={`
              block text-sm font-medium
              ${fieldError ? 'text-red-700' : 'text-gray-700'}
              ${disabled ? 'opacity-50' : ''}
            `}>
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          
          <select
            {...field}
            disabled={disabled}
            className={`
              w-full px-4 py-3 text-base
              border rounded-lg
              transition-all duration-200
              focus:outline-none focus:ring-2 focus:ring-opacity-50
              ${fieldError
                ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed bg-gray-50' : 'bg-white'}
            `}
            onBlur={async () => {
              field.onBlur();
              await trigger(name);
            }}
          >
            <option value="">{placeholder}</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          
          {(fieldError || helperText) && (
            <div className={`text-sm ${fieldError ? 'text-red-600' : 'text-gray-500'}`}>
              {fieldError || helperText}
            </div>
          )}
        </div>
      )}
    />
  );
}

export default ValidatedForm;
