import { Router } from 'express';
import {
  getMessages,
  getMessage,
  sendMessage,
  markAsRead,
  deleteMessage,
  getConversations,
  getConversationMessages
} from '../controllers/messageController';
import { authenticate } from '../middleware/auth';

const router = Router();

// All message routes require authentication
router.use(authenticate);

router.get('/conversations', getConversations);
router.get('/conversations/:otherUserId', getConversationMessages);
router.get('/', getMessages);
router.post('/', sendMessage);
router.get('/:id', getMessage);
router.put('/:id/read', markAsRead);
router.delete('/:id', deleteMessage);

export default router;
