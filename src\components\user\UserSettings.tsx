import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  UserIcon,
  BellIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  EyeIcon,
  KeyIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import Button from '../common/Button';
import Input from '../common/Input';
import Modal from '../common/Modal';
import { useLanguage } from '../../contexts/LanguageContext';

interface UserSettingsProps {
  user: {
    id: string;
    name: string;
    email: string;
    phone: string;
    avatar: string;
    emailVerified: boolean;
    phoneVerified: boolean;
    twoFactorEnabled: boolean;
  };
  onUpdateSettings: (settings: any) => void;
  onDeleteAccount: () => void;
  className?: string;
}

const UserSettings: React.FC<UserSettingsProps> = ({
  user,
  onUpdateSettings,
  onDeleteAccount,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState('profile');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      sms: false,
      push: true,
      marketing: false
    },
    privacy: {
      profileVisible: true,
      showEmail: false,
      showPhone: false,
      allowMessages: true
    },
    security: {
      twoFactor: user.twoFactorEnabled,
      loginAlerts: true,
      sessionTimeout: 30
    }
  });
  const { t, isRTL, changeLanguage, currentLanguage } = useLanguage();
  
  const tabs = [
    { id: 'profile', label: t('settings.profile'), icon: UserIcon },
    { id: 'notifications', label: t('settings.notifications'), icon: BellIcon },
    { id: 'privacy', label: t('settings.privacy'), icon: EyeIcon },
    { id: 'security', label: t('settings.security'), icon: ShieldCheckIcon },
    { id: 'language', label: t('settings.language'), icon: GlobeAltIcon }
  ];
  
  const languages = [
    { code: 'en', name: 'English', nativeName: 'English' },
    { code: 'ps', name: 'Pashto', nativeName: 'پښتو' },
    { code: 'fa', name: 'Dari', nativeName: 'فارسی' }
  ];
  
  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [key]: value
      }
    }));
  };
  
  const handleSaveSettings = () => {
    onUpdateSettings(settings);
  };
  
  const renderProfileTab = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <img
          src={user.avatar}
          alt={user.name}
          className="w-20 h-20 rounded-full object-cover"
        />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
          <p className="text-gray-600">{user.email}</p>
          <Button variant="outline" size="sm" className="mt-2">
            {t('settings.changeAvatar')}
          </Button>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Input
          label={t('settings.fullName')}
          defaultValue={user.name}
        />
        <Input
          label={t('settings.phoneNumber')}
          defaultValue={user.phone}
          rightIcon={user.phoneVerified ? <CheckCircleIcon className="w-5 h-5 text-green-500" /> : undefined}
        />
      </div>
      
      <Input
        label={t('settings.emailAddress')}
        defaultValue={user.email}
        rightIcon={user.emailVerified ? <CheckCircleIcon className="w-5 h-5 text-green-500" /> : undefined}
      />
      
      <div className="pt-4 border-t border-gray-200">
        <Button onClick={handleSaveSettings}>
          {t('settings.saveChanges')}
        </Button>
      </div>
    </div>
  );
  
  const renderNotificationsTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('settings.notificationPreferences')}
        </h3>
        <div className="space-y-4">
          {Object.entries(settings.notifications).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">
                  {t(`settings.${key}Notifications`)}
                </p>
                <p className="text-sm text-gray-600">
                  {t(`settings.${key}NotificationsDesc`)}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
  
  const renderPrivacyTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('settings.privacySettings')}
        </h3>
        <div className="space-y-4">
          {Object.entries(settings.privacy).map(([key, value]) => (
            <div key={key} className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">
                  {t(`settings.${key}`)}
                </p>
                <p className="text-sm text-gray-600">
                  {t(`settings.${key}Desc`)}
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={value}
                  onChange={(e) => handleSettingChange('privacy', key, e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
  
  const renderSecurityTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {t('settings.securitySettings')}
        </h3>
        
        <div className="space-y-6">
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-gray-900">
                {t('settings.twoFactorAuth')}
              </h4>
              <Button
                variant={settings.security.twoFactor ? 'danger' : 'primary'}
                size="sm"
                onClick={() => handleSettingChange('security', 'twoFactor', !settings.security.twoFactor)}
              >
                {settings.security.twoFactor ? t('settings.disable') : t('settings.enable')}
              </Button>
            </div>
            <p className="text-sm text-gray-600">
              {t('settings.twoFactorAuthDesc')}
            </p>
          </div>
          
          <div className="p-4 border border-gray-200 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2">
              {t('settings.changePassword')}
            </h4>
            <p className="text-sm text-gray-600 mb-4">
              {t('settings.changePasswordDesc')}
            </p>
            <Button variant="outline" size="sm" icon={<KeyIcon className="w-4 h-4" />}>
              {t('settings.changePassword')}
            </Button>
          </div>
        </div>
      </div>
      
      <div className="pt-6 border-t border-gray-200">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <ExclamationTriangleIcon className="w-6 h-6 text-red-600 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h4 className="font-medium text-red-900 mb-2">
                {t('settings.deleteAccount')}
              </h4>
              <p className="text-sm text-red-700 mb-4">
                {t('settings.deleteAccountDesc')}
              </p>
              <Button
                variant="danger"
                size="sm"
                onClick={() => setShowDeleteModal(true)}
                icon={<TrashIcon className="w-4 h-4" />}
              >
                {t('settings.deleteAccount')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
  
  // Remove language tab from the tabs array
  // Remove renderLanguageTab function
  // Remove language case from renderTabContent switch statement
  
  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return renderProfileTab();
      case 'notifications':
        return renderNotificationsTab();
      case 'privacy':
        return renderPrivacyTab();
      case 'security':
        return renderSecurityTab();
      case 'language':
        return renderLanguageTab();
      default:
        return renderProfileTab();
    }
  };
  
  return (
    <>
      <div className={`bg-white rounded-2xl shadow-xl overflow-hidden ${className}`}>
        <div className="flex flex-col lg:flex-row">
          {/* Sidebar */}
          <div className="lg:w-64 bg-gray-50 border-r border-gray-200">
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">
                {t('settings.title')}
              </h2>
              <nav className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-600 hover:bg-gray-100'
                    } ${isRTL ? 'space-x-reverse' : ''}`}
                  >
                    <tab.icon className="w-5 h-5" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>
          
          {/* Content */}
          <div className="flex-1 p-8">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
            >
              {renderTabContent()}
            </motion.div>
          </div>
        </div>
      </div>
      
      {/* Delete Account Modal */}
      <Modal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title={t('settings.deleteAccount')}
        size="md"
      >
        <div className="space-y-4">
          <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg">
            <ExclamationTriangleIcon className="w-6 h-6 text-red-600 flex-shrink-0" />
            <p className="text-sm text-red-700">
              {t('settings.deleteAccountWarning')}
            </p>
          </div>
          
          <p className="text-gray-600">
            {t('settings.deleteAccountConfirmation')}
          </p>
          
          <Input
            label={t('settings.typeDeleteToConfirm')}
            placeholder="DELETE"
          />
        </div>
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="outline"
            onClick={() => setShowDeleteModal(false)}
          >
            {t('common.cancel')}
          </Button>
          <Button
            variant="danger"
            onClick={onDeleteAccount}
          >
            {t('settings.deleteAccount')}
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default UserSettings;
