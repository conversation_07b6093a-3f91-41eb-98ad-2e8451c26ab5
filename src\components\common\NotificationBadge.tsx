import React from 'react';

interface NotificationBadgeProps {
  count: number;
  className?: string;
  maxCount?: number;
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({ 
  count, 
  className = '', 
  maxCount = 99 
}) => {
  if (count <= 0) return null;

  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  return (
    <span className={`
      absolute -top-2 -right-2 
      bg-red-500 text-white 
      text-xs font-bold 
      rounded-full 
      min-w-[20px] h-5 
      flex items-center justify-center 
      px-1.5
      animate-pulse
      ${className}
    `}>
      {displayCount}
    </span>
  );
};

export default NotificationBadge;
