import { Request, Response, NextFunction } from 'express';
import { Message, User, Property } from '../models';
import { AppError, catchAsync } from '../middleware/errorHandler';
import { MessageCreateInput } from '../types/api';

export const getMessages = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;
  const { page = 1, limit = 20, type = 'received' } = req.query as any;

  const filter = type === 'sent' 
    ? { sender: userId }
    : { receiver: userId };

  const skip = (Number(page) - 1) * Number(limit);

  const [messages, total] = await Promise.all([
    Message.find(filter)
      .populate('sender', 'name email avatar')
      .populate('receiver', 'name email avatar')
      .populate('property', 'title price category images')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit)),
    Message.countDocuments(filter)
  ]);

  const totalPages = Math.ceil(total / Number(limit));

  res.json({
    success: true,
    data: messages,
    meta: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    }
  });
});

export const getMessage = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user._id;

  const message = await Message.findById(id)
    .populate('sender', 'name email avatar')
    .populate('receiver', 'name email avatar')
    .populate('property', 'title price category images');

  if (!message) {
    return next(new AppError('Message not found', 404));
  }

  // Check if user is sender or receiver
  if (message.sender._id.toString() !== userId.toString() && 
      message.receiver._id.toString() !== userId.toString()) {
    return next(new AppError('Not authorized to view this message', 403));
  }

  // Mark as read if user is the receiver
  if (message.receiver._id.toString() === userId.toString() && !message.isRead) {
    message.isRead = true;
    message.readAt = new Date();
    await message.save();
  }

  res.json({
    success: true,
    data: message
  });
});

export const sendMessage = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const senderId = req.user._id;
  const { receiverId, propertyId, subject, content }: MessageCreateInput = req.body;

  // Validate receiver exists
  const receiver = await User.findById(receiverId);
  if (!receiver) {
    return next(new AppError('Receiver not found', 404));
  }

  // Validate property exists if provided
  if (propertyId) {
    const property = await Property.findById(propertyId);
    if (!property) {
      return next(new AppError('Property not found', 404));
    }
  }

  // Create message
  const message = new Message({
    sender: senderId,
    receiver: receiverId,
    property: propertyId,
    subject,
    content
  });

  await message.save();
  await message.populate([
    { path: 'sender', select: 'name email avatar' },
    { path: 'receiver', select: 'name email avatar' },
    { path: 'property', select: 'title price category images' }
  ]);

  res.status(201).json({
    success: true,
    data: message,
    message: 'Message sent successfully'
  });
});

export const markAsRead = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user._id;

  const message = await Message.findById(id);

  if (!message) {
    return next(new AppError('Message not found', 404));
  }

  // Check if user is the receiver
  if (message.receiver.toString() !== userId.toString()) {
    return next(new AppError('Not authorized to mark this message as read', 403));
  }

  if (!message.isRead) {
    message.isRead = true;
    message.readAt = new Date();
    await message.save();
  }

  res.json({
    success: true,
    message: 'Message marked as read'
  });
});

export const deleteMessage = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const { id } = req.params;
  const userId = req.user._id;

  const message = await Message.findById(id);

  if (!message) {
    return next(new AppError('Message not found', 404));
  }

  // Check if user is sender or receiver
  if (message.sender.toString() !== userId.toString() && 
      message.receiver.toString() !== userId.toString()) {
    return next(new AppError('Not authorized to delete this message', 403));
  }

  await Message.findByIdAndDelete(id);

  res.json({
    success: true,
    message: 'Message deleted successfully'
  });
});

export const getConversations = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;

  // Get unique conversations (users the current user has messaged with)
  const conversations = await Message.aggregate([
    {
      $match: {
        $or: [
          { sender: userId },
          { receiver: userId }
        ]
      }
    },
    {
      $sort: { createdAt: -1 }
    },
    {
      $group: {
        _id: {
          $cond: [
            { $eq: ['$sender', userId] },
            '$receiver',
            '$sender'
          ]
        },
        lastMessage: { $first: '$$ROOT' },
        unreadCount: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$receiver', userId] },
                  { $eq: ['$isRead', false] }
                ]
              },
              1,
              0
            ]
          }
        }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        user: {
          _id: '$user._id',
          name: '$user.name',
          email: '$user.email',
          avatar: '$user.avatar'
        },
        lastMessage: 1,
        unreadCount: 1
      }
    },
    {
      $sort: { 'lastMessage.createdAt': -1 }
    }
  ]);

  res.json({
    success: true,
    data: conversations
  });
});

export const getConversationMessages = catchAsync(async (req: Request, res: Response, next: NextFunction) => {
  const userId = req.user._id;
  const { otherUserId } = req.params;
  const { page = 1, limit = 50 } = req.query as any;

  const skip = (Number(page) - 1) * Number(limit);

  const [messages, total] = await Promise.all([
    Message.find({
      $or: [
        { sender: userId, receiver: otherUserId },
        { sender: otherUserId, receiver: userId }
      ]
    })
      .populate('sender', 'name email avatar')
      .populate('receiver', 'name email avatar')
      .populate('property', 'title price category images')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(Number(limit)),
    Message.countDocuments({
      $or: [
        { sender: userId, receiver: otherUserId },
        { sender: otherUserId, receiver: userId }
      ]
    })
  ]);

  // Mark messages as read
  await Message.updateMany(
    {
      sender: otherUserId,
      receiver: userId,
      isRead: false
    },
    {
      isRead: true,
      readAt: new Date()
    }
  );

  const totalPages = Math.ceil(total / Number(limit));

  res.json({
    success: true,
    data: messages.reverse(), // Reverse to show oldest first
    meta: {
      page: Number(page),
      limit: Number(limit),
      total,
      totalPages,
      hasNext: Number(page) < totalPages,
      hasPrev: Number(page) > 1
    }
  });
});
