import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { 
  ChevronLeftIcon,
  ChevronRightIcon,
  HeartIcon,
  ShareIcon,
  MapPinIcon,
  HomeIcon,
  BuildingOfficeIcon,
  CalendarIcon,
  TruckIcon,
  SparklesIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  EyeIcon,
  CheckCircleIcon,
  XMarkIcon,
  PhotoIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon, StarIcon } from '@heroicons/react/24/solid';
import ContactModal from '../../components/common/ContactModal';

interface PropertyDetail {
  id: number;
  title: string;
  location: string;
  price: string;
  images: string[];
  beds: number;
  baths: number;
  area: string;
  type: string;
  category: string;
  featured: boolean;
  views: number;
  saved: boolean;
  yearBuilt: number;
  parking: number;
  garden: boolean;
  description: string;
  features: string[];
  owner: {
    name: string;
    photo: string;
    phone: string;
    email: string;
    verified: boolean;
    rating: number;
    totalProperties: number;
  };
  coordinates: {
    lat: number;
    lng: number;
  };
  nearbyPlaces: {
    schools: string[];
    hospitals: string[];
    shopping: string[];
    transport: string[];
  };
}

const PropertyDetailPage: React.FC = () => {
  const { id } = useParams();
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);
  const [saved, setSaved] = useState(false);
  const [showContactModal, setShowContactModal] = useState(false);

  // Mock data - in real app this would come from API
  const property: PropertyDetail = {
    id: parseInt(id || '1'),
    title: "Modern Villa in Wazir Akbar Khan",
    location: "Wazir Akbar Khan, Kabul, Afghanistan",
    price: "$250,000",
    images: [
      "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      "https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      "https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      "https://images.unsplash.com/photo-1600585154340-be6161a56a0c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      "https://images.unsplash.com/photo-1600607687644-aac4c3eac7f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
      "https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
    ],
    beds: 4,
    baths: 3,
    area: "2,500 sq ft",
    type: "Villa",
    category: "For Sale",
    featured: true,
    views: 1247,
    saved: false,
    yearBuilt: 2020,
    parking: 2,
    garden: true,
    description: "This stunning modern villa offers luxurious living in the heart of Wazir Akbar Khan, one of Kabul's most prestigious neighborhoods. The property features contemporary architecture with high-end finishes throughout, spacious rooms with abundant natural light, and a beautiful private garden. Perfect for families seeking comfort, security, and convenience in an prime location.",
    features: [
      "Central Air Conditioning",
      "Modern Kitchen",
      "Master Suite",
      "Private Garden",
      "Security System",
      "Garage Parking",
      "Balcony",
      "Hardwood Floors",
      "Walk-in Closets",
      "High Ceilings"
    ],
    owner: {
      name: "Ahmad Rezai",
      photo: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80",
      phone: "+93 70 123 4567",
      email: "<EMAIL>",
      verified: true,
      rating: 4.8,
      totalProperties: 12
    },
    coordinates: {
      lat: 34.5553,
      lng: 69.2075
    },
    nearbyPlaces: {
      schools: ["International School of Kabul", "Kabul University", "American University"],
      hospitals: ["Wazir Akbar Khan Hospital", "French Medical Institute", "Emergency Hospital"],
      shopping: ["Kabul City Center", "Chicken Street Bazaar", "Shar-e-Naw Market"],
      transport: ["Kabul Airport (15 min)", "Bus Station (10 min)", "Taxi Stand (5 min)"]
    }
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === property.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? property.images.length - 1 : prev - 1
    );
  };

  const toggleSaved = () => {
    setSaved(!saved);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumbs */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-gray-500 hover:text-primary-500 transition-colors duration-300">
              Home
            </Link>
            <ChevronRightIcon className="w-4 h-4 text-gray-400" />
            <Link to="/listings" className="text-gray-500 hover:text-primary-500 transition-colors duration-300">
              Properties
            </Link>
            <ChevronRightIcon className="w-4 h-4 text-gray-400" />
            <span className="text-gray-900 font-medium">{property.type}</span>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Property Header */}
        <div className="bg-white rounded-xl shadow-soft p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
            <div className="flex-1 mb-6 lg:mb-0">
              <div className="flex items-center space-x-3 mb-3">
                {property.featured && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-accent-100 text-accent-800">
                    Featured
                  </span>
                )}
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                  {property.category}
                </span>
              </div>
              
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-3">
                {property.title}
              </h1>
              
              <div className="flex items-center text-gray-600 mb-4">
                <MapPinIcon className="w-5 h-5 mr-2" />
                <span className="text-lg">{property.location}</span>
              </div>
              
              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <div className="flex items-center">
                  <EyeIcon className="w-4 h-4 mr-1" />
                  <span>{property.views} views</span>
                </div>
                <div className="flex items-center">
                  <CalendarIcon className="w-4 h-4 mr-1" />
                  <span>Listed 2 days ago</span>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col items-end">
              <div className="text-3xl lg:text-4xl font-bold text-primary-500 mb-4">
                {property.price}
              </div>
              
              <div className="flex items-center space-x-3">
                <button
                  onClick={toggleSaved}
                  className={`p-3 rounded-full border-2 transition-all duration-300 ${
                    saved 
                      ? 'border-red-500 bg-red-50 text-red-500' 
                      : 'border-gray-300 hover:border-red-500 hover:bg-red-50 hover:text-red-500'
                  }`}
                >
                  {saved ? (
                    <HeartSolidIcon className="w-6 h-6" />
                  ) : (
                    <HeartIcon className="w-6 h-6" />
                  )}
                </button>
                
                <button className="p-3 rounded-full border-2 border-gray-300 hover:border-primary-500 hover:bg-primary-50 hover:text-primary-500 transition-all duration-300">
                  <ShareIcon className="w-6 h-6" />
                </button>
                
                <button
                  onClick={() => setShowContactModal(true)}
                  className="btn-primary flex items-center space-x-2"
                >
                  <ChatBubbleLeftRightIcon className="w-5 h-5" />
                  <span>Contact Owner</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Image Gallery */}
        <div className="bg-white rounded-xl shadow-soft overflow-hidden mb-8">
          <div className="relative">
            <div className="aspect-w-16 aspect-h-9 lg:aspect-h-6">
              <img 
                src={property.images[currentImageIndex]} 
                alt={property.title}
                className="w-full h-96 lg:h-[500px] object-cover"
              />
            </div>
            
            {/* Navigation Arrows */}
            <button
              onClick={prevImage}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-all duration-300 shadow-lg"
            >
              <ChevronLeftIcon className="w-6 h-6 text-gray-700" />
            </button>
            
            <button
              onClick={nextImage}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white transition-all duration-300 shadow-lg"
            >
              <ChevronRightIcon className="w-6 h-6 text-gray-700" />
            </button>
            
            {/* Image Counter */}
            <div className="absolute bottom-4 right-4 bg-black/50 text-white px-3 py-1 rounded-lg backdrop-blur-sm">
              {currentImageIndex + 1} / {property.images.length}
            </div>
            
            {/* View All Photos Button */}
            <button
              onClick={() => setShowImageModal(true)}
              className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm text-gray-700 px-4 py-2 rounded-lg hover:bg-white transition-all duration-300 flex items-center space-x-2"
            >
              <PhotoIcon className="w-5 h-5" />
              <span>View All Photos</span>
            </button>
          </div>
          
          {/* Thumbnail Gallery */}
          <div className="p-4">
            <div className="flex space-x-2 overflow-x-auto">
              {property.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentImageIndex(index)}
                  className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                    index === currentImageIndex 
                      ? 'border-primary-500' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <img 
                    src={image} 
                    alt={`${property.title} ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Property Details */}
          <div className="lg:col-span-2 space-y-8">
            {/* Property Overview */}
            <div className="bg-white rounded-xl shadow-soft p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Property Overview</h2>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <HomeIcon className="w-8 h-8 text-primary-500" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{property.beds}</div>
                  <div className="text-sm text-gray-600">Bedrooms</div>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <BuildingOfficeIcon className="w-8 h-8 text-secondary-500" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{property.baths}</div>
                  <div className="text-sm text-gray-600">Bathrooms</div>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <SparklesIcon className="w-8 h-8 text-accent-500" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{property.area}</div>
                  <div className="text-sm text-gray-600">Total Area</div>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <CalendarIcon className="w-8 h-8 text-primary-500" />
                  </div>
                  <div className="text-2xl font-bold text-gray-900">{property.yearBuilt}</div>
                  <div className="text-sm text-gray-600">Year Built</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3">
                  <TruckIcon className="w-6 h-6 text-gray-400" />
                  <span className="text-gray-700">
                    <span className="font-semibold">{property.parking}</span> Parking Spaces
                  </span>
                </div>

                <div className="flex items-center space-x-3">
                  <SparklesIcon className="w-6 h-6 text-gray-400" />
                  <span className="text-gray-700">
                    {property.garden ? 'Private Garden' : 'No Garden'}
                  </span>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="bg-white rounded-xl shadow-soft p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Description</h2>
              <p className="text-gray-700 leading-relaxed text-lg">
                {property.description}
              </p>
            </div>

            {/* Features & Amenities */}
            <div className="bg-white rounded-xl shadow-soft p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Features & Amenities</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {property.features.map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircleIcon className="w-5 h-5 text-secondary-500 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Location & Nearby */}
            <div className="bg-white rounded-xl shadow-soft p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Location & Nearby</h2>

              {/* Map Placeholder */}
              <div className="bg-gray-200 rounded-lg h-64 mb-6 flex items-center justify-center">
                <div className="text-center">
                  <MapPinIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                  <p className="text-gray-500">Interactive Map</p>
                  <p className="text-sm text-gray-400">Lat: {property.coordinates.lat}, Lng: {property.coordinates.lng}</p>
                </div>
              </div>

              {/* Nearby Places */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">🏫 Schools</h4>
                  <ul className="space-y-2">
                    {property.nearbyPlaces.schools.map((school, index) => (
                      <li key={index} className="text-gray-600 text-sm">{school}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">🏥 Healthcare</h4>
                  <ul className="space-y-2">
                    {property.nearbyPlaces.hospitals.map((hospital, index) => (
                      <li key={index} className="text-gray-600 text-sm">{hospital}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">🛍️ Shopping</h4>
                  <ul className="space-y-2">
                    {property.nearbyPlaces.shopping.map((shop, index) => (
                      <li key={index} className="text-gray-600 text-sm">{shop}</li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">🚌 Transport</h4>
                  <ul className="space-y-2">
                    {property.nearbyPlaces.transport.map((transport, index) => (
                      <li key={index} className="text-gray-600 text-sm">{transport}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Owner Info & Contact */}
          <div className="space-y-6">
            {/* Owner Information */}
            <div className="bg-white rounded-xl shadow-soft p-6 sticky top-24">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Property Owner</h3>

              <div className="flex items-center space-x-4 mb-6">
                <img
                  src={property.owner.photo}
                  alt={property.owner.name}
                  className="w-16 h-16 rounded-full object-cover"
                />
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="font-semibold text-gray-900">{property.owner.name}</h4>
                    {property.owner.verified && (
                      <CheckCircleIcon className="w-5 h-5 text-secondary-500" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <StarIcon
                          key={i}
                          className={`w-4 h-4 ${
                            i < Math.floor(property.owner.rating)
                              ? 'text-yellow-400'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">
                      {property.owner.rating} ({property.owner.totalProperties} properties)
                    </span>
                  </div>
                </div>
              </div>

              {/* Contact Buttons */}
              <div className="space-y-3">
                <button className="btn-primary w-full flex items-center justify-center space-x-2">
                  <PhoneIcon className="w-5 h-5" />
                  <span>Call Now</span>
                </button>

                <button className="btn-secondary w-full flex items-center justify-center space-x-2">
                  <ChatBubbleLeftRightIcon className="w-5 h-5" />
                  <span>WhatsApp</span>
                </button>

                <button
                  onClick={() => setShowContactModal(true)}
                  className="btn-outline w-full flex items-center justify-center space-x-2"
                >
                  <EnvelopeIcon className="w-5 h-5" />
                  <span>Send Message</span>
                </button>
              </div>

              {/* Owner Stats */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-primary-500">{property.owner.totalProperties}</div>
                    <div className="text-sm text-gray-600">Properties</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-secondary-500">{property.owner.rating}</div>
                    <div className="text-sm text-gray-600">Rating</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-soft p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full text-left px-4 py-3 rounded-lg border border-gray-200 hover:border-primary-500 hover:bg-primary-50 transition-all duration-300">
                  <div className="flex items-center space-x-3">
                    <CalendarIcon className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">Schedule Viewing</span>
                  </div>
                </button>

                <button className="w-full text-left px-4 py-3 rounded-lg border border-gray-200 hover:border-primary-500 hover:bg-primary-50 transition-all duration-300">
                  <div className="flex items-center space-x-3">
                    <ShareIcon className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">Share Property</span>
                  </div>
                </button>

                <button className="w-full text-left px-4 py-3 rounded-lg border border-gray-200 hover:border-primary-500 hover:bg-primary-50 transition-all duration-300">
                  <div className="flex items-center space-x-3">
                    <PhotoIcon className="w-5 h-5 text-gray-400" />
                    <span className="text-gray-700">Request More Photos</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Similar Properties */}
        <div className="mt-12">
          <div className="bg-white rounded-xl shadow-soft p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Similar Properties</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Similar property cards would go here */}
              {[1, 2, 3].map((item) => (
                <div key={item} className="card card-hover">
                  <div className="relative overflow-hidden rounded-t-xl">
                    <img
                      src={`https://images.unsplash.com/photo-160060${item}542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80`}
                      alt={`Similar Property ${item}`}
                      className="w-full h-48 object-cover transform group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                        For Sale
                      </span>
                    </div>
                    <div className="absolute bottom-4 right-4">
                      <div className="text-lg font-bold text-white bg-black/50 px-3 py-1 rounded-lg backdrop-blur-sm">
                        ${150 + item * 50}k
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-2">
                      Beautiful Property in Kabul {item}
                    </h3>
                    <div className="flex items-center text-gray-600 mb-3">
                      <MapPinIcon className="w-4 h-4 mr-1" />
                      <span className="text-sm">Kabul, Afghanistan</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                      <div className="flex items-center">
                        <HomeIcon className="w-4 h-4 mr-1" />
                        <span>{2 + item} Beds</span>
                      </div>
                      <div className="flex items-center">
                        <BuildingOfficeIcon className="w-4 h-4 mr-1" />
                        <span>{1 + item} Baths</span>
                      </div>
                      <span>{1200 + item * 300} sq ft</span>
                    </div>
                    <Link
                      to={`/property/${item + 10}`}
                      className="btn-outline w-full text-center block"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Contact Modal */}
      <ContactModal
        isOpen={showContactModal}
        onClose={() => setShowContactModal(false)}
        property={{
          id: property.id,
          title: property.title,
          image: property.images[0],
          price: property.price
        }}
        owner={property.owner}
      />
    </div>
  );
};

export default PropertyDetailPage;
