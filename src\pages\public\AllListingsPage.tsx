import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  MapPinIcon,
  HomeIcon,
  BuildingOfficeIcon,
  CurrencyDollarIcon,
  AdjustmentsHorizontalIcon,
  XMarkIcon,
  ChevronDownIcon,
  HeartIcon,
  ShareIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

// Import our new components
import SearchBar from '../../components/common/SearchBar';
import SearchResults from '../../components/common/SearchResults';
import PropertyFilters from '../../components/property/PropertyFilters';
import MapView from '../../components/map/MapView';
import { useLanguage } from '../../contexts/LanguageContext';
import { usePropertySearch, useFavorites } from '../../hooks/usePropertySearch';

interface Property {
  id: number;
  title: string;
  location: string;
  price: string;
  image: string;
  beds: number;
  baths: number;
  area: string;
  type: string;
  category: string;
  featured: boolean;
  views: number;
  saved: boolean;
  yearBuilt?: number;
  parking?: number;
  garden?: boolean;
  coordinates: {
    lat: number;
    lng: number;
  };
}

const AllListingsPage: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { t, isRTL } = useLanguage();

  // Use the property search hook
  const {
    properties,
    loading,
    totalCount,
    currentPage,
    totalPages,
    hasNext,
    hasPrev,
    filters,
    setFilters,
    updateFilter,
    clearFilters,
    search,
    loadMore
  } = usePropertySearch();

  // Use the favorites hook
  const { toggleFavorite } = useFavorites();

  // Event handlers
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    search(query);
  };

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters);
  };

  const handleSortChange = (sortOption: string) => {
    updateFilter('sortBy', sortOption);
  };

  const handleViewModeChange = (mode: 'grid' | 'list' | 'map') => {
    setViewMode(mode);
  };

  const handlePageChange = (page: number) => {
    updateFilter('page', page);
  };

  const handleToggleFavorite = async (propertyId: string, isFavorited: boolean) => {
    await toggleFavorite(propertyId, isFavorited);
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Page Header */}
      <div className="bg-white shadow-soft">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
              {t('listings.allProperties')}
            </h1>
            <p className="text-lg text-gray-600">
              {t('listings.findPerfectHome')}
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <SearchBar
              placeholder={t('search.placeholder')}
              onSearch={handleSearch}
              size="lg"
              variant="hero"
            />
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Results */}
        <SearchResults
          properties={properties}
          loading={loading}
          totalCount={totalCount}
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={handlePageChange}
          onViewModeChange={handleViewModeChange}
          onSortChange={handleSortChange}
          onFilterToggle={() => setShowFilters(true)}
          viewMode={viewMode}
          sortBy={filters.sortBy}
        />

        {/* Property Filters Modal */}
        <PropertyFilters
          isOpen={showFilters}
          onClose={() => setShowFilters(false)}
          onApplyFilters={handleFilterChange}
          onClearFilters={clearFilters}
          initialFilters={filters}
        />
      </div>
    </div>
  );
};

export default AllListingsPage;
