import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  UserIcon,
  PencilIcon,
  CameraIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  StarIcon,
  CheckBadgeIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import Button from '../common/Button';
import Input from '../common/Input';
import Modal from '../common/Modal';
import { useLanguage } from '../../contexts/LanguageContext';

interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar: string;
  location: {
    city: string;
    province: string;
  };
  bio: string;
  joinDate: string;
  verified: boolean;
  rating: number;
  totalReviews: number;
  totalListings: number;
  role: 'user' | 'agent' | 'admin';
  socialLinks?: {
    facebook?: string;
    twitter?: string;
    linkedin?: string;
  };
}

interface UserProfileProps {
  user: User;
  isOwnProfile?: boolean;
  onUpdateProfile?: (data: Partial<User>) => void;
  onSendMessage?: () => void;
  className?: string;
}

const UserProfile: React.FC<UserProfileProps> = ({
  user,
  isOwnProfile = false,
  onUpdateProfile,
  onSendMessage,
  className = ''
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [editData, setEditData] = useState({
    name: user.name,
    bio: user.bio,
    phone: user.phone,
    location: user.location
  });
  const { t, isRTL } = useLanguage();
  
  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setIsUploadingAvatar(true);
    // Simulate upload
    setTimeout(() => {
      setIsUploadingAvatar(false);
      // In real app, update avatar URL
    }, 2000);
  };
  
  const handleSaveProfile = () => {
    onUpdateProfile?.(editData);
    setIsEditModalOpen(false);
  };
  
  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span key={index}>
        {index < Math.floor(rating) ? (
          <StarSolidIcon className="w-4 h-4 text-yellow-400" />
        ) : (
          <StarIcon className="w-4 h-4 text-gray-300" />
        )}
      </span>
    ));
  };
  
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'agent':
        return 'bg-blue-100 text-blue-800';
      case 'admin':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'agent':
        return t('user.agent');
      case 'admin':
        return t('user.admin');
      default:
        return t('user.member');
    }
  };
  
  return (
    <>
      <div className={`bg-white rounded-2xl shadow-xl overflow-hidden ${className}`}>
        {/* Cover Image */}
        <div className="h-32 bg-gradient-primary relative">
          <div className="absolute inset-0 bg-black bg-opacity-20"></div>
        </div>
        
        {/* Profile Content */}
        <div className="relative px-6 pb-6">
          {/* Avatar */}
          <div className="flex items-start justify-between -mt-16 mb-4">
            <div className="relative">
              <motion.div
                whileHover={{ scale: 1.05 }}
                className="relative"
              >
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-32 h-32 rounded-full border-4 border-white shadow-lg object-cover"
                />
                {user.verified && (
                  <div className="absolute bottom-2 right-2 bg-blue-500 rounded-full p-1">
                    <CheckBadgeIcon className="w-6 h-6 text-white" />
                  </div>
                )}
                {isOwnProfile && (
                  <label className="absolute bottom-0 right-0 bg-primary-600 rounded-full p-2 cursor-pointer hover:bg-primary-700 transition-colors">
                    <CameraIcon className="w-4 h-4 text-white" />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleAvatarUpload}
                      className="hidden"
                    />
                  </label>
                )}
              </motion.div>
              {isUploadingAvatar && (
                <div className="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </div>
              )}
            </div>
            
            {/* Action Buttons */}
            <div className={`flex items-center space-x-3 mt-16 ${isRTL ? 'space-x-reverse' : ''}`}>
              {isOwnProfile ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditModalOpen(true)}
                  icon={<PencilIcon className="w-4 h-4" />}
                >
                  {t('user.editProfile')}
                </Button>
              ) : (
                <Button
                  size="sm"
                  onClick={onSendMessage}
                >
                  {t('user.sendMessage')}
                </Button>
              )}
            </div>
          </div>
          
          {/* User Info */}
          <div className="space-y-4">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">{user.name}</h1>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                  {getRoleLabel(user.role)}
                </span>
              </div>
              
              {user.bio && (
                <p className="text-gray-600 leading-relaxed">{user.bio}</p>
              )}
            </div>
            
            {/* Contact Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3 text-gray-600">
                <MapPinIcon className="w-5 h-5 flex-shrink-0" />
                <span className="text-sm">
                  {user.location.city}, {user.location.province}
                </span>
              </div>
              
              <div className="flex items-center space-x-3 text-gray-600">
                <CalendarIcon className="w-5 h-5 flex-shrink-0" />
                <span className="text-sm">
                  {t('user.memberSince')} {new Date(user.joinDate).getFullYear()}
                </span>
              </div>
              
              <div className="flex items-center space-x-3 text-gray-600">
                <EnvelopeIcon className="w-5 h-5 flex-shrink-0" />
                <span className="text-sm">{user.email}</span>
              </div>
              
              <div className="flex items-center space-x-3 text-gray-600">
                <PhoneIcon className="w-5 h-5 flex-shrink-0" />
                <span className="text-sm">{user.phone}</span>
              </div>
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-200">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  {renderStars(user.rating)}
                </div>
                <p className="text-sm text-gray-600">
                  {user.rating.toFixed(1)} ({user.totalReviews} {t('user.reviews')})
                </p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <BuildingOfficeIcon className="w-5 h-5 text-primary-600" />
                </div>
                <p className="text-lg font-semibold text-gray-900">{user.totalListings}</p>
                <p className="text-sm text-gray-600">{t('user.listings')}</p>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center mb-1">
                  <UserIcon className="w-5 h-5 text-primary-600" />
                </div>
                <p className="text-lg font-semibold text-gray-900">
                  {new Date().getFullYear() - new Date(user.joinDate).getFullYear()}+
                </p>
                <p className="text-sm text-gray-600">{t('user.yearsActive')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Edit Profile Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title={t('user.editProfile')}
        size="lg"
      >
        <div className="space-y-6">
          <Input
            label={t('user.fullName')}
            value={editData.name}
            onChange={(e) => setEditData({ ...editData, name: e.target.value })}
          />
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('user.bio')}
            </label>
            <textarea
              value={editData.bio}
              onChange={(e) => setEditData({ ...editData, bio: e.target.value })}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder={t('user.bioPlaceholder')}
            />
          </div>
          
          <Input
            label={t('user.phoneNumber')}
            value={editData.phone}
            onChange={(e) => setEditData({ ...editData, phone: e.target.value })}
          />
          
          <div className="grid grid-cols-2 gap-4">
            <Input
              label={t('user.city')}
              value={editData.location.city}
              onChange={(e) => setEditData({ 
                ...editData, 
                location: { ...editData.location, city: e.target.value }
              })}
            />
            <Input
              label={t('user.province')}
              value={editData.location.province}
              onChange={(e) => setEditData({ 
                ...editData, 
                location: { ...editData.location, province: e.target.value }
              })}
            />
          </div>
        </div>
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="outline"
            onClick={() => setIsEditModalOpen(false)}
          >
            {t('common.cancel')}
          </Button>
          <Button onClick={handleSaveProfile}>
            {t('common.save')}
          </Button>
        </div>
      </Modal>
    </>
  );
};

export default UserProfile;
