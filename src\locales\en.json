{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "close": "Close", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "clear": "Clear", "apply": "Apply", "submit": "Submit", "back": "Back", "home": "Home", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "name": "Name", "phone": "Phone", "address": "Address", "city": "City", "province": "Province", "country": "Country", "price": "Price", "area": "Area", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "features": "Features", "description": "Description", "images": "Images", "contact": "Contact", "share": "Share", "favorite": "Favorite", "featured": "Featured", "new": "New", "popular": "Popular", "recent": "Recent", "all": "All", "none": "None", "yes": "Yes", "no": "No", "active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "sold": "Sold", "available": "Available", "unavailable": "Unavailable"}, "navigation": {"home": "Home", "listings": "Listings", "about": "About", "contact": "Contact", "login": "<PERSON><PERSON>", "register": "Register", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "logout": "Logout", "postProperty": "Post Property", "myListings": "My Listings", "favorites": "Favorites", "messages": "Messages"}, "search": {"placeholder": "Search properties, locations...", "button": "Search", "results": "Search Results", "noResults": "No properties found", "noResultsDescription": "Try adjusting your search criteria or browse all properties", "page": "Page", "of": "of", "filters": "Filters", "suggestions": "Suggestions", "recentSearches": "Recent Searches"}, "filters": {"title": "Filters", "propertyType": "Property Type", "category": "Category", "location": "Location", "priceRange": "Price Range", "minPrice": "<PERSON>", "maxPrice": "Max Price", "areaRange": "Area Range", "minArea": "Min Area", "maxArea": "Max Area", "bedrooms": "Bedrooms", "bathrooms": "Bathrooms", "features": "Features", "rooms": "Rooms", "province": "Province", "selectProvince": "Select Province", "applyFilters": "Apply Filters", "clearAll": "Clear All"}, "sort": {"newest": "Newest First", "oldest": "Oldest First", "priceLow": "Price: Low to High", "priceHigh": "Price: High to Low", "areaLarge": "Area: Large to Small", "areaSmall": "Area: Small to Large"}, "propertyTypes": {"residential": "Residential", "commercial": "Commercial", "land": "Land"}, "categories": {"house": "House", "apartment": "Apartment", "villa": "Villa", "townhouse": "Townhouse", "office": "Office", "shop": "Shop", "warehouse": "Warehouse", "restaurant": "Restaurant", "residentialLand": "Residential Land", "commercialLand": "Commercial Land", "agricultural": "Agricultural Land"}, "listings": {"allProperties": "All Properties", "findPerfectHome": "Find your perfect home in Afghanistan", "myListings": "My Listings", "manageProperties": "Manage your property listings", "addProperty": "Add Property", "addFirstProperty": "Add Your First Property", "searchPlaceholder": "Search your properties...", "allStatus": "All Status", "active": "Active", "pending": "Pending", "sold": "Sold", "expired": "Expired", "featured": "Featured", "view": "View", "edit": "Edit", "delete": "Delete", "deleteProperty": "Delete Property", "deleteConfirmation": "Are you sure you want to delete this property? This action cannot be undone.", "noProperties": "No properties found", "noPropertiesDesc": "You haven't posted any properties yet. Start by adding your first property.", "noResults": "No matching properties", "noResultsDesc": "Try adjusting your search or filter criteria.", "loading": "Loading properties...", "listings": "listings", "views": "views"}, "propertyForm": {"createTitle": "Post New Property", "editTitle": "Edit Property", "subtitle": "Fill in the details to list your property", "basicInfo": "Basic Information", "details": "Property Details", "location": "Location", "pricing": "Pricing", "images": "Images", "title": "Property Title", "titlePlaceholder": "Enter a descriptive title for your property", "propertyType": "Property Type", "selectType": "Select Property Type", "category": "Category", "selectCategory": "Select Category", "description": "Description", "descriptionPlaceholder": "Describe your property in detail...", "create": "Create Property", "update": "Update Property"}, "dashboard": {"welcome": "Welcome back!", "welcomeMessage": "Here's an overview of your real estate activities", "totalListings": "Total Listings", "totalViews": "Total Views", "inquiries": "Inquiries", "favorites": "Favorites", "today": "today", "thisMonth": "this month", "quickActions": "Quick Actions", "postProperty": "Post Property", "postPropertyDesc": "List a new property for sale or rent", "viewListings": "View Listings", "viewListingsDesc": "Manage your property listings", "messages": "Messages", "messagesDesc": "Check your messages and inquiries", "favoritesDesc": "View your saved properties", "recentActivity": "Recent Activity", "viewAll": "View All", "noActivity": "No recent activity"}, "user": {"profile": "Profile", "editProfile": "Edit Profile", "fullName": "Full Name", "phoneNumber": "Phone Number", "bio": "Bio", "bioPlaceholder": "Tell us about yourself...", "city": "City", "province": "Province", "memberSince": "Member since", "reviews": "reviews", "listings": "listings", "yearsActive": "years active", "sendMessage": "Send Message", "agent": "Agent", "admin": "Admin", "member": "Member"}, "settings": {"title": "Settings", "profile": "Profile", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "language": "Language", "changeAvatar": "Change Avatar", "fullName": "Full Name", "phoneNumber": "Phone Number", "emailAddress": "Email Address", "saveChanges": "Save Changes", "notificationPreferences": "Notification Preferences", "emailNotifications": "Email Notifications", "emailNotificationsDesc": "Receive notifications via email", "smsNotifications": "SMS Notifications", "smsNotificationsDesc": "Receive notifications via SMS", "pushNotifications": "Push Notifications", "pushNotificationsDesc": "Receive push notifications", "marketingNotifications": "Marketing", "marketingNotificationsDesc": "Receive marketing and promotional emails", "privacySettings": "Privacy Settings", "profileVisible": "Profile Visibility", "profileVisibleDesc": "Make your profile visible to other users", "showEmail": "Show Email", "showEmailDesc": "Display your email address on your profile", "showPhone": "Show Phone", "showPhoneDesc": "Display your phone number on your profile", "allowMessages": "Allow Messages", "allowMessagesDesc": "Allow other users to send you messages", "securitySettings": "Security Settings", "twoFactorAuth": "Two-Factor Authentication", "twoFactorAuthDesc": "Add an extra layer of security to your account", "enable": "Enable", "disable": "Disable", "changePassword": "Change Password", "changePasswordDesc": "Update your account password", "deleteAccount": "Delete Account", "deleteAccountDesc": "Permanently delete your account and all data", "deleteAccountWarning": "This action cannot be undone. All your data will be permanently deleted.", "deleteAccountConfirmation": "Please type DELETE to confirm account deletion.", "typeDeleteToConfirm": "Type DELETE to confirm", "languagePreferences": "Language Preferences"}, "admin": {"welcome": "Admin Dashboard", "welcomeMessage": "Manage your real estate platform", "totalUsers": "Total Users", "totalProperties": "Total Properties", "totalViews": "Total Views", "revenue": "Revenue", "thisMonth": "this month", "quickActions": "Quick Actions", "manageUsers": "Manage Users", "manageUsersDesc": "View and manage user accounts", "manageProperties": "Manage Properties", "managePropertiesDesc": "Review and manage property listings", "analytics": "Analytics", "analyticsDesc": "View platform statistics and reports", "reports": "reports", "reportsDesc": "Handle user reports and issues", "recentActivity": "Recent Activity", "viewAll": "View All", "allRoles": "All Roles", "user": "User", "agent": "Agent", "admin": "Admin", "allStatuses": "All Statuses", "active": "Active", "pending": "Pending", "suspended": "Suspended", "searchUsers": "Search users...", "usersFound": "users found", "loadingUsers": "Loading users...", "userDetails": "User Details", "deleteUser": "Delete User", "deleteUserConfirmation": "Are you sure you want to delete this user? This action cannot be undone.", "noUsersFound": "No users found", "noUsersFoundDesc": "No users match your search criteria.", "manageListings": "Manage Listings", "manageListingsDesc": "Review and manage property listings", "allTypes": "All Types", "residential": "Residential", "commercial": "Commercial", "land": "Land", "searchProperties": "Search properties...", "propertiesFound": "properties found", "loadingProperties": "Loading properties...", "propertyDetails": "Property Details", "deleteProperty": "Delete Property", "deletePropertyConfirmation": "Are you sure you want to delete this property? This action cannot be undone.", "noPropertiesFound": "No properties found", "noPropertiesFoundDesc": "No properties match your search criteria.", "owner": "Owner", "performance": "Performance", "actions": "Actions", "approve": "Approve", "reject": "Reject", "feature": "Feature", "unfeature": "Unfeature", "views": "views", "inquiries": "inquiries", "joined": "Joined", "totalListings": "Total Listings", "completed": "Completed", "requires_attention": "Requires Attention"}, "gallery": {"noImages": "No images available"}, "time": {"justNow": "Just now", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago"}, "pagination": {"previous": "Previous", "next": "Next"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "minLength": "Must be at least {{min}} characters", "maxLength": "Must be no more than {{max}} characters", "numeric": "Must be a number", "positive": "Must be a positive number"}}