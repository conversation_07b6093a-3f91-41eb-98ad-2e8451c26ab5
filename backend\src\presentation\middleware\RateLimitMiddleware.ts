/**
 * Professional Rate Limiting Middleware
 * Implements various rate limiting strategies for API protection
 */

import { Request, Response, NextFunction } from 'express';
import { RateLimitError } from '../../shared/errors/ApplicationErrors';

export interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  max: number; // Maximum number of requests per window
  message?: string; // Custom error message
  keyGenerator?: (req: Request) => string; // Custom key generator
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
  onLimitReached?: (req: Request, res: Response) => void; // Callback when limit is reached
}

interface RateLimitStore {
  get(key: string): Promise<{ count: number; resetTime: number } | null>;
  set(key: string, value: { count: number; resetTime: number }, ttl: number): Promise<void>;
  increment(key: string): Promise<{ count: number; resetTime: number }>;
  reset(key: string): Promise<void>;
}

/**
 * In-memory rate limit store (for development/single instance)
 */
class MemoryStore implements RateLimitStore {
  private store = new Map<string, { count: number; resetTime: number }>();

  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    const data = this.store.get(key);
    if (!data) return null;
    
    // Check if expired
    if (Date.now() > data.resetTime) {
      this.store.delete(key);
      return null;
    }
    
    return data;
  }

  async set(key: string, value: { count: number; resetTime: number }, ttl: number): Promise<void> {
    this.store.set(key, value);
    
    // Auto cleanup expired entries
    setTimeout(() => {
      this.store.delete(key);
    }, ttl);
  }

  async increment(key: string): Promise<{ count: number; resetTime: number }> {
    const existing = await this.get(key);
    if (existing) {
      existing.count++;
      return existing;
    }
    
    const newData = { count: 1, resetTime: Date.now() + 60000 }; // Default 1 minute
    this.store.set(key, newData);
    return newData;
  }

  async reset(key: string): Promise<void> {
    this.store.delete(key);
  }
}

/**
 * Redis rate limit store (for production/distributed systems)
 */
class RedisStore implements RateLimitStore {
  // Implementation would use Redis client
  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    // Redis implementation
    return null;
  }

  async set(key: string, value: { count: number; resetTime: number }, ttl: number): Promise<void> {
    // Redis implementation
  }

  async increment(key: string): Promise<{ count: number; resetTime: number }> {
    // Redis implementation with atomic increment
    return { count: 1, resetTime: Date.now() + 60000 };
  }

  async reset(key: string): Promise<void> {
    // Redis implementation
  }
}

/**
 * Rate limiting middleware factory
 */
export const rateLimitMiddleware = (options: RateLimitOptions) => {
  const store = process.env.REDIS_URL ? new RedisStore() : new MemoryStore();
  
  const {
    windowMs,
    max,
    message = 'Too many requests, please try again later',
    keyGenerator = defaultKeyGenerator,
    skipSuccessfulRequests = false,
    skipFailedRequests = false,
    onLimitReached
  } = options;

  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const key = keyGenerator(req);
      const now = Date.now();
      const resetTime = now + windowMs;

      // Get current count
      let data = await store.get(key);
      
      if (!data) {
        data = { count: 0, resetTime };
      }

      // Check if window has expired
      if (now > data.resetTime) {
        data = { count: 0, resetTime };
      }

      // Increment count
      data.count++;

      // Check if limit exceeded
      if (data.count > max) {
        if (onLimitReached) {
          onLimitReached(req, res);
        }

        // Set rate limit headers
        res.set({
          'X-RateLimit-Limit': max.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil(data.resetTime / 1000).toString(),
          'Retry-After': Math.ceil((data.resetTime - now) / 1000).toString()
        });

        throw new RateLimitError(
          message,
          Math.ceil((data.resetTime - now) / 1000),
          max,
          0
        );
      }

      // Update store
      await store.set(key, data, windowMs);

      // Set rate limit headers
      res.set({
        'X-RateLimit-Limit': max.toString(),
        'X-RateLimit-Remaining': (max - data.count).toString(),
        'X-RateLimit-Reset': Math.ceil(data.resetTime / 1000).toString()
      });

      // Handle response counting
      if (!skipSuccessfulRequests || !skipFailedRequests) {
        const originalSend = res.send;
        res.send = function(body) {
          const statusCode = res.statusCode;
          
          // Decrement count if we should skip this request
          if ((skipSuccessfulRequests && statusCode < 400) ||
              (skipFailedRequests && statusCode >= 400)) {
            store.get(key).then(currentData => {
              if (currentData && currentData.count > 0) {
                currentData.count--;
                store.set(key, currentData, windowMs);
              }
            });
          }
          
          return originalSend.call(this, body);
        };
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Default key generator (IP-based)
 */
function defaultKeyGenerator(req: Request): string {
  return `rate_limit:${req.ip}`;
}

/**
 * User-based key generator
 */
export function userKeyGenerator(req: Request): string {
  const userId = req.user?.id || req.ip;
  return `rate_limit:user:${userId}`;
}

/**
 * Endpoint-based key generator
 */
export function endpointKeyGenerator(req: Request): string {
  const userId = req.user?.id || req.ip;
  const endpoint = `${req.method}:${req.route?.path || req.path}`;
  return `rate_limit:${userId}:${endpoint}`;
}

/**
 * Sliding window rate limiter
 */
export const slidingWindowRateLimit = (options: RateLimitOptions) => {
  const store = new Map<string, number[]>();
  
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const key = options.keyGenerator ? options.keyGenerator(req) : defaultKeyGenerator(req);
      const now = Date.now();
      const windowStart = now - options.windowMs;

      // Get existing timestamps
      let timestamps = store.get(key) || [];
      
      // Remove old timestamps
      timestamps = timestamps.filter(timestamp => timestamp > windowStart);
      
      // Check if limit exceeded
      if (timestamps.length >= options.max) {
        const oldestTimestamp = Math.min(...timestamps);
        const retryAfter = Math.ceil((oldestTimestamp + options.windowMs - now) / 1000);
        
        res.set({
          'X-RateLimit-Limit': options.max.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil((oldestTimestamp + options.windowMs) / 1000).toString(),
          'Retry-After': retryAfter.toString()
        });

        throw new RateLimitError(
          options.message || 'Too many requests, please try again later',
          retryAfter,
          options.max,
          0
        );
      }

      // Add current timestamp
      timestamps.push(now);
      store.set(key, timestamps);

      // Set headers
      res.set({
        'X-RateLimit-Limit': options.max.toString(),
        'X-RateLimit-Remaining': (options.max - timestamps.length).toString(),
        'X-RateLimit-Reset': Math.ceil((now + options.windowMs) / 1000).toString()
      });

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Adaptive rate limiter (adjusts based on server load)
 */
export const adaptiveRateLimit = (baseOptions: RateLimitOptions) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Get server load metrics
    const cpuUsage = process.cpuUsage();
    const memoryUsage = process.memoryUsage();
    
    // Calculate load factor (0-1, where 1 is high load)
    const loadFactor = Math.min(
      (memoryUsage.heapUsed / memoryUsage.heapTotal),
      1
    );

    // Adjust rate limit based on load
    const adjustedMax = Math.floor(baseOptions.max * (1 - loadFactor * 0.5));
    
    const adaptedOptions = {
      ...baseOptions,
      max: Math.max(adjustedMax, 1) // Ensure at least 1 request is allowed
    };

    return rateLimitMiddleware(adaptedOptions)(req, res, next);
  };
};

/**
 * Burst rate limiter (allows short bursts but limits sustained usage)
 */
export const burstRateLimit = (
  burstOptions: RateLimitOptions,
  sustainedOptions: RateLimitOptions
) => {
  const burstLimiter = rateLimitMiddleware(burstOptions);
  const sustainedLimiter = rateLimitMiddleware(sustainedOptions);

  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    // Apply burst limit first
    burstLimiter(req, res, (burstError) => {
      if (burstError) {
        return next(burstError);
      }

      // Then apply sustained limit
      sustainedLimiter(req, res, next);
    });
  };
};

/**
 * Rate limit bypass for trusted IPs
 */
export const trustedIpBypass = (trustedIps: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (trustedIps.includes(req.ip)) {
      return next();
    }
    next();
  };
};
