import { Router } from 'express';
import {
  getProperties,
  getProperty,
  createProperty,
  updateProperty,
  deleteProperty,
  getUserProperties,
  searchProperties
} from '../controllers/propertyController';
import { authenticate, optionalAuth } from '../middleware/auth';
// import {
//   validate,
//   propertyValidation,
//   idValidation,
//   paginationValidation
// } from '../middleware/validation';

const router = Router();

// Public routes
router.get('/search', searchProperties);
router.get('/', optionalAuth, getProperties);
router.get('/:id', optionalAuth, getProperty);

// Protected routes
router.use(authenticate); // All routes below require authentication

router.post('/', createProperty);
router.put('/:id', updateProperty);
router.delete('/:id', deleteProperty);
router.get('/user/my-properties', getUserProperties);

export default router;
